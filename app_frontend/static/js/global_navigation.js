/**
 * Global Navigation System with Authentication
 * Handles all navigation with automatic authentication checks
 * Version: 1.0
 */

console.log('🌐 Loading Global Navigation System v1.0');

class GlobalNavigation {
    constructor() {
        // Get API URLs from Django settings
        this.API_BASE_URL = window.API_BASE_URL || 'http://192.168.50.180:8001';
        this.LOCAL_BASE_URL = window.LOCAL_BASE_URL || 'http://192.168.50.180:8000';
        
        // Storage keys
        this.RETURN_URL_KEY = 'navigation_return_url';
        
        console.log('🌐 GlobalNavigation initialized:', {
            API_BASE_URL: this.API_BASE_URL,
            LOCAL_BASE_URL: this.LOCAL_BASE_URL
        });
        
        // Auto-setup when DOM is ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.initialize());
        } else {
            this.initialize();
        }
    }
    
    /**
     * Initialize the navigation system
     */
    initialize() {
        console.log('🌐 Initializing global navigation...');
        this.setupNavigationHandlers();
        this.handlePostLoginRedirect();
        console.log('🌐 Global navigation setup complete');
    }
    
    /**
     * Check if user is authenticated
     */
    isAuthenticated() {
        const token = localStorage.getItem('access_token');
        if (!token) {
            return false;
        }
        
        try {
            // Basic JWT token validation (check if not expired)
            const payload = JSON.parse(atob(token.split('.')[1]));
            const now = Date.now() / 1000;
            return payload.exp > now;
        } catch (error) {
            console.warn('🌐 Invalid token format:', error);
            return false;
        }
    }
    
    /**
     * Save current URL for post-login redirect
     */
    saveReturnUrl(url = null) {
        const returnUrl = url || window.location.href;
        sessionStorage.setItem(this.RETURN_URL_KEY, returnUrl);
        console.log('🌐 Saved return URL:', returnUrl);
    }
    
    /**
     * Get saved return URL
     */
    getReturnUrl() {
        return sessionStorage.getItem(this.RETURN_URL_KEY);
    }
    
    /**
     * Clear saved return URL
     */
    clearReturnUrl() {
        sessionStorage.removeItem(this.RETURN_URL_KEY);
    }
    
    /**
     * Navigate to URL with optional authentication check
     */
    navigateTo(url, requiresAuth = false) {
        console.log('🌐 Navigation request:', { url, requiresAuth, isAuthenticated: this.isAuthenticated() });
        
        if (requiresAuth && !this.isAuthenticated()) {
            console.log('🌐 Authentication required, redirecting to login');
            this.saveReturnUrl(url);
            window.location.href = '/login/';
            return;
        }
        
        console.log('🌐 Navigating to:', url);
        window.location.href = url;
    }
    
    /**
     * Go to login page and save current page for return
     */
    goToLogin() {
        console.log('🌐 Going to login page');
        this.saveReturnUrl();
        window.location.href = '/login/';
    }
    
    /**
     * Handle post-login redirect
     */
    handlePostLoginRedirect() {
        // Only handle redirect if we're on a page that indicates successful login
        const currentPath = window.location.pathname;
        const isLoginSuccess = this.isAuthenticated() && (
            currentPath === '/login/' || 
            currentPath === '/' ||
            sessionStorage.getItem('login_success') === 'true'
        );
        
        if (isLoginSuccess) {
            const returnUrl = this.getReturnUrl();
            console.log('🌐 Post-login redirect check:', { returnUrl, currentPath });
            
            // Clear login success flag
            sessionStorage.removeItem('login_success');
            
            if (returnUrl && returnUrl !== window.location.href && !returnUrl.includes('/login/')) {
                console.log('🌐 Redirecting to saved URL:', returnUrl);
                this.clearReturnUrl();
                window.location.replace(returnUrl);
                return;
            }
            
            // Clear return URL if we're staying on current page
            this.clearReturnUrl();
        }
    }
    
    /**
     * Setup navigation handlers for all elements
     */
    setupNavigationHandlers() {
        // Handle auth-required links
        document.addEventListener('click', (e) => {
            const element = e.target.closest('[data-auth-required="true"]');
            if (element && element.href) {
                e.preventDefault();
                this.navigateTo(element.href, true);
                return false;
            }
        });
        
        // Handle login links
        document.addEventListener('click', (e) => {
            const element = e.target.closest('[data-login-link="true"], .signin, #global-signin-btn');
            if (element) {
                e.preventDefault();
                this.goToLogin();
                return false;
            }
        });
        
        // Handle regular navigation links
        document.addEventListener('click', (e) => {
            const element = e.target.closest('[data-nav-link="true"]');
            if (element && element.href) {
                e.preventDefault();
                this.navigateTo(element.href, false);
                return false;
            }
        });
        
        console.log('🌐 Navigation event handlers attached');
    }
    
    /**
     * Mark login as successful (to be called after successful login)
     */
    markLoginSuccess() {
        sessionStorage.setItem('login_success', 'true');
        console.log('🌐 Login marked as successful');
    }
}

// Create global instance
window.globalNav = new GlobalNavigation();

// Expose global functions for backward compatibility and direct use
window.navigateTo = (url, requiresAuth = false) => window.globalNav.navigateTo(url, requiresAuth);
window.goToLogin = () => window.globalNav.goToLogin();
window.markLoginSuccess = () => window.globalNav.markLoginSuccess();

console.log('🌐 Global Navigation System loaded and ready');
