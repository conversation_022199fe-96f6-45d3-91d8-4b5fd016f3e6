/**
 * Global Navigation System with Authentication
 * Handles all navigation with automatic authentication checks
 * Version: 2.0 - Complete Rewrite
 */

console.log('🌐 Loading Global Navigation System v2.0');

class GlobalNavigation {
    constructor() {
        // Get API URLs from Django settings
        this.API_BASE_URL = window.API_BASE_URL || 'http://192.168.50.180:8001';
        this.LOCAL_BASE_URL = window.LOCAL_BASE_URL || 'http://192.168.50.180:8000';
        
        // Storage keys
        this.RETURN_URL_KEY = 'navigation_return_url';
        
        console.log('🌐 GlobalNavigation v2.0 initialized:', {
            API_BASE_URL: this.API_BASE_URL,
            LOCAL_BASE_URL: this.LOCAL_BASE_URL
        });
        
        // Auto-setup when DOM is ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.initialize());
        } else {
            this.initialize();
        }
    }
    
    /**
     * Initialize the navigation system
     */
    initialize() {
        console.log('🌐 Initializing Global Navigation System v2.0');
        this.setupNavigationHandlers();
        this.handlePostLoginRedirect();
    }
    
    /**
     * Check if user is authenticated
     */
    isAuthenticated() {
        const token = localStorage.getItem('access_token');
        if (!token) {
            console.log('🌐 No access token found');
            return false;
        }
        
        try {
            // Basic JWT token validation (check if not expired)
            const payload = JSON.parse(atob(token.split('.')[1]));
            const currentTime = Math.floor(Date.now() / 1000);
            const isValid = payload.exp > currentTime;
            console.log('🌐 Token validation:', { isValid, exp: payload.exp, current: currentTime });
            return isValid;
        } catch (error) {
            console.error('🌐 Token validation error:', error);
            return false;
        }
    }
    
    /**
     * Handle post-login redirect
     */
    handlePostLoginRedirect() {
        const returnUrl = this.getReturnUrl();
        if (returnUrl && this.isAuthenticated()) {
            console.log('🌐 Post-login redirect to:', returnUrl);
            this.clearReturnUrl();
            // Avoid infinite redirect loops
            if (returnUrl !== window.location.href && !returnUrl.includes('/login/')) {
                window.location.href = returnUrl;
            }
        }
    }
    
    /**
     * Save current URL for post-login redirect
     */
    saveReturnUrl(url = null) {
        const returnUrl = url || window.location.href;
        // Don't save login page as return URL
        if (returnUrl.includes('/login/')) {
            return;
        }
        sessionStorage.setItem(this.RETURN_URL_KEY, returnUrl);
        console.log('🌐 Saved return URL:', returnUrl);
    }
    
    /**
     * Get saved return URL
     */
    getReturnUrl() {
        return sessionStorage.getItem(this.RETURN_URL_KEY);
    }
    
    /**
     * Clear saved return URL
     */
    clearReturnUrl() {
        sessionStorage.removeItem(this.RETURN_URL_KEY);
        console.log('🌐 Cleared return URL');
    }
    
    /**
     * Navigate to URL with authentication check
     * @param {string} url - Target URL
     * @param {boolean} requiresAuth - Whether authentication is required
     */
    navigateTo(url, requiresAuth = false) {
        console.log('🌐 Navigation request:', { url, requiresAuth, isAuthenticated: this.isAuthenticated() });
        
        if (requiresAuth && !this.isAuthenticated()) {
            console.log('🌐 Authentication required, redirecting to login');
            this.saveReturnUrl(url);
            window.location.href = '/login/';
            return;
        }
        
        console.log('🌐 Navigating to:', url);
        window.location.href = url;
    }
    
    /**
     * Go to login page and save current page for return
     */
    goToLogin() {
        console.log('🌐 Going to login page');
        this.saveReturnUrl();
        window.location.href = '/login/';
    }
    
    /**
     * Handle successful login - redirect to saved URL or home
     */
    handleLoginSuccess() {
        console.log('🌐 Login successful, handling redirect');
        const returnUrl = this.getReturnUrl();
        
        if (returnUrl && returnUrl !== window.location.href && !returnUrl.includes('/login/')) {
            console.log('🌐 Redirecting to saved URL:', returnUrl);
            this.clearReturnUrl();
            window.location.href = returnUrl;
        } else {
            console.log('🌐 No saved URL, redirecting to home');
            window.location.href = '/';
        }
    }
    
    /**
     * Handle logout
     */
    handleLogout() {
        console.log('🌐 Handling logout');
        localStorage.removeItem('access_token');
        localStorage.removeItem('refresh_token');
        localStorage.removeItem('user_info');
        this.clearReturnUrl();
        window.location.href = '/';
    }
    
    /**
     * Setup navigation handlers for all elements
     */
    setupNavigationHandlers() {
        console.log('🌐 Setting up navigation handlers');
        
        // Handle all button clicks and link clicks
        document.addEventListener('click', (e) => {
            this.handleClick(e);
        });
        
        console.log('🌐 Navigation event handlers attached');
    }
    
    /**
     * Handle all click events for navigation
     */
    handleClick(e) {
        const element = e.target.closest('button, a, [onclick], [data-nav-url], [data-auth-required], [data-login-link]');
        if (!element) return;

        // Handle login buttons/links
        if (this.isLoginElement(element)) {
            e.preventDefault();
            this.goToLogin();
            return;
        }

        // Handle elements with data-nav-url attribute
        if (element.hasAttribute('data-nav-url')) {
            e.preventDefault();
            const url = element.getAttribute('data-nav-url');
            const requiresAuth = element.hasAttribute('data-auth-required');
            this.navigateTo(url, requiresAuth);
            return;
        }

        // Handle navigation elements that require authentication
        if (this.requiresAuthentication(element)) {
            e.preventDefault();
            const url = this.getTargetUrl(element);
            if (url) {
                this.navigateTo(url, true);
            }
            return;
        }

        // Handle regular navigation elements
        if (this.isNavigationElement(element)) {
            e.preventDefault();
            const url = this.getTargetUrl(element);
            if (url) {
                this.navigateTo(url, false);
            }
            return;
        }
    }
    
    /**
     * Check if element is a login element
     */
    isLoginElement(element) {
        return element.hasAttribute('data-login-link') ||
               element.classList.contains('signin') ||
               element.id === 'global-signin-btn' ||
               element.textContent.toLowerCase().includes('login') ||
               element.textContent.toLowerCase().includes('sign in');
    }
    
    /**
     * Check if element requires authentication
     */
    requiresAuthentication(element) {
        return element.hasAttribute('data-auth-required') ||
               element.href?.includes('/audio_upload/') ||
               element.href?.includes('/user/') ||
               element.href?.includes('/history/') ||
               element.href?.includes('/profile/') ||
               element.href?.includes('/notifications/') ||
               element.onclick?.toString().includes('audio_upload') ||
               element.onclick?.toString().includes('history') ||
               element.onclick?.toString().includes('profile') ||
               element.onclick?.toString().includes('notifications');
    }
    
    /**
     * Check if element is a navigation element
     */
    isNavigationElement(element) {
        return element.href || 
               element.hasAttribute('data-nav-link') ||
               element.onclick?.toString().includes('location.href') ||
               element.onclick?.toString().includes('window.location');
    }
    
    /**
     * Get target URL from element
     */
    getTargetUrl(element) {
        if (element.href) {
            return element.href;
        }
        
        // Extract URL from onclick handlers
        const onclick = element.onclick?.toString();
        if (onclick) {
            const urlMatch = onclick.match(/location\.href\s*=\s*['"`]([^'"`]+)['"`]/);
            if (urlMatch) {
                return urlMatch[1];
            }
        }
        
        return null;
    }
}

// Create global instance
window.globalNav = new GlobalNavigation();

// Expose global functions for backward compatibility and direct use
window.navigateTo = (url, requiresAuth = false) => window.globalNav.navigateTo(url, requiresAuth);
window.goToLogin = () => window.globalNav.goToLogin();
window.handleLoginSuccess = () => window.globalNav.handleLoginSuccess();

console.log('🌐 Global Navigation System v2.0 loaded and ready');
