<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Transcription Capture</title>
    <style>
        /* Professional Medical Report Style - Specifically for transcription section */
        body {
            font-family: 'Times New Roman', Times, serif;
            background-color: #FFFFFF;
            margin: 0;
            padding: 20px;
            color: #000000;
            -webkit-font-smoothing: auto;
        }

        .transcription-container {
            width: 800px; /* 固定宽度以确保一致的截图 */
            margin: 0 auto;
            background: #FFFFFF;
        }

        .transcription-box {
            background: #F8F8F8;
            border: 1px solid #CCC;
            border-left: 4px solid #000000;
            padding: 1rem 1.5rem;
            margin: 0;
        }

        .transcription-box h4 {
            font-family: 'Times New Roman', Times, serif;
            font-weight: bold;
            margin: 0 0 1rem 0;
            font-size: 1.1rem;
            color: #000;
        }

        .transcription-content {
            margin: 0;
            line-height: 1.7;
            font-style: italic;
            color: #000;
            font-family: 'Times New Roman', Times, serif;
            font-size: 1rem;
        }

        /* Enhanced styles for colored transcription */
        .transcription-content .word-normal {
            color: #333;
        }
        .transcription-content .word-hesitation {
            color: #ff6b35;
            background-color: rgba(255, 107, 53, 0.1);
            padding: 1px 3px;
            border-radius: 3px;
        }
        .transcription-content .word-repetition {
            color: #e74c3c;
            background-color: rgba(231, 76, 60, 0.1);
            padding: 1px 3px;
            border-radius: 3px;
            text-decoration: underline;
        }
        .transcription-content .word-pause {
            color: #9b59b6;
            background-color: rgba(155, 89, 182, 0.1);
            padding: 1px 3px;
            border-radius: 3px;
            font-weight: bold;
        }
        .transcription-content .word-filler {
            color: #f39c12;
            background-color: rgba(243, 156, 18, 0.1);
            padding: 1px 3px;
            border-radius: 3px;
            font-style: normal;
        }
        .transcription-content .word-unclear {
            color: #95a5a6;
            background-color: rgba(149, 165, 166, 0.1);
            padding: 1px 3px;
            border-radius: 3px;
            text-decoration: line-through;
        }
        .transcription-content .word-emphasis {
            color: #27ae60;
            background-color: rgba(39, 174, 96, 0.1);
            padding: 1px 3px;
            border-radius: 3px;
            font-weight: bold;
        }
        .transcription-content .word-error {
            color: #c0392b;
            background-color: rgba(192, 57, 43, 0.1);
            padding: 1px 3px;
            border-radius: 3px;
            border: 1px solid rgba(192, 57, 43, 0.3);
        }
    </style>
</head>
<body>
    <div class="transcription-container">
        <div class="transcription-box" id="transcription-capture-area">
            <h4>Enhanced Transcription with Linguistic Analysis</h4>
            <div id="result-transcription" class="transcription-content">
                <!-- Transcription content will be injected via JavaScript -->
            </div>
        </div>
    </div>

    <script>
        // Get transcription data from URL parameters
        function getUrlParameter(name) {
            name = name.replace(/[\[]/, '\\[').replace(/[\]]/, '\\]');
            var regex = new RegExp('[\\?&]' + name + '=([^&#]*)');
            var results = regex.exec(location.search);
            return results === null ? '' : decodeURIComponent(results[1].replace(/\+/g, ' '));
        }

        // Set transcription content after page loads
        document.addEventListener('DOMContentLoaded', function() {
            const transcriptionData = getUrlParameter('transcription');
            const transcriptionElement = document.getElementById('result-transcription');

            if (transcriptionData) {
                try {
                    // Decode and set transcription content
                    const decodedTranscription = decodeURIComponent(transcriptionData);
                    transcriptionElement.innerHTML = decodedTranscription;
                } catch (e) {
                    console.error('Error decoding transcription data:', e);
                    transcriptionElement.innerHTML = '<span class="word-unclear">Transcription not available</span>';
                }
            } else {
                transcriptionElement.innerHTML = '<span class="word-unclear">Transcription not available</span>';
            }

            // Notify that page is ready for screenshot
            document.body.setAttribute('data-ready', 'true');
        });
    </script>
</body>
</html>
