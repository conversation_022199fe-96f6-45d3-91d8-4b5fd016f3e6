{% load static %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token }}">
    <title>Speech Analysis History - HiSage</title>

    <!-- Performance Optimizations -->
    <link rel="dns-prefetch" href="//cdnjs.cloudflare.com">
    <link rel="preconnect" href="https://cdnjs.cloudflare.com" crossorigin>

    <!-- Font Awesome -->
    <link rel="preload" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" as="style"
          onload="this.onload=null;this.rel='stylesheet'">
    <noscript>
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    </noscript>

    <!-- Stripe -->
    <script src="https://js.stripe.com/v3/"></script>

    <!-- Critical CSS Inline -->
    <style>
        /* Modern CSS Reset */
        *, *::before, *::after {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        /* CSS Custom Properties */
        :root {
            --primary-color: #3b82f6;
            --primary-dark: #1e40af;
            --secondary-color: #64748b;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --error-color: #ef4444;
            --background-color: #f8fafc;
            --surface-color: #ffffff;
            --text-primary: #1e293b;
            --text-secondary: #64748b;
            --text-muted: #94a3b8;
            --border-color: #e2e8f0;
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
            --radius-sm: 0.375rem;
            --radius-md: 0.5rem;
            --radius-lg: 0.75rem;
            --radius-xl: 1rem;
            --transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: linear-gradient(135deg, var(--background-color) 0%, #e2e8f0 100%);
            min-height: 100vh;
            color: var(--text-primary);
            line-height: 1.6;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }

        /* Navigation removed */

        /* Header Section */
        .header {
            background: var(--surface-color);
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-lg);
            padding: 1.5rem;
            margin-bottom: 2rem;
            text-align: center;
            position: relative;
            overflow: hidden;
            border: 1px solid var(--border-color);
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color) 0%, var(--primary-dark) 100%);
        }

        .header h1 {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 0.75rem;
            line-height: 1.2;
        }

        .header p {
            color: var(--text-secondary);
            font-size: 1.125rem;
            max-width: 600px;
            margin: 0 auto 2rem;
        }

        /* Statistics */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1rem;
            margin-top: 2rem;
        }

        .stat-card {
            background: var(--background-color);
            padding: 0.375rem;
            border-radius: var(--radius-lg);
            text-align: center;
            border: 1px solid var(--border-color);
        }

        .stat-value {
            font-size: 2rem;
            font-weight: 700;
            color: var(--primary-color);
            display: block;
            margin-bottom: 0.25rem;
        }

        .stat-label {
            font-size: 0.875rem;
            color: var(--text-secondary);
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        /* Filter Section */
        .filter-section {
            background: var(--surface-color);
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-lg);
            padding: 1rem;
            margin-bottom: 2rem;
            border: 1px solid var(--border-color);
        }

        .filter-grid {
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            gap: 0.75rem;
            align-items: end;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .search-container {
            display: flex;
            gap: 0.25rem;
            align-items: stretch;
        }

        .search-container .form-input {
            flex: 1;
        }

        .search-container .clear-btn {
            flex-shrink: 0;
            white-space: nowrap;
        }

        .form-label {
            font-size: 0.875rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .form-input, .form-select {
            padding: 0.875rem 1rem;
            border: 2px solid var(--border-color);
            border-radius: var(--radius-lg);
            font-size: 1rem;
            transition: var(--transition);
            background: var(--surface-color);
            color: var(--text-primary);
        }

        .form-input:focus, .form-select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .clear-btn {
            background: var(--error-color);
            color: white;
            border: none;
            padding: 0.875rem 0.75rem;
            border-radius: var(--radius-lg);
            font-size: 0.8rem;
            font-weight: 500;
            cursor: pointer;
            transition: var(--transition);
            display: flex;
            align-items: center;
            gap: 0.25rem;
            height: 100%;
            min-width: fit-content;
        }

        .clear-btn:hover {
            background: #dc2626;
            transform: translateY(-1px);
            box-shadow: var(--shadow-md);
        }

        /* History Items */
        .history-section {
            background: var(--surface-color);
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-lg);
            padding: 2rem;
            border: 1px solid var(--border-color);
        }

        .history-grid {
            display: grid;
            gap: 1rem;
        }

        .history-item {
            background: var(--background-color);
            border-radius: var(--radius-lg);
            padding: 1.5rem;
            border: 1px solid var(--border-color);
            transition: var(--transition);
            position: relative;
        }

        .history-item::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 4px;
            background: var(--primary-color);
            border-radius: var(--radius-sm) 0 0 var(--radius-sm);
            transform: scaleY(0);
            transition: var(--transition);
        }

        .history-item:hover {
            background: linear-gradient(135deg, #cbd5e1 0%, #94a3b8 100%);
            transform: translateX(4px);
            box-shadow: var(--shadow-lg);
            color: #1e293b !important;
            border-left: 4px solid var(--primary-color);
        }

        .history-actions {
            display: flex;
            justify-content: flex-end;
            margin-top: 1rem;
            padding-top: 1rem;
            border-top: 1px solid var(--border-color);
        }

        .view-details-btn {
            background: var(--primary-color);
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: var(--radius-md);
            font-size: 0.875rem;
            font-weight: 500;
            cursor: pointer;
            transition: var(--transition);
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .view-details-btn:hover {
            background: var(--primary-dark);
            transform: translateY(-1px);
            box-shadow: var(--shadow-md);
        }

        .view-details-btn.disabled {
            background: #9ca3af;
            color: #6b7280;
            cursor: not-allowed;
            opacity: 0.6;
        }

        .view-details-btn.disabled:hover {
            background: #9ca3af;
            transform: none;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .view-details-btn.disabled i {
            color: #6b7280;
        }

        .view-details-btn:focus {
            outline: none;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
        }

        .history-item:hover::before {
            transform: scaleY(1);
        }

        .history-item:hover .history-title,
        .history-item:hover .history-filename,
        .history-item:hover .history-details,
        .history-item:hover .history-date {
            color: #1e293b !important;
        }

        .history-item:hover .history-details .detail-item {
            color: #475569 !important;
        }

        .history-item:hover .history-filename span {
            color: #1e293b !important;
        }

        .history-item:hover .history-status {
            background: rgba(255, 255, 255, 0.9) !important;
            color: inherit !important;
            border: 1px solid rgba(0, 0, 0, 0.1);
        }

        .history-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 1rem;
        }

        .history-filename {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-weight: 600;
            color: var(--text-primary);
        }

        .history-date {
            font-size: 0.875rem;
            color: var(--text-muted);
        }

        .history-title {
            font-size: 1.125rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
        }

        .history-details {
            display: flex;
            gap: 1rem;
            margin: 0.5rem 0;
            font-size: 0.875rem;
            color: var(--text-secondary);
        }

        .detail-item {
            display: flex;
            align-items: center;
            gap: 0.25rem;
        }

        .history-status {
            display: inline-flex;
            align-items: center;
            gap: 0.25rem;
            padding: 0.25rem 0.75rem;
            border-radius: var(--radius-md);
            font-size: 0.75rem;
            font-weight: 500;
        }

        .status-completed {
            background: #dcfce7;
            color: #166534;
        }

        .status-processing {
            background: #fef3c7;
            color: #92400e;
        }

        .status-failed {
            background: #fee2e2;
            color: #991b1b;
        }

        /* Loading and Empty States */
        .loading {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 3rem;
            color: var(--text-muted);
        }

        .loading i {
            margin-right: 0.75rem;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            from {
                transform: rotate(0deg);
            }
            to {
                transform: rotate(360deg);
            }
        }

        .empty-state {
            text-align: center;
            padding: 3rem;
            color: var(--text-muted);
        }

        .empty-state i {
            font-size: 3rem;
            margin-bottom: 1rem;
            color: var(--text-muted);
        }

        .empty-state h3 {
            font-size: 1.25rem;
            margin-bottom: 0.5rem;
            color: var(--text-secondary);
        }

        /* Responsive Design */
        @media (max-width: 1200px) {
            .filter-grid {
                grid-template-columns: repeat(3, 1fr);
                gap: 0.5rem;
            }
        }

        @media (max-width: 768px) {
            .container {
                padding: 1rem;
            }

            .header {
                padding: 2rem;
            }

            .header h1 {
                font-size: 2rem;
            }

            .filter-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 0.5rem;
            }

            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            /* Back button removed */
        }

        @media (max-width: 480px) {
            .container {
                padding: 0.75rem;
            }

            .header {
                padding: 1.5rem;
            }

            .filter-section,
            .history-section {
                padding: 0.75rem;
            }

            .filter-grid {
                grid-template-columns: 1fr;
                gap: 0.5rem;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }
        }

        /* Accessibility */
        @media (prefers-reduced-motion: reduce) {
            *, *::before, *::after {
                animation-duration: 0.01ms !important;
                animation-iteration-count: 1 !important;
                transition-duration: 0.01ms !important;
            }
        }

        /* Modern Donation Modal */
        .donation-modal {
            display: none;
            position: fixed;
            inset: 0;
            z-index: 50;
            background-color: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(4px);
        }

        .donation-modal.show {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 1rem;
        }

        .donation-modal-content {
            background: white;
            border-radius: 0.75rem;
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
            width: 100%;
            max-width: 56rem;
            max-height: 92vh;
            overflow-y: auto;
            animation: slideIn 0.2s ease-out;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: scale(0.95);
            }
            to {
                opacity: 1;
                transform: scale(1);
            }
        }

        .modal-header {
            padding: 1.5rem;
            border-bottom: 1px solid #e5e7eb;
            display: flex;
            align-items: center;
            justify-content: between;
        }

        .modal-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: #111827;
            flex: 1;
            text-align: center;
        }

        .modal-close {
            background: none;
            border: none;
            color: #6b7280;
            cursor: pointer;
            padding: 0.5rem;
            border-radius: 0.375rem;
            transition: all 0.15s;
        }

        .modal-close:hover {
            background: #f3f4f6;
            color: #374151;
        }

        .modal-body {
            padding: 1.75rem;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
            align-items: start;
            min-height: fit-content;
        }

        .donation-left-panel {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .donation-right-panel {
            display: flex;
            flex-direction: column;
            gap: 1.25rem;
        }

        .form-group {
            margin-bottom: 1.75rem;
        }

        .form-label {
            display: block;
            font-size: 0.875rem;
            font-weight: 500;
            color: #374151;
            margin-bottom: 0.5rem;
        }

        .amount-options {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 0.75rem;
            margin-bottom: 1rem;
        }

        .amount-btn {
            padding: 0.75rem;
            border: 2px solid #d1d5db;
            border-radius: 0.5rem;
            background: white;
            color: #374151;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.15s;
            text-align: center;
        }

        .amount-btn:hover {
            border-color: #3b82f6;
        }

        .amount-btn.selected {
            border-color: #3b82f6;
            background: #3b82f6;
            color: white;
        }

        .custom-amount-wrapper {
            position: relative;
            display: inline-block;
            width: 100%;
        }

        .custom-amount-input {
            width: 100%;
            padding: 0.75rem 0.75rem 0.75rem 2rem;
            border: 2px solid #d1d5db;
            border-radius: 0.5rem;
            font-size: 1rem;
            font-weight: 600;
            text-align: center;
            transition: border-color 0.15s;
        }

        .custom-amount-input:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .custom-amount-dollar {
            position: absolute;
            left: 0.75rem;
            top: 50%;
            transform: translateY(-50%);
            font-size: 1rem;
            font-weight: 600;
            color: #374151;
            pointer-events: none;
            z-index: 1;
        }

        .custom-amount-input::placeholder {
            text-align: center;
            color: #9ca3af;
            font-weight: 400;
        }

        /* Focus state for custom amount */
        .custom-amount-wrapper:focus-within .custom-amount-dollar {
            color: #3b82f6;
        }

        /* Stripe-style Payment Fields */
        .payment-form-container {
            background: white;
            border: 1px solid #e6ebf1;
            border-radius: 6px;
            padding: 0;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            transition: all 0.15s ease;
        }

        .payment-form-container:hover {
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.12);
        }

        .payment-form-container.focused {
            border-color: #635bff;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1), 0 0 0 2px rgba(99, 91, 255, 0.2);
        }

        .card-number-row {
            border-bottom: 1px solid #e6ebf1;
            position: relative;
        }

        .card-details-row {
            display: flex;
        }

        .card-expiry-field {
            flex: 1;
            border-right: 1px solid #e6ebf1;
        }

        .card-cvc-field {
            flex: 1;
        }

        .stripe-field {
            padding: 16px;
            min-height: 20px;
            position: relative;
        }

        .card-number-row .stripe-field {
            padding-right: 180px; /* Make room for multiple card brand icons */
        }

        .field-label {
            position: absolute;
            top: 6px;
            left: 16px;
            font-size: 11px;
            font-weight: 500;
            color: #8898aa;
            text-transform: uppercase;
            letter-spacing: 0.025em;
            pointer-events: none;
        }

        .stripe-element {
            margin-top: 8px;
            font-size: 16px;
            line-height: 20px;
        }

        /* Hide Stripe's built-in card brand icons */
        .stripe-element .InputContainer .Input-icon,
        .stripe-element .CardNumberField-icon,
        .stripe-element .Input--cardNumber .Input-icon,
        .stripe-element [data-testid="card-icon"],
        .stripe-element .p-CardNumberInput-icon,
        .stripe-element .CardField-icon {
            display: none !important;
            visibility: hidden !important;
            opacity: 0 !important;
        }

        /* Additional selectors for different Stripe versions */
        #card-number-element .Input-icon,
        #card-number-element [class*="icon"],
        #card-number-element [class*="Icon"] {
            display: none !important;
        }

        /* Card brand icons container */
        .card-brands-container {
            position: absolute;
            right: 16px;
            top: 50%;
            transform: translateY(-50%);
            display: flex;
            gap: 4px;
            align-items: center;
        }

        /* Individual card brand icons */
        .card-brand-icon {
            width: 32px;
            height: 20px;
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
            opacity: 0.4;
            transition: all 0.2s ease;
            border-radius: 3px;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }

        .card-brand-icon:hover {
            opacity: 0.7;
            transform: scale(1.05);
        }

        /* When a specific brand is detected, hide others and highlight the detected one */
        .card-brands-container.brand-detected .card-brand-icon {
            opacity: 0.2;
            transform: scale(0.9);
        }

        .card-brands-container.brand-detected .card-brand-icon.detected {
            opacity: 1;
            transform: scale(1.1);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
            z-index: 10;
        }

        .card-brand-visa {
            background-image: url('{% static "audio_upload/images/cards/visa.svg" %}');
        }

        .card-brand-mastercard {
            background-image: url('{% static "audio_upload/images/cards/mastercard.svg" %}');
        }

        .card-brand-amex {
            background-image: url('{% static "audio_upload/images/cards/amex.svg" %}');
        }

        .card-brand-discover {
            background-image: url('{% static "audio_upload/images/cards/discover.svg" %}');
        }

        /* Animation for brand detection */
        @keyframes brandDetected {
            0% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.2);
            }
            100% {
                transform: scale(1.1);
            }
        }

        .card-brand-icon.detected {
            animation: brandDetected 0.3s ease-out;
        }

        /* Tooltip effect for supported cards */
        .card-brands-container::before {
            content: 'We accept';
            position: absolute;
            top: -20px;
            right: 0;
            font-size: 10px;
            color: #8898aa;
            font-weight: 500;
            opacity: 0;
            transition: opacity 0.2s ease;
            pointer-events: none;
        }

        .card-number-row:hover .card-brands-container::before {
            opacity: 1;
        }


        .btn {
            width: 100%;
            padding: 0.75rem 1rem;
            border-radius: 0.5rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.15s;
            border: none;
            font-size: 1rem;
        }

        .btn-primary {
            background: #3b82f6;
            color: white;
        }

        .btn-primary:hover:not(:disabled) {
            background: #2563eb;
        }

        .btn-primary:disabled {
            background: #9ca3af;
            cursor: not-allowed;
        }

        .btn-secondary {
            background: #f9fafb;
            color: #374151;
            border: 1px solid #d1d5db;
        }

        .btn-secondary:hover {
            background: #f3f4f6;
        }

        /* Subtle skip button to encourage donations */
        .btn-skip-subtle {
            background: none;
            border: none;
            color: #6b7280;
            font-size: 13px;
            font-weight: 400;
            cursor: pointer;
            padding: 8px 0;
            text-decoration: none;
            position: relative;
            transition: all 0.2s ease;
            display: inline-block;
        }

        .btn-skip-subtle::after {
            content: '';
            position: absolute;
            bottom: 2px;
            left: 0;
            right: 0;
            height: 1px;
            background: #6b7280;
            opacity: 0.5;
            transition: opacity 0.2s ease;
        }

        .btn-skip-subtle:hover {
            color: #4b5563;
        }

        .btn-skip-subtle:hover::after {
            opacity: 0.8;
        }

        .button-group {
            display: flex;
            flex-direction: column;
            gap: 0;
        }

        .error-message {
            color: #dc2626;
            font-size: 0.875rem;
            margin-top: 0.5rem;
            display: none;
        }

        .error-message.show {
            display: block;
        }

        .loading-spinner {
            display: none;
            text-align: center;
            padding: 1rem;
        }

        .loading-spinner.show {
            display: block;
        }

        .spinner {
            border: 2px solid #f3f4f6;
            border-top: 2px solid #3b82f6;
            border-radius: 50%;
            width: 1.5rem;
            height: 1.5rem;
            animation: spin 1s linear infinite;
            margin: 0 auto 0.5rem;
        }

        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }
            100% {
                transform: rotate(360deg);
            }
        }

        /* Thank you modal animation */
        @keyframes heartBeat {
            0%, 100% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.1);
            }
        }

        .thank-you-heart {
            animation: heartBeat 1.5s ease-in-out infinite;
        }

        /* Donation Encouragement Styles - Wide Layout Version */
        .donation-encouragement {
            margin-bottom: 0;
            padding: 1rem;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            border-radius: 0.5rem;
            border: 1px solid #e2e8f0;
            height: fit-content;
        }

        .encouragement-text {
            font-size: 0.9rem;
            color: #374151;
            font-weight: 500;
            line-height: 1.4;
            margin: 0 0 0.75rem 0;
            text-align: center;
        }

        .impact-highlights {
            display: grid;
            grid-template-columns: 1fr;
            gap: 0.5rem;
        }

        .impact-item {
            display: flex;
            flex-direction: column;
            gap: 0.375rem;
            padding: 0.625rem;
            background: white;
            border-radius: 0.375rem;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
            transition: all 0.15s ease;
        }

        .impact-item:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .impact-header {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .impact-header i {
            width: 1.25rem;
            height: 1.25rem;
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 0.625rem;
            flex-shrink: 0;
        }

        .impact-title {
            font-size: 0.7rem;
            font-weight: 600;
            color: #374151;
            line-height: 1.2;
        }

        .impact-description {
            font-size: 0.625rem;
            color: #6b7280;
            line-height: 1.3;
            margin: 0;
        }

        @media (max-width: 640px) {
            .amount-options {
                grid-template-columns: repeat(2, 1fr);
            }

            .card-details-row {
                flex-direction: column;
            }

            .card-expiry-field {
                border-right: none;
                border-bottom: 1px solid #e6ebf1;
            }

            .field-label {
                font-size: 10px;
            }

            .stripe-field {
                padding: 14px;
            }

            .card-number-row .stripe-field {
                padding-right: 140px; /* Smaller padding on mobile for 4 icons */
            }

            .card-brands-container {
                right: 12px;
                gap: 2px;
            }

            .card-brand-icon {
                width: 28px;
                height: 18px;
            }

            .btn-skip-subtle {
                font-size: 12px;
            }

            /* Responsive donation encouragement */
            .impact-highlights {
                grid-template-columns: 1fr;
                gap: 0.375rem;
            }

            .impact-item {
                padding: 0.5rem;
                gap: 0.375rem;
            }

            .impact-header i {
                width: 1rem;
                height: 1rem;
                font-size: 0.5rem;
            }

            .impact-title {
                font-size: 0.6875rem;
            }

            .impact-description {
                font-size: 0.625rem;
            }

            .encouragement-text {
                font-size: 0.875rem;
                margin-bottom: 0.75rem;
            }

            .donation-encouragement {
                padding: 0.75rem;
                margin-bottom: 1rem;
            }

            /* Mobile: Stack panels vertically */
            .modal-body {
                grid-template-columns: 1fr;
                gap: 1.25rem;
                padding: 1.25rem;
            }

            .donation-modal-content {
                max-width: 95%;
                max-height: 95vh;
            }
        }

        /* Payment Amount Section */
        .payment-amounts {
            margin-bottom: 24px;
        }

        .section-label {
            font-size: 16px;
            font-weight: 600;
            color: #374151;
            margin-bottom: 12px;
        }

        .amount-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 8px;
            margin-bottom: 16px;
        }

        .amount-option {
            padding: 12px;
            border: 2px solid #d1d5db;
            border-radius: 8px;
            text-align: center;
            cursor: pointer;
            background: #ffffff;
            transition: all 0.2s;
            font-weight: 600;
        }

        .amount-option:hover {
            border-color: #3b82f6;
        }

        .amount-option.selected {
            border-color: #3b82f6;
            background: #3b82f6;
            color: white;
        }

        .custom-amount {
            grid-column: span 3;
            margin-top: 8px;
        }

        .custom-amount input {
            width: 100%;
            padding: 12px;
            border: 2px solid #d1d5db;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
        }

        .custom-amount input:focus {
            outline: none;
            border-color: #3b82f6;
        }

        /* Payment Information Section */
        .payment-info {
            margin-bottom: 24px;
        }

        .card-input {
            width: 100%;
            padding: 16px;
            border: 2px solid #d1d5db;
            border-radius: 8px;
            font-size: 16px;
            background: #ffffff;
            transition: border-color 0.2s;
        }

        .card-input:focus {
            outline: none;
            border-color: #3b82f6;
        }

        .accepted-cards {
            display: flex;
            gap: 8px;
            margin-top: 8px;
            align-items: center;
        }

        .accepted-cards-label {
            font-size: 12px;
            color: #6b7280;
            margin-right: 4px;
        }

        .card-logo {
            width: 32px;
            height: 20px;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 8px;
            font-weight: bold;
            color: white;
        }

        .card-visa {
            background: #1a1f71;
        }

        .card-mastercard {
            background: #eb001b;
        }

        .card-amex {
            background: #006fcf;
        }

        .card-discover {
            background: #ff6000;
        }


        /* Buttons */
        .button-group {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .btn {
            width: 100%;
            padding: 16px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s;
        }

        .btn-primary {
            background: #3b82f6;
            color: white;
        }

        .btn-primary:hover {
            background: #2563eb;
        }

        .btn-primary:disabled {
            background: #9ca3af;
            cursor: not-allowed;
        }

        .btn-secondary {
            background: #f3f4f6;
            color: #374151;
            border: 1px solid #d1d5db;
        }

        .btn-secondary:hover {
            background: #e5e7eb;
        }

        /* Error Messages */
        .error-message {
            color: #dc2626;
            font-size: 14px;
            margin-top: 8px;
            display: none;
        }

        .error-message.show {
            display: block;
        }

        /* Loading State */
        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }

        .loading.show {
            display: block;
        }

        /* Responsive Design */
        @media (max-width: 640px) {
            .donation-modal-content {
                width: 95%;
                margin: 20px;
            }

            .amount-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .custom-amount {
                grid-column: span 2;
            }
        }

        /* Payment form enhancements */
        .payment-form-group {
            margin-bottom: 20px;
        }

        .payment-form-row {
            display: flex;
            gap: 16px;
        }

        .payment-form-row .payment-form-group {
            flex: 1;
        }

        /* Stripe Elements styling improvements */
        .StripeElement {
            height: 44px;
            padding: 12px 16px;
            color: #1f2937;
            background-color: white;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
        }

        .StripeElement--focus {
            border-color: #0ea5e9;
            box-shadow: 0 0 0 3px rgba(14, 165, 233, 0.1);
        }

        .StripeElement--invalid {
            border-color: #dc2626;
        }

        .StripeElement--complete {
            border-color: #059669;
        }

        /* Processing state */
        .payment-form.processing {
            opacity: 0.7;
            pointer-events: none;
        }

        .payment-form.processing::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.8);
            border-radius: 16px;
            z-index: 1;
        }

        /* Skip donation button */
        .skip-donation-section {
            margin-top: 32px;
            text-align: center;
            padding-top: 24px;
            border-top: 2px solid #f1f5f9;
        }

        .skip-donation-btn {
            background: transparent;
            border: 1px solid #e5e7eb;
            color: #6b7280;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            padding: 12px 24px;
            border-radius: 8px;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .skip-donation-btn:hover {
            color: #374151;
            background: #f9fafb;
            border-color: #d1d5db;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .skip-donation-btn:active {
            transform: translateY(0);
        }

        .payment-form {
            background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
            border: 2px solid #e2e8f0;
            border-radius: 20px;
            padding: 32px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.06),
            inset 0 1px 0 rgba(255, 255, 255, 0.1);
            position: relative;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .payment-form::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #0ea5e9 0%, #06b6d4 50%, #0891b2 100%);
            border-radius: 16px 16px 0 0;
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        .payment-form:focus-within {
            border-color: #0ea5e9;
            background: #ffffff;
            box-shadow: 0 0 0 4px rgba(14, 165, 233, 0.1),
            0 8px 32px rgba(14, 165, 233, 0.1);
            transform: translateY(-2px);
        }

        .payment-form:focus-within::before {
            transform: scaleX(1);
        }

        .card-input-container {
            position: relative;
        }

        .card-input-label {
            display: block;
            font-size: 14px;
            font-weight: 600;
            color: #374151;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .card-input-label .required {
            color: #ef4444;
            font-size: 12px;
        }

        #donation-card-element {
            padding: 20px 24px;
            border: 2px solid #d1d5db;
            border-radius: 12px;
            background: #ffffff;
            transition: all 0.3s ease;
            min-height: 56px;
            display: flex;
            align-items: center;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04),
            inset 0 1px 0 rgba(255, 255, 255, 0.1);
        }

        #donation-card-element:focus-within {
            border-color: #0ea5e9;
            box-shadow: 0 0 0 4px rgba(14, 165, 233, 0.1),
            0 4px 16px rgba(14, 165, 233, 0.1);
            transform: translateY(-1px);
        }

        .payment-security-info {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-top: 12px;
            padding: 12px 16px;
            background: linear-gradient(145deg, #f0f9ff 0%, #e0f2fe 100%);
            border-radius: 8px;
            border-left: 3px solid #0ea5e9;
        }

        .payment-security-info i {
            color: #0ea5e9;
            font-size: 16px;
        }

        .payment-security-info span {
            font-size: 13px;
            color: #0f172a;
            font-weight: 500;
        }

        #donation-card-errors {
            color: #dc2626;
            margin-top: 12px;
            font-size: 14px;
            font-weight: 500;
            display: none;
            align-items: center;
            gap: 8px;
            padding: 12px 16px;
            background: linear-gradient(145deg, #fef2f2 0%, #fee2e2 100%);
            border-radius: 8px;
            border-left: 4px solid #dc2626;
            animation: errorSlideIn 0.3s ease-out;
        }

        #donation-card-errors.show {
            display: flex;
        }

        #donation-card-errors::before {
            content: '⚠';
            font-size: 16px;
            color: #dc2626;
            flex-shrink: 0;
        }

        @keyframes errorSlideIn {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .donation-modal-actions {
            grid-column: span 2;
            display: flex;
            justify-content: center;
            margin-top: 40px;
            padding-top: 32px;
            border-top: 2px solid #f1f5f9;
        }

        .donation-modal-btn {
            width: 100%;
            max-width: 400px;
            padding: 20px 32px;
            border: none;
            border-radius: 16px;
            font-size: 18px;
            font-weight: 800;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 12px;
            text-transform: uppercase;
            letter-spacing: 0.75px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
        }

        .donation-modal-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s ease;
        }

        .donation-modal-btn:hover::before {
            left: 100%;
        }

        .btn-donate {
            background: linear-gradient(135deg, #0ea5e9 0%, #0284c7 100%);
            color: white;
            border: 2px solid transparent;
            box-shadow: 0 6px 20px rgba(14, 165, 233, 0.3),
            inset 0 1px 0 rgba(255, 255, 255, 0.2);
        }

        .btn-donate:hover:not(:disabled) {
            transform: translateY(-3px);
            box-shadow: 0 12px 32px rgba(14, 165, 233, 0.4),
            inset 0 1px 0 rgba(255, 255, 255, 0.2);
            background: linear-gradient(135deg, #0284c7 0%, #0369a1 100%);
        }

        .btn-donate:active:not(:disabled) {
            transform: translateY(-1px);
            box-shadow: 0 4px 16px rgba(14, 165, 233, 0.3),
            inset 0 1px 0 rgba(255, 255, 255, 0.2);
        }

        .btn-donate:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
            background: linear-gradient(135deg, #94a3b8 0%, #64748b 100%);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }


        .donation-loading {
            display: none;
            text-align: center;
            padding: 32px 24px;
            background: linear-gradient(145deg, #f0f9ff 0%, #e0f2fe 100%);
            border-radius: 16px;
            margin: 24px 0;
            border: 1px solid rgba(14, 165, 233, 0.1);
            box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.1),
            0 2px 8px rgba(14, 165, 233, 0.1);
        }

        .donation-spinner {
            border: 4px solid #e0f2fe;
            border-top: 4px solid #0ea5e9;
            border-radius: 50%;
            width: 48px;
            height: 48px;
            animation: medicalSpin 1.2s linear infinite;
            margin: 0 auto 20px;
            box-shadow: 0 4px 16px rgba(14, 165, 233, 0.2);
        }

        @keyframes medicalSpin {
            0% {
                transform: rotate(0deg);
                box-shadow: 0 4px 16px rgba(14, 165, 233, 0.2);
            }
            50% {
                box-shadow: 0 4px 20px rgba(14, 165, 233, 0.3);
            }
            100% {
                transform: rotate(360deg);
                box-shadow: 0 4px 16px rgba(14, 165, 233, 0.2);
            }
        }

        .donation-loading p {
            color: #0f172a;
            font-weight: 600;
            margin: 0;
            font-size: 16px;
            letter-spacing: 0.5px;
        }

        /* Medical Professional Responsive Design */
        @media (max-width: 1024px) {
            .donation-modal-body {
                grid-template-columns: 1fr;
                gap: 32px;
                padding: 40px 32px 36px;
            }

            .donation-modal-actions {
                grid-column: span 1;
            }
        }

        @media (max-width: 768px) {
            .donation-modal-content {
                width: 98%;
                margin: 2% auto;
                max-height: 95vh;
                overflow-y: auto;
                border-radius: 16px;
            }

            .donation-modal-header {
                padding: 32px 24px 28px;
                border-radius: 16px 16px 0 0;
            }

            .donation-modal-body {
                padding: 32px 24px 28px;
            }

            .donation-amounts {
                grid-template-columns: 1fr;
                gap: 12px;
            }

            .amount-option.custom-amount {
                grid-column: span 1;
            }

            .donation-modal-btn {
                max-width: none;
                padding: 18px 24px;
                font-size: 16px;
            }

            .card-icons {
                flex-wrap: wrap;
                gap: 4px;
            }

            .card-icon {
                width: 40px;
                height: 26px;
                font-size: 9px;
            }
        }

        @media (max-width: 480px) {
            .donation-amounts {
                grid-template-columns: 1fr;
            }

            .amount-option.custom-amount {
                grid-column: span 1;
            }

            .donation-modal-header h2 {
                font-size: 22px;
            }

            .donation-modal-subtitle {
                font-size: 14px;
            }
        }
    </style>

    <!-- Application Configuration -->
    <script>
        window.API_BASE_URL = "{{ API_BASE_URL }}";
        window.LOCAL_BASE_URL = "{{ LOCAL_BASE_URL }}";
        window.CSRF_TOKEN = "{{ csrf_token }}";
    </script>

    <!-- Global Navigation System -->
    <script src="{% static 'js/global_navigation.js' %}?v=1.0"></script>
</head>
<body>
<!-- Navigation removed -->

<div class="container">
    <!-- Header -->
    <div class="header">
        <h1>Speech Analysis History</h1>
        <p>View cognitive assessment results</p>

        <!-- Statistics -->
        <div class="stats-grid">
            <div class="stat-card">
                <span id="total-count" class="stat-value">0</span>
                <span class="stat-label">Total Records</span>
            </div>
            <div class="stat-card">
                <span id="completed-count" class="stat-value">0</span>
                <span class="stat-label">Completed</span>
            </div>
            <div class="stat-card">
                <span id="processing-count" class="stat-value">0</span>
                <span class="stat-label">Processing</span>
            </div>
            <div class="stat-card">
                <span id="failed-count" class="stat-value">0</span>
                <span class="stat-label">Failed</span>
            </div>
        </div>
    </div>

    <!-- Filter Section -->
    <div class="filter-section">
        <div class="filter-grid">
            <div class="form-group">
                <label class="form-label" for="statusFilter">Status</label>
                <select id="statusFilter" class="form-select">
                    <option value="">All Status</option>
                    <option value="completed">Completed</option>
                    <option value="processing">Processing</option>
                    <option value="failed">Failed</option>
                </select>
            </div>
            <div class="form-group">
                <label class="form-label" for="relationshipFilter">Spoken by</label>
                <select id="relationshipFilter" class="form-select">
                    <option value="">All Speakers</option>
                    <option value="my_self">Myself</option>
                    <option value="my_father">My Father</option>
                    <option value="my_mother">My Mother</option>
                    <option value="my_father_in_law">My Father in law</option>
                    <option value="my_mother_in_law">My Mother in law</option>
                    <option value="my_grandfather">My Grandfather</option>
                    <option value="my_grandmother">My Grandmother</option>
                    <option value="great_grandfather">My Great-Grandfather</option>
                    <option value="great_grandmother">My Great-Grandmother</option>
                    <option value="my_friend">My Friend</option>
                    <option value="others">Other</option>
                </select>
            </div>
            <div class="form-group">
                <label class="form-label" for="dateFrom">From Date</label>
                <input type="date" id="dateFrom" class="form-input">
            </div>
            <div class="form-group">
                <label class="form-label" for="dateTo">To Date</label>
                <input type="date" id="dateTo" class="form-input">
            </div>
            <div class="form-group search-group">
                <label class="form-label" for="searchFilter">Search</label>
                <div class="search-container">
                    <input type="text" id="searchFilter" class="form-input" placeholder="Search filename...">
                    <button id="clearFilters" class="clear-btn">
                        <i class="fas fa-times"></i>
                        Clear All
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- History Section -->
    <div class="history-section">
        <div id="historyList" class="history-grid">
            <div class="loading">
                <i class="fas fa-spinner"></i>
                <span>Loading analysis history...</span>
            </div>
        </div>
    </div>
</div>

<!-- High-Performance History Application -->
<script>
    /**
     * Fast History Application - Based on Profile.html Architecture
     * Optimized for speed and user experience
     */
    class FastHistoryApp {
        constructor() {
            this.config = {
                apiBaseUrl: window.API_BASE_URL,
                localBaseUrl: window.LOCAL_BASE_URL
            };

            this.cache = new Map();
            this.isLoading = false;
            this.data = {
                all: [],
                filtered: []
            };

            // Cache DOM elements
            this.dom = {
                historyList: document.getElementById('historyList'),
                totalCount: document.getElementById('total-count'),
                completedCount: document.getElementById('completed-count'),
                processingCount: document.getElementById('processing-count'),
                failedCount: document.getElementById('failed-count'),
                statusFilter: document.getElementById('statusFilter'),
                relationshipFilter: document.getElementById('relationshipFilter'),
                searchFilter: document.getElementById('searchFilter'),
                dateFrom: document.getElementById('dateFrom'),
                dateTo: document.getElementById('dateTo'),
                clearFilters: document.getElementById('clearFilters')
            };

            // Debounced filter function
            this.debouncedFilter = this.debounce(this.applyFilters.bind(this), 150);

            this.init();
        }

        async init() {
            console.log('Initializing FastHistoryApp...');

            try {
                this.setupEventListeners();
                await this.loadHistoryData();
            } catch (error) {
                console.error('Failed to initialize history app:', error);
                this.showError('Failed to load history data');
            }
        }

        // Authentication helpers (using JWT)
        getAuthToken() {
            return localStorage.getItem('access_token');
        }

        isTokenExpired(token) {
            try {
                const payload = JSON.parse(atob(token.split('.')[1]));
                return payload.exp < Date.now() / 1000;
            } catch {
                return true;
            }
        }

        async makeAuthenticatedRequest(url, options = {}) {
            const token = this.getAuthToken();

            if (!token || this.isTokenExpired(token)) {
                // Use global navigation for login redirect
                if (window.globalNav) {
                    window.globalNav.goToLogin();
                } else {
                    window.location.href = '/login/';
                }
                return null;
            }

            const response = await fetch(url, {
                ...options,
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                    ...options.headers
                }
            });

            if (response.status === 401) {
                localStorage.removeItem('access_token');
                // Use global navigation for login redirect
                if (window.globalNav) {
                    window.globalNav.goToLogin();
                } else {
                    window.location.href = '/login/';
                }
                return null;
            }

            return response;
        }

        // Load history data
        async loadHistoryData() {
            if (this.isLoading) return;
            this.isLoading = true;

            try {
                const response = await this.makeAuthenticatedRequest(
                        `${this.config.apiBaseUrl}/api/audio_history/`
                );

                if (!response) return;

                if (response.ok) {
                    const result = await response.json();
                    this.data.all = result.data || result.results || [];
                    this.data.filtered = [...this.data.all];

                    this.updateStatistics();
                    this.displayHistory(this.data.filtered);
                    this.cache.set('historyData', this.data.all);

                    console.log(`Loaded ${this.data.all.length} history records`);
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                console.error('Failed to load history:', error);
                this.showError('Failed to load analysis history');
            } finally {
                this.isLoading = false;
            }
        }

        // Setup event listeners
        setupEventListeners() {
            // Filter event listeners
            if (this.dom.statusFilter) {
                this.dom.statusFilter.addEventListener('change', this.debouncedFilter);
            }
            if (this.dom.relationshipFilter) {
                this.dom.relationshipFilter.addEventListener('change', this.debouncedFilter);
            }
            if (this.dom.searchFilter) {
                this.dom.searchFilter.addEventListener('input', this.debouncedFilter);
            }
            if (this.dom.dateFrom) {
                this.dom.dateFrom.addEventListener('change', this.debouncedFilter);
            }
            if (this.dom.dateTo) {
                this.dom.dateTo.addEventListener('change', this.debouncedFilter);
            }
            if (this.dom.clearFilters) {
                this.dom.clearFilters.addEventListener('click', () => this.clearFilters());
            }
        }

        // Apply filters
        applyFilters() {
            const filters = {
                status: this.dom.statusFilter?.value || '',
                relationship: this.dom.relationshipFilter?.value || '',
                search: this.dom.searchFilter?.value.toLowerCase() || '',
                dateFrom: this.dom.dateFrom?.value || '',
                dateTo: this.dom.dateTo?.value || ''
            };

            this.data.filtered = this.data.all.filter(item => {
                // Status filter
                if (filters.status && item.status !== filters.status) return false;

                // Relationship filter
                if (filters.relationship && item.relationship !== filters.relationship) return false;

                // Search filter
                if (filters.search) {
                    const filename = (item.filename || '').toLowerCase();
                    if (!filename.includes(filters.search)) return false;
                }

                // Date filters
                if (filters.dateFrom) {
                    const itemDate = new Date(item.upload_time);
                    const fromDate = new Date(filters.dateFrom);
                    if (itemDate < fromDate) return false;
                }

                if (filters.dateTo) {
                    const itemDate = new Date(item.upload_time);
                    const toDate = new Date(filters.dateTo);
                    toDate.setHours(23, 59, 59, 999);
                    if (itemDate > toDate) return false;
                }

                return true;
            });

            this.updateStatistics();
            this.displayHistory(this.data.filtered);
        }

        // Clear all filters
        clearFilters() {
            // Clear all filter inputs
            Object.values(this.dom).forEach(element => {
                if (element && (element.tagName === 'INPUT' || element.tagName === 'SELECT')) {
                    element.value = '';
                }
            });

            this.data.filtered = [...this.data.all];
            this.updateStatistics();
            this.displayHistory(this.data.filtered);
        }

        // Update statistics
        updateStatistics() {
            const stats = this.calculateStatistics(this.data.all);
            const filteredStats = this.calculateStatistics(this.data.filtered);

            if (this.dom.totalCount) this.dom.totalCount.textContent = this.data.all.length;
            if (this.dom.completedCount) this.dom.completedCount.textContent = stats.completed;
            if (this.dom.processingCount) this.dom.processingCount.textContent = stats.processing;
            if (this.dom.failedCount) this.dom.failedCount.textContent = stats.failed;
        }

        // Calculate statistics
        calculateStatistics(data) {
            return {
                total: data.length,
                completed: data.filter(item => item.status === 'completed').length,
                processing: data.filter(item => item.status === 'processing').length,
                failed: data.filter(item => item.status === 'failed').length
            };
        }

        // Display history items
        displayHistory(items) {
            if (!this.dom.historyList) return;

            if (items.length === 0) {
                this.dom.historyList.innerHTML = `
                        <div class="empty-state">
                            <i class="fas fa-microphone-slash"></i>
                            <h3>No Analysis Records</h3>
                            <p>No records match your current filters.</p>
                        </div>
                    `;
                return;
            }

            // Use document fragment for better performance
            const fragment = document.createDocumentFragment();

            items.forEach(item => {
                const historyItem = this.createHistoryItem(item);
                fragment.appendChild(historyItem);
            });

            this.dom.historyList.innerHTML = '';
            this.dom.historyList.appendChild(fragment);
        }

        // Create history item element
        createHistoryItem(item) {
            const div = document.createElement('div');
            div.className = 'history-item';

            const statusClass = this.getStatusClass(item.status);
            const mmseScore = this.parseMMSEScore(item.result);
            const relationship = this.formatRelationship(item.relationship);
            const formattedDate = this.formatDate(item.upload_time);

            div.innerHTML = `
                    <div class="history-header">
                        <div class="history-filename">
                            <i class="fas fa-file-audio"></i>
                            <span>${this.escapeHtml(item.filename || 'Audio Analysis')}</span>
                        </div>
                        <div class="history-date">${formattedDate}</div>
                    </div>
                    <div class="history-title">Prediced MMSE Score: ${mmseScore}</div>
                    <div class="history-details">
                        <span class="detail-item">Spoken by: ${relationship}</span>
                        <span class="detail-item">Age: ${item.age || 'N/A'}</span>
                    </div>
                    <span class="history-status ${statusClass}">${this.getStatusText(item.status)}</span>
                    <div class="history-actions">
                        ${this.createViewDetailsButton(item)}
                    </div>
                `;

            return div;
        }

        // Navigation methods
        async viewAnalysisDetail(analysisId) {
            try {
                // Find the analysis item from cached data
                const analysisItem = this.data.all.find(item => item.id == analysisId);

                if (analysisItem) {
                    console.log('Found analysis item:', analysisItem);

                    // Check if user has already donated for this analysis
                    const hasDonated = await this.checkDonationStatus(analysisId);

                    if (hasDonated) {
                        // User has already donated, go directly to details
                        console.log('User has already donated for this analysis, going to details');
                        this.goToAnalysisDetails(analysisItem);
                    } else {
                        // User hasn't donated, show donation modal
                        console.log('User hasn\'t donated, showing donation modal');
                        showDonationModal(analysisId);
                    }
                } else {
                    console.error('Analysis item not found');
                    alert('Analysis record not found.');
                }
            } catch (error) {
                console.error('Failed to check donation status:', error);
                // If there's an error checking donation status, show donation modal as fallback
                showDonationModal(analysisId);
            }
        }

        async checkDonationStatus(analysisId) {
            try {
                const token = localStorage.getItem('access_token');
                if (!token) {
                    console.log('No access token, user needs to login');
                    return false;
                }

                const response = await fetch(`${window.API_BASE_URL}/api/donations/status/${analysisId}/`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });

                const data = await response.json();

                if (data.success) {
                    return data.data.has_donated;
                } else {
                    console.error('Failed to check donation status:', data.message);
                    return false;
                }
            } catch (error) {
                console.error('Error checking donation status:', error);
                return false;
            }
        }

        goToAnalysisDetails(analysisItem) {
            // Store analysis details and navigate to details page
            sessionStorage.setItem('analysisDetails', JSON.stringify(analysisItem));
            window.location.href = '/audio_upload/details/';
        }

        showAnalysisNotReadyMessage(status) {
            let message = '';
            switch (status?.toLowerCase()) {
                case 'processing':
                    message = 'Analysis is still in progress. Please wait for it to complete before viewing details.';
                    break;
                case 'failed':
                    message = 'Analysis failed and details are not available. Please try uploading the audio file again.';
                    break;
                default:
                    message = 'Analysis status is unknown. Please refresh the page or contact support.';
            }

            alert(message);
        }

        // Utility methods
        debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }

        escapeHtml(text) {
            if (!text) return '';
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        formatDate(dateStr) {
            try {
                return new Date(dateStr).toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'short',
                    day: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit'
                });
            } catch {
                return 'Invalid date';
            }
        }

        parseMMSEScore(result) {
            try {
                if (!result) return 'N/A';
                const parsed = typeof result === 'string' ? JSON.parse(result) : result;
                const score = parsed['Predicted mmse score'];
                return typeof score === 'number' ? score.toFixed(1) : (parseFloat(score) || 0).toFixed(1);
            } catch {
                return 'N/A';
            }
        }

        formatRelationship(relationship) {
            const relationshipMap = {
                'my_self': 'Myself',
                'my_father': 'My Father',
                'my_mother': 'My Mother',
                'my_father_in_law': 'My Father in law',
                'my_mother_in_law': 'My Mother in law',
                'my_grandfather': 'My Grandfather',
                'my_grandmother': 'My Grandmother',
                'great_grandfather': 'My Great-Grandfather',
                'great_grandmother': 'My Great-Grandmother',
                'my_friend': 'My Friend',
                'others': 'Other'
            };
            return relationshipMap[relationship] || relationship || 'Not specified';
        }

        getStatusClass(status) {
            switch (status?.toLowerCase()) {
                case 'completed':
                    return 'status-completed';
                case 'processing':
                    return 'status-processing';
                case 'failed':
                    return 'status-failed';
                default:
                    return 'status-completed';
            }
        }

        getStatusText(status) {
            switch (status?.toLowerCase()) {
                case 'completed':
                    return 'Completed';
                case 'processing':
                    return 'Processing';
                case 'failed':
                    return 'Failed';
                default:
                    return 'Unknown';
            }
        }

        createViewDetailsButton(item) {
            const isCompleted = item.status?.toLowerCase() === 'completed';
            const isProcessing = item.status?.toLowerCase() === 'processing';
            const isFailed = item.status?.toLowerCase() === 'failed';

            if (isCompleted) {
                // Analysis completed - button is clickable
                return `
                        <button class="view-details-btn" onclick="fastHistoryApp.viewAnalysisDetail('${item.id}')" aria-label="View analysis details">
                            <i class="fas fa-eye"></i>
                            View Details
                        </button>
                    `;
            } else if (isProcessing) {
                // Analysis in progress - button is disabled
                return `
                        <button class="view-details-btn disabled" onclick="fastHistoryApp.showAnalysisNotReadyMessage('processing')" aria-label="Analysis in progress" title="Please wait for analysis to complete">
                            <i class="fas fa-clock"></i>
                            Processing...
                        </button>
                    `;
            } else if (isFailed) {
                // Analysis failed - button is disabled
                return `
                        <button class="view-details-btn disabled" onclick="fastHistoryApp.showAnalysisNotReadyMessage('failed')" aria-label="Analysis failed" title="Analysis failed - details not available">
                            <i class="fas fa-exclamation-triangle"></i>
                            Failed
                        </button>
                    `;
            } else {
                // Unknown status - button is disabled
                return `
                        <button class="view-details-btn disabled" onclick="fastHistoryApp.showAnalysisNotReadyMessage('unknown')" aria-label="Status unknown" title="Analysis status unknown">
                            <i class="fas fa-question"></i>
                            Unknown
                        </button>
                    `;
            }
        }

        showError(message) {
            if (this.dom.historyList) {
                this.dom.historyList.innerHTML = `
                        <div class="empty-state">
                            <i class="fas fa-exclamation-triangle"></i>
                            <h3>Error</h3>
                            <p>${this.escapeHtml(message)}</p>
                        </div>
                    `;
            }
        }
    }

    // Initialize the application
    let fastHistoryApp;
    document.addEventListener('DOMContentLoaded', () => {
        try {
            fastHistoryApp = new FastHistoryApp();
            window.fastHistoryApp = fastHistoryApp; // For debugging
            console.log('Fast History App initialized successfully');
        } catch (error) {
            console.error('Failed to initialize Fast History App:', error);
        }
    });
</script>

<!-- Modern Donation Modal -->
<div id="donationModal" class="donation-modal">
    <div class="donation-modal-content">
        <div class="modal-header">
            <h2 class="modal-title">Support our Research!</h2>
            <button class="modal-close" onclick="hideDonationModal()">
                <svg width="20" height="20" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd"
                          d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                          clip-rule="evenodd"/>
                </svg>
            </button>
        </div>

        <div class="modal-body">
            <!-- Left Panel: Donation Encouragement -->
            <div class="donation-left-panel">
                <div class="donation-encouragement">
                    <p class="encouragement-text">Your donation supports breakthrough medical AI research that requires
                        significant resources and expertise</p>

                    <div class="impact-highlights">
                        <div class="impact-item">
                            <div class="impact-header">
                                <i class="fas fa-database"></i>
                                <span class="impact-title">Complex Data & Algorithm Development</span>
                            </div>
                            <p class="impact-description">Collecting diverse medical datasets and developing
                                sophisticated algorithms requires extensive research, expert knowledge, and
                                computational resources that are costly and time-intensive.</p>
                        </div>
                        <div class="impact-item">
                            <div class="impact-header">
                                <i class="fas fa-brain"></i>
                                <span class="impact-title">State-of-the-Art AI Performance</span>
                            </div>
                            <p class="impact-description">Achieving superior detection accuracy demands cutting-edge AI
                                models, advanced neural networks, and continuous optimization that require substantial
                                investment in research and development.</p>
                        </div>
                        <div class="impact-item">
                            <div class="impact-header">
                                <i class="fas fa-heart"></i>
                                <span class="impact-title">Global Health Impact</span>
                            </div>
                            <p class="impact-description">Making this technology accessible worldwide for early disease
                                detection and prevention requires funding to multilingual support and global healthcare
                                initiatives.</p>
                        </div>
                        <div class="impact-item">
                            <div class="impact-header">
                                <i class="fas fa-cogs"></i>
                                <span class="impact-title">Infrastructure & Operations</span>
                            </div>
                            <p class="impact-description">Maintaining reliable, secure, and scalable cloud
                                infrastructure for processing medical data requires ongoing investment in servers,
                                security systems, and technical maintenance.</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Right Panel: Payment Form -->
            <div class="donation-right-panel">
                <!-- Payment Amount -->
                <div class="form-group">
                    <label class="form-label">Select Donation Amount</label>
                    <div class="amount-options">
                        <button class="amount-btn" data-amount="50" onclick="selectAmount(50)">$50</button>
                        <button class="amount-btn" data-amount="100" onclick="selectAmount(100)">$100</button>
                        <button class="amount-btn selected" data-amount="150" onclick="selectAmount(150)">$150</button>
                        <button class="amount-btn" data-amount="200" onclick="selectAmount(200)">$200</button>
                        <button class="amount-btn" data-amount="250" onclick="selectAmount(250)">$250</button>
                        <button class="amount-btn" data-amount="300" onclick="selectAmount(300)">$300</button>
                    </div>
                    <div class="custom-amount-wrapper" id="customAmountWrapper" style="display: none;">
                        <span class="custom-amount-dollar">$</span>
                        <input type="number" id="customAmount" class="custom-amount-input" placeholder="0" min="1"
                               max="10000">
                    </div>
                    <button type="button" id="customAmountBtn" onclick="toggleCustomAmount()"
                            style="margin-top: 0.5rem; padding: 0.5rem; background: none; border: 1px solid #d1d5db; border-radius: 0.375rem; color: #6b7280; cursor: pointer;">
                        Input donation amount
                    </button>
                </div>

                <!-- Payment Information -->
                <div class="form-group">
                    <label class="form-label">Payment Information</label>
                    <div class="payment-form-container" id="payment-container">
                        <!-- Card Number Row -->
                        <div class="card-number-row">
                            <div class="stripe-field">
                                <label class="field-label">Card number</label>
                                <div id="card-number-element" class="stripe-element">
                                    <!-- Stripe card number element -->
                                </div>
                                <div id="card-brands-container" class="card-brands-container">
                                    <div class="card-brand-icon card-brand-visa" data-brand="visa"></div>
                                    <div class="card-brand-icon card-brand-mastercard" data-brand="mastercard"></div>
                                    <div class="card-brand-icon card-brand-amex" data-brand="amex"></div>
                                    <div class="card-brand-icon card-brand-discover" data-brand="discover"></div>
                                </div>
                            </div>
                        </div>

                        <!-- Card Details Row -->
                        <div class="card-details-row">
                            <div class="card-expiry-field">
                                <div class="stripe-field">
                                    <label class="field-label">Expiration date</label>
                                    <div id="card-expiry-element" class="stripe-element">
                                        <!-- Stripe card expiry element -->
                                    </div>
                                </div>
                            </div>
                            <div class="card-cvc-field">
                                <div class="stripe-field">
                                    <label class="field-label">Security code</label>
                                    <div id="card-cvc-element" class="stripe-element">
                                        <!-- Stripe card CVC element -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div id="card-errors" class="error-message"></div>
                </div>

                <!-- Loading State -->
                <div id="loading" class="loading-spinner">
                    <div class="spinner"></div>
                    <p>Processing payment...</p>
                </div>

                <!-- Buttons -->
                <div class="button-group">
                    <button id="donate-btn" class="btn btn-primary" onclick="handleDonation()">
                        Donate $150
                    </button>
                    <div style="text-align: center; margin-top: 16px;">
                        <button class="btn-skip-subtle" onclick="skipDonation()">
                            Skip donation and view details
                        </button>
                    </div>
                </div>
            </div> <!-- End donation-right-panel -->
        </div> <!-- End modal-body -->
    </div>
</div>
</div>

<!-- Modern Donation JavaScript -->
<script>
    // Global variables
    let stripe = null;
    let elements = null;
    let cardNumberElement = null;
    let cardExpiryElement = null;
    let cardCvcElement = null;
    let currentAnalysisId = null;
    let selectedAmount = 150;

    // Initialize Stripe
    async function initializeStripe() {
        try {
            console.log('Starting Stripe initialization...');

            // Check if Stripe is available
            if (typeof Stripe === 'undefined') {
                console.error('Stripe.js not loaded');
                throw new Error('Stripe.js not loaded');
            }

            // Get Stripe publishable key from backend
            console.log('Fetching Stripe configuration...');
            const configResponse = await fetch(`${window.API_BASE_URL}/api/stripe/config/`);
            const configData = await configResponse.json();

            if (!configData.success) {
                throw new Error(configData.message || 'Failed to get Stripe configuration');
            }

            console.log('Stripe config received:', configData.data);
            stripe = Stripe(configData.data.publishable_key);

            if (!stripe) {
                throw new Error('Failed to create Stripe instance');
            }

            console.log('Stripe instance created successfully');

            // Create elements with basic styling
            elements = stripe.elements();
            console.log('Elements created successfully');

            // Create separate elements with Stripe styling
            const elementStyles = {
                base: {
                    fontSize: '16px',
                    color: '#32325d',
                    fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif',
                    fontSmoothing: 'antialiased',
                    '::placeholder': {
                        color: '#aab7c4',
                    },
                },
                invalid: {
                    color: '#fa755a',
                    iconColor: 'transparent', // Hide invalid state icon
                },
                complete: {
                    iconColor: 'transparent', // Hide complete state icon
                },
            };

            cardNumberElement = elements.create('cardNumber', {
                style: elementStyles,
                placeholder: '1234 1234 1234 1234',
                showIcon: false,
            });

            cardExpiryElement = elements.create('cardExpiry', {
                style: elementStyles,
                placeholder: 'MM / YY',
            });

            cardCvcElement = elements.create('cardCvc', {
                style: elementStyles,
                placeholder: 'CVC',
            });

            console.log('Card elements created successfully');

            // Mount elements
            cardNumberElement.mount('#card-number-element');
            cardExpiryElement.mount('#card-expiry-element');
            cardCvcElement.mount('#card-cvc-element');
            console.log('Elements mounted successfully');

            // Hide any Stripe built-in icons after mounting
            const hideStripeIcons = () => {
                const cardNumberContainer = document.getElementById('card-number-element');
                if (cardNumberContainer) {
                    const icons = cardNumberContainer.querySelectorAll('[class*="icon"], [class*="Icon"], [data-testid="card-icon"]');
                    icons.forEach(icon => {
                        icon.style.display = 'none';
                        icon.style.visibility = 'hidden';
                        icon.style.opacity = '0';
                    });
                }
            };

            // Initial hide
            setTimeout(hideStripeIcons, 100);

            // Set up MutationObserver to hide icons if they're added dynamically
            const observer = new MutationObserver(hideStripeIcons);
            const cardNumberContainer = document.getElementById('card-number-element');
            if (cardNumberContainer) {
                observer.observe(cardNumberContainer, {
                    childList: true,
                    subtree: true,
                    attributes: true,
                    attributeFilter: ['class']
                });
            }

            // Enhanced event handling with card brand detection
            const handleElementChange = (event) => {
                const displayError = document.getElementById('card-errors');
                if (displayError) {
                    if (event.error) {
                        displayError.textContent = event.error.message;
                        displayError.classList.add('show');
                    } else {
                        displayError.textContent = '';
                        displayError.classList.remove('show');
                    }
                }
            };

            const handleCardNumberChange = (event) => {
                handleElementChange(event);

                // Update card brand icons
                const container = document.getElementById('card-brands-container');
                if (container) {
                    // Remove all detected states
                    container.classList.remove('brand-detected');
                    const allIcons = container.querySelectorAll('.card-brand-icon');
                    allIcons.forEach(icon => {
                        icon.classList.remove('detected');
                    });

                    // If a brand is detected, highlight it
                    if (event.brand && event.brand !== 'unknown') {
                        container.classList.add('brand-detected');
                        const detectedIcon = container.querySelector(`[data-brand="${event.brand}"]`);
                        if (detectedIcon) {
                            detectedIcon.classList.add('detected');
                            console.log(`Card brand detected: ${event.brand}`);
                        }
                    } else {
                        console.log('No card brand detected - showing all supported cards');
                    }
                }
            };

            const handleFocus = () => {
                const container = document.getElementById('payment-container');
                if (container) {
                    container.classList.add('focused');
                }
            };

            const handleBlur = () => {
                const container = document.getElementById('payment-container');
                if (container) {
                    container.classList.remove('focused');
                }
            };

            // Add event listeners with enhanced functionality
            cardNumberElement.on('change', handleCardNumberChange);
            cardNumberElement.on('focus', handleFocus);
            cardNumberElement.on('blur', handleBlur);

            cardExpiryElement.on('change', handleElementChange);
            cardExpiryElement.on('focus', handleFocus);
            cardExpiryElement.on('blur', handleBlur);

            cardCvcElement.on('change', handleElementChange);
            cardCvcElement.on('focus', handleFocus);
            cardCvcElement.on('blur', handleBlur);

            console.log('Event listeners added successfully');

            console.log('Stripe initialization completed successfully');

        } catch (error) {
            console.error('Stripe initialization failed:', error);
            throw error;
        }
    }

    // Show donation modal
    function showDonationModal(analysisId) {
        console.log('Showing donation modal for analysis:', analysisId);
        currentAnalysisId = analysisId;
        const modal = document.getElementById('donationModal');
        if (modal) {
            // Set data attribute as backup
            modal.setAttribute('data-analysis-id', analysisId);
            modal.classList.add('show');
            document.body.style.overflow = 'hidden';
            console.log('Donation modal shown successfully');
        } else {
            console.error('Donation modal element not found');
        }
    }

    // Hide donation modal
    function hideDonationModal() {
        const modal = document.getElementById('donationModal');
        modal.classList.remove('show');
        document.body.style.overflow = 'auto';
        // Don't clear currentAnalysisId here - it's needed for skip donation
    }

    // Select amount
    function selectAmount(amount) {
        selectedAmount = amount;

        // Update UI
        document.querySelectorAll('.amount-btn').forEach(btn => {
            btn.classList.remove('selected');
        });
        document.querySelector(`[data-amount="${amount}"]`).classList.add('selected');

        // Hide custom amount input
        const customWrapper = document.getElementById('customAmountWrapper');
        const customInput = document.getElementById('customAmount');
        customWrapper.style.display = 'none';
        customInput.value = '';

        // Update donate button
        document.getElementById('donate-btn').textContent = `Donate $${amount}`;
    }

    // Toggle custom amount
    function toggleCustomAmount() {
        const customWrapper = document.getElementById('customAmountWrapper');
        const customInput = document.getElementById('customAmount');
        const isVisible = customWrapper.style.display !== 'none';

        if (isVisible) {
            customWrapper.style.display = 'none';
            customInput.value = '';
            selectAmount(150); // Reset to default
        } else {
            customWrapper.style.display = 'block';
            customInput.focus();

            // Clear selected amounts
            document.querySelectorAll('.amount-btn').forEach(btn => {
                btn.classList.remove('selected');
            });

            // Listen for input changes
            customInput.oninput = function () {
                const value = parseInt(this.value) || 0;
                if (value > 0) {
                    selectedAmount = value;
                    document.getElementById('donate-btn').textContent = `Donate $${value}`;
                } else {
                    document.getElementById('donate-btn').textContent = 'Enter Amount';
                }
            };
        }
    }

    // Skip donation
    function skipDonation() {
        console.log('skipDonation called, currentAnalysisId:', currentAnalysisId);

        hideDonationModal();

        if (currentAnalysisId) {
            console.log('Looking for analysis item with ID:', currentAnalysisId);

            const analysisItem = fastHistoryApp.data.all.find(item => item.id == currentAnalysisId);
            console.log('Found analysis item:', analysisItem);

            if (analysisItem) {
                console.log('Using fastHistoryApp.goToAnalysisDetails...');
                fastHistoryApp.goToAnalysisDetails(analysisItem);
            } else {
                console.error('Analysis item not found in data array');
                alert('Analysis record not found. Please try again.');
            }
        } else {
            console.error('currentAnalysisId is not set');
            alert('No analysis selected. Please try again.');
        }

        // Don't clear currentAnalysisId here - it's needed for donation
        // currentAnalysisId = null;
    }

    // Show thank you message and redirect to details
    function showThankYouAndRedirect() {
        // Hide the donation modal
        hideDonationModal();

        // Show thank you message
        const thankYouModal = document.createElement('div');
        thankYouModal.className = 'donation-modal show';
        thankYouModal.innerHTML = `
                <div class="donation-modal-content" style="max-width: 400px; text-align: center;">
                    <div class="modal-header" style="background: linear-gradient(135deg, #10b981 0%, #059669 100%); color: white;">
                        <h2 class="modal-title" style="color: white;">Thank You!</h2>
                    </div>
                    <div class="modal-body" style="padding: 2rem;">
                        <div style="font-size: 3rem; color: #10b981; margin-bottom: 1rem;" class="thank-you-heart">
                            <i class="fas fa-heart"></i>
                        </div>
                        <h3 style="color: #374151; margin-bottom: 1rem;">Your donation was successful!</h3>
                        <p style="color: #6b7280; margin-bottom: 1.5rem;">
                            Thank you for supporting medical research. Your contribution helps advance cognitive health analysis.
                        </p>
                        <div style="font-size: 0.875rem; color: #9ca3af;">
                            Redirecting to analysis details...
                        </div>
                    </div>
                </div>
            `;

        document.body.appendChild(thankYouModal);

        // Redirect after 3 seconds
        setTimeout(() => {
            document.body.removeChild(thankYouModal);
            if (currentAnalysisId) {
                const analysisItem = fastHistoryApp.data.all.find(item => item.id == currentAnalysisId);
                if (analysisItem) {
                    fastHistoryApp.goToAnalysisDetails(analysisItem);
                }
                // Clear currentAnalysisId after use
                currentAnalysisId = null;
            }
        }, 3000);
    }

    // Handle donation
    async function handleDonation() {
        // Validate custom amount if visible
        const customWrapper = document.getElementById('customAmountWrapper');
        const customInput = document.getElementById('customAmount');
        if (customWrapper.style.display !== 'none' && customInput.value) {
            const customValue = parseInt(customInput.value) || 0;
            if (customValue > 0) {
                selectedAmount = customValue;
            } else {
                alert('Please enter a valid amount');
                return;
            }
        }

        const donateBtn = document.getElementById('donate-btn');
        const loading = document.getElementById('loading');

        try {
            // Show loading state
            donateBtn.disabled = true;
            loading.classList.add('show');

            // Check if Stripe is initialized
            if (!stripe || !cardNumberElement) {
                throw new Error('Payment system not initialized. Please refresh the page and try again.');
            }

            // Check authentication
            const token = localStorage.getItem('access_token');
            if (!token) {
                alert('Please log in to continue');
                window.location.href = '/login/';
                return;
            }

            // Validate required data
            if (!currentAnalysisId) {
                throw new Error('No analysis selected. Please try again.');
            }

            if (!selectedAmount || selectedAmount <= 0) {
                throw new Error('Please select a valid donation amount.');
            }

            console.log('Creating donation with:', {
                audio_analysis_id: currentAnalysisId,
                amount: selectedAmount
            });

            // Create donation
            const response = await fetch(`${window.API_BASE_URL}/api/donations/create/`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    audio_analysis_id: currentAnalysisId,
                    amount: selectedAmount
                })
            });

            const data = await response.json();
            console.log('Donation creation response:', data);

            // Log the errors object in detail
            if (data.errors) {
                console.log('Detailed errors:', data.errors);
                for (const [field, fieldErrors] of Object.entries(data.errors)) {
                    console.log(`Field "${field}" errors:`, fieldErrors);
                }
            }

            if (!response.ok) {
                console.error('HTTP Error:', response.status, response.statusText);
                console.error('Response data:', data);
                throw new Error(data.message || `HTTP ${response.status}: Failed to create donation`);
            }

            if (!data.success) {
                console.error('API Error:', data);
                console.error('Full error response:', JSON.stringify(data, null, 2));

                let errorMessage = data.message || 'Failed to create donation';

                // If there are validation errors, include them
                if (data.errors) {
                    console.error('Validation errors:', data.errors);
                    const errorDetails = Object.entries(data.errors)
                            .map(([field, errors]) => {
                                if (Array.isArray(errors)) {
                                    return `${field}: ${errors.join(', ')}`;
                                } else {
                                    return `${field}: ${errors}`;
                                }
                            })
                            .join('; ');
                    errorMessage += ` - Details: ${errorDetails}`;
                }

                throw new Error(errorMessage);
            }

            // Confirm payment with Stripe
            console.log('Confirming payment with Stripe...');
            console.log('Client secret:', data.data.client_secret);
            console.log('Card element:', cardNumberElement);

            const result = await stripe.confirmCardPayment(data.data.client_secret, {
                payment_method: {
                    card: cardNumberElement,
                    billing_details: {
                        name: 'Donor'
                    }
                }
            });

            console.log('Stripe payment result:', result);

            if (result.error) {
                throw new Error(result.error.message);
            }

            // Confirm donation on backend
            await confirmDonation(result.paymentIntent.id);

            // Show thank you message and redirect to details
            showThankYouAndRedirect();

        } catch (error) {
            console.error('Donation failed:', error);
            alert(`Donation failed: ${error.message}`);
        } finally {
            donateBtn.disabled = false;
            loading.classList.remove('show');
        }
    }

    // Confirm donation
    async function confirmDonation(paymentIntentId) {
        console.log('Confirming donation with payment intent:', paymentIntentId);
        const token = localStorage.getItem('access_token');

        const response = await fetch(`${window.API_BASE_URL}/api/donations/confirm/`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                payment_intent_id: paymentIntentId
            })
        });

        const data = await response.json();
        console.log('Donation confirmation response:', data);

        if (!data.success) {
            console.error('Donation confirmation failed:', data);
            throw new Error(data.message || 'Payment confirmation failed');
        }

        console.log('Donation confirmed successfully:', data.data);
        return data;
    }

    // Test function to verify Stripe initialization
    function testStripeInitialization() {
        console.log('Testing Stripe initialization...');
        console.log('Stripe available:', typeof Stripe !== 'undefined');
        console.log('stripe instance:', stripe);
        console.log('elements instance:', elements);
        console.log('cardNumberElement:', cardNumberElement);
        console.log('cardExpiryElement:', cardExpiryElement);
        console.log('cardCvcElement:', cardCvcElement);
    }

    // Initialize on page load
    document.addEventListener('DOMContentLoaded', async function () {
        console.log('DOM loaded, initializing Stripe...');

        // Check if Stripe is available
        if (typeof Stripe === 'undefined') {
            console.error('Stripe.js not loaded');
            alert('Failed to load donation interface. Please refresh the page and try again.');
            return;
        }

        try {
            await initializeStripe();
            console.log('Stripe initialization completed');

            // Test initialization after a short delay
            setTimeout(testStripeInitialization, 1000);
        } catch (error) {
            console.error('Stripe initialization failed:', error);
            alert('Failed to load donation interface. Please try again.');
        }
    });

    // Back button functionality removed

    // Note: Post-login setup is handled globally by base template
</script>

</body>
</html>