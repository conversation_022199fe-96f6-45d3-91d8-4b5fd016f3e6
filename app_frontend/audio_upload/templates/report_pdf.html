<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Cognitive Health Speech Analysis Report</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        /* PDF专用样式 - 基于详情页样式优化 */
        @page {
            size: A4;
            margin: 1.5cm;
        }
        
        /* 基础样式 - 与详情页完全一致 */
        body {
            font-family: 'Times New Roman', Times, serif;
            background-color: #FFFFFF;
            margin: 0;
            padding: 0;
            color: #000000;
            -webkit-font-smoothing: auto;
            font-size: 12pt;
            line-height: 1.4;
        }

        .report-page {
            background: #FFFFFF;
            border: 1px solid #000000;
            box-shadow: none;
        }

        .report-header {
            padding: 2rem;
            border-bottom: 2px solid #000000;
        }

        .header-top {
            display: flex;
            align-items: flex-start;
            justify-content: space-between;
            margin-bottom: 2rem;
        }

        .header-logo {
            width: 80px;
            height: 80px;
            object-fit: contain;
            flex-shrink: 0;
        }

        .header-main {
            flex: 1;
            text-align: center;
            margin: 0 2rem;
        }
        
        .header-main h1 {
            font-family: 'Times New Roman', Times, serif;
            font-size: 18pt;
            margin: 0 0 0.25rem 0;
            font-weight: bold;
        }
        .header-main p {
            font-size: 12pt;
            margin: 0;
            font-weight: bold;
            color: #333;
        }

        .demographics-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 11pt;
            border: 1px solid #000;
        }
        .demographics-table td {
            padding: 0.35rem 0.5rem;
            border: 1px solid #CCC;
        }
        .demographics-table .label {
            font-weight: bold;
            white-space: nowrap;
            color: #000;
        }
        .demographics-table .value {
            color: #000;
        }

        .report-body {
            padding: 2rem;
            font-family: 'Times New Roman', Times, serif;
        }

        .report-section {
            margin-bottom: 2.5rem;
            page-break-inside: avoid;
        }
        .section-title {
            font-family: 'Times New Roman', Times, serif;
            font-size: 16pt;
            font-weight: bold;
            color: #000;
            border-bottom: 1px solid #000;
            padding-bottom: 0.5rem;
            margin: 0 0 1.5rem 0;
        }

        .results-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 12pt;
            margin-bottom: 2rem;
            border: 1px solid black;
        }
        .results-table th, .results-table td {
            border: 1px solid black;
            padding: 0.75rem;
            text-align: left;
        }
        .results-table th {
            font-family: 'Times New Roman', Times, serif;
            background-color: #F0F0F0;
            font-weight: bold;
        }
        .results-table .result-value {
            font-weight: bold;
            text-align: center;
        }

        .transcription-box {
            background: #F8F8F8;
            border: 1px solid #CCC;
            border-left: 4px solid #000000;
            padding: 1rem 1.5rem;
            margin-top: 1rem;
            page-break-inside: avoid;
        }
        .transcription-box h4 {
            font-family: 'Times New Roman', Times, serif;
            font-weight: bold;
            margin-top: 0;
            margin-bottom: 0.5rem;
        }
        .transcription-content {
            margin: 0;
            line-height: 1.7;
            font-style: italic;
            color: #000;
            font-family: 'Times New Roman', Times, serif;
            font-size: 12pt;
        }

        .features-grid {
            margin-top: 1.5rem;
        }
        .feature-category {
            margin-bottom: 2rem;
            page-break-inside: avoid;
        }
        .feature-category h4 {
            font-family: 'Times New Roman', Times, serif;
            font-size: 14pt;
            font-weight: bold;
            margin: 0 0 1rem 0;
            color: #000;
        }
        .feature-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 11pt;
            margin-bottom: 1rem;
        }
        .feature-table th, .feature-table td {
            border: 1px solid #E0E0E0;
            padding: 0.5rem;
            text-align: left;
        }
        .feature-table th {
            background-color: #F8F8F8;
            font-weight: bold;
        }
        .feature-table td:nth-child(2), .feature-table th:nth-child(2) {
            text-align: center;
            width: 20%;
            position: relative;
        }
        .feature-table td:nth-child(3), .feature-table th:nth-child(3) {
            text-align: center;
            width: 25%;
        }

        .abnormal-indicator {
            color: #000000;
            font-weight: bold;
            position: absolute;
            right: 10px;
            top: 50%;
            transform: translateY(-50%);
        }

        .report-footer {
            margin-top: 3rem;
            padding: 1.5rem 2rem;
            border-top: 2px solid #000;
            font-size: 10pt;
            color: #333;
        }
        .disclaimer {
            line-height: 1.6;
            margin-bottom: 1rem;
        }

        /* PDF专用隐藏元素 */
        .tooltip-container {
            display: none;
        }
        
        /* 科学记数法上标样式 */
        sup {
            font-size: 0.8em;
            vertical-align: super;
        }
    </style>
</head>
<body>
    <div class="report-page">
        <header class="report-header">
            <div class="header-top">
                <div style="width: 80px;"></div> <!-- Logo占位符 -->
                <div class="header-main">
                    <h1>Cognitive Health Speech Analysis</h1>
                    <p>CONFIDENTIAL SCIENTIFIC REPORT</p>
                </div>
                <div style="width: 80px;"></div> <!-- 占位符保持居中 -->
            </div>
            <table class="demographics-table">
                <tbody>
                    <tr>
                        <td class="label">Audio File Name:</td>
                        <td class="value" id="subject-id"></td>
                        <td class="label">Date of Report:</td>
                        <td class="value" id="report-date">{{ current_date }}</td>
                    </tr>
                    <tr>
                        <td class="label">This Audio is Spoken by:</td>
                        <td class="value" id="subject-relationship"></td>
                        <td class="label">Occupation:</td>
                        <td class="value" id="subject-occupation"></td>
                    </tr>
                    <tr>
                        <td class="label">Age:</td>
                        <td class="value" id="subject-age" colspan="3"></td>
                    </tr>
                </tbody>
            </table>
        </header>

        <main class="report-body">
            <section class="report-section">
                <h3 class="section-title">Prediction Results</h3>
                <table class="results-table">
                    <thead>
                        <tr>
                            <th style="width: 50%; text-align: center;">
                                <strong>Predicted MMSE Score</strong> (from voice)
                            </th>
                            <th style="width: 50%; text-align: center;">
                                Percentile
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td class="result-value" id="result-mmse"></td>
                            <td class="result-value" id="result-percentile"></td>
                        </tr>
                    </tbody>
                </table>
            </section>

            <section class="report-section">
                <h3 class="section-title">Technical Details</h3>
                <table class="results-table">
                    <thead>
                        <tr>
                            <th style="width: 50%; text-align: center;">
                                Model
                            </th>
                            <th style="width: 50%; text-align: center;">Model Performance</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td class="result-value" id="tech-model"></td>
                            <td>
                                <div style="margin-bottom: 10px;">
                                    <strong>RMSE:</strong> 
                                    <span id="tech-rmse" style="vertical-align: middle;"></span>
                                </div>
                                <div>
                                    <strong>Pearson correlation coefficient:</strong> 
                                    <span id="tech-pearson" style="vertical-align: middle;"></span>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </section>

            <section class="report-section">
                <h3 class="section-title">Speech Analysis</h3>
                <div class="transcription-box">
                    <h4>Enhanced Transcription with Linguistic Analysis</h4>
                    <div id="result-transcription" class="transcription-content"></div>
                </div>
            </section>

            <section class="report-section">
                <h3 class="section-title">Feature Analysis</h3>
                <div class="features-grid" id="features-container">
                    <!-- JS will inject feature categories and lists here -->
                </div>
            </section>

        </main>

        <footer class="report-footer">
            <p class="disclaimer">
                <strong>MEDICAL DISCLAIMER:</strong> This computational analysis report is generated using artificial intelligence algorithms for research and clinical decision support purposes. The acoustic and linguistic biomarkers presented herein are derived from automated speech analysis and should be interpreted within the context of comprehensive clinical assessment. This report does not constitute a medical diagnosis, clinical recommendation, or therapeutic intervention. The findings require validation through standardized neuropsychological evaluation and clinical correlation by qualified healthcare professionals. Healthcare providers should exercise clinical judgment when integrating these results with patient history, physical examination, and other diagnostic modalities. This technology is intended as an adjunctive tool and should not replace established clinical protocols or professional medical expertise in the assessment of cognitive function.
            </p>
            <p id="footer-id"></p>
        </footer>
    </div>

    <script>
        // 与详情页完全相同的JavaScript代码
        document.addEventListener('DOMContentLoaded', function() {
            // 从模板变量获取数据
            const analysisDetailsString = '{{ analysis_data|escapejs }}';
            
            if (!analysisDetailsString) {
                document.body.innerHTML = '<h1>Error: No analysis data found.</h1>';
                return;
            }

            // 解析数据
            let item, analysisResult;
            try {
                item = JSON.parse(analysisDetailsString);
                analysisResult = item.result && typeof item.result === 'string'
                    ? JSON.parse(item.result)
                    : (item.result || {});
            } catch (e) {
                console.error("Failed to parse analysis data:", e);
                document.body.innerHTML = `<h1>Error: Could not display analysis data.</h1>`;
                return;
            }

            // 与详情页完全相同的格式化函数
            const formatValue = (value, fallback = 'Not provided') => value ?? fallback;

            const formatRelationship = (value) => {
                if (!value) return 'Not provided';
                const map = { 'my_self': 'Myself', 'my_father': 'My Father', 'my_mother': 'My Mother', 'my_father_in_law': 'My Father in Law', 'my_mother_in_law': 'My Mother in Law', 'my_grandfather': 'My Grandfather', 'my_grandmother': 'My Grandmother', 'my_friend': 'My Friend' };
                return map[value] || value;
            };

            const formatOccupation = (value) => {
                if (!value) return 'Not provided';
                const map = { 'student': 'Student', 'retired': 'Retired', 'unemployed': 'Unemployed' };
                return map[value] || value.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
            };

            // 标准的数值格式化函数 - 以10为底的科学记数法，保留3位有效数字
            const formatNumber = (value) => {
                const num = parseFloat(value);
                if (isNaN(num)) return 'Not provided';

                // 处理零值
                if (num === 0) return '0';

                // 处理整数
                if (Number.isInteger(num) && Math.abs(num) < 1000) {
                    return num.toString();
                }

                const absNum = Math.abs(num);

                // 判断是否需要科学记数法：过大(≥1000)或过小(<0.01)的数值
                if (absNum >= 1000 || (absNum < 0.01 && absNum !== 0)) {
                    // 使用标准的科学记数法格式
                    const scientificStr = num.toExponential(2); // 保留2位小数，总共3位有效数字
                    const [mantissa, exponentStr] = scientificStr.split('e');
                    const exponent = parseInt(exponentStr);

                    // 格式化为标准的10为底科学记数法
                    return `${mantissa} × 10<sup>${exponent}</sup>`;
                }

                // 对于正常范围的数值，保留3位有效数字
                return parseFloat(num.toPrecision(3)).toString();
            };

            // Helper function to escape HTML for security when handling plain text
            function escapeHtml(text) {
                const div = document.createElement('div');
                div.textContent = text;
                return div.innerHTML;
            }

            // 填充文档数据 - 与详情页完全一致

            // Header Demographics
            document.getElementById('subject-id').textContent = formatValue(item.filename);
            document.getElementById('subject-age').textContent = formatValue(item.age);
            document.getElementById('subject-relationship').textContent = formatRelationship(item.relationship);
            document.getElementById('subject-occupation').textContent = formatOccupation(item.occupation);

            // Test Results Table
            document.getElementById('result-mmse').textContent = formatNumber(analysisResult['Predicted mmse score']);
            document.getElementById('result-percentile').textContent = formatValue(analysisResult['Percentile']) + '%';

            // Enhanced Transcription
            const transcriptionElement = document.getElementById('result-transcription');
            const coloredTranscription = analysisResult['Transcribed with color'];

            if (coloredTranscription && coloredTranscription.trim() !== '') {
                // 对于PDF，移除HTML标签，只保留文本
                const tempDiv = document.createElement('div');
                tempDiv.innerHTML = coloredTranscription;
                transcriptionElement.textContent = tempDiv.textContent || tempDiv.innerText || '';
            } else {
                // Fallback to regular transcription
                const regularTranscription = analysisResult['Transcribed'];
                if (regularTranscription && regularTranscription.trim() !== '') {
                    transcriptionElement.textContent = regularTranscription;
                } else {
                    transcriptionElement.textContent = 'Transcription not available';
                }
            }

            // Biomarkers - 与详情页完全一致的特征处理
            const featuresContainer = document.getElementById('features-container');
            const features = analysisResult['Selected features'] || {};
            featuresContainer.innerHTML = '';

            if (Object.keys(features).length === 0) {
                featuresContainer.innerHTML = '<p>No biomarker data available for this analysis.</p>';
            } else {
                let categoryKeys = Object.keys(features);
                const is10Index = categoryKeys.indexOf('is10');
                if (is10Index > -1) {
                    const is10Key = categoryKeys.splice(is10Index, 1)[0];
                    categoryKeys.push(is10Key);
                }

                categoryKeys.forEach(category => {
                    const featureList = features[category];

                    if (!Array.isArray(featureList) || featureList.length === 0) return;

                    const categoryDiv = document.createElement('div');
                    categoryDiv.className = 'feature-category';

                    const title = document.createElement('h4');
                    let categoryName = category.replace(/_/g, ' ');
                    // Apply specific category name mappings
                    if (categoryName === 'digipsych prosody feats') {
                        categoryName = 'Prosodic';
                    } else if (categoryName === 'lexicosyntactic') {
                        categoryName = 'Linguistic';
                    } else if (categoryName === 'is10') {
                        categoryName = 'Acoustic';
                    }
                    title.textContent = categoryName + ' Features';
                    categoryDiv.appendChild(title);

                    const table = document.createElement('table');
                    table.className = 'feature-table';

                    const thead = table.createTHead();
                    const headerRow = thead.insertRow();
                    headerRow.innerHTML = '<th>Feature</th><th>Value</th><th>Reference Range</th>';

                    const tbody = table.createTBody();

                    featureList.forEach((featureObject, index) => {
                        const name = featureObject['feature name'];
                        const value = featureObject['value'];
                        const lower = featureObject['clsi lower'];
                        const upper = featureObject['clsi upper'];
                        const briefIntroduction = featureObject['brief introduction'];

                        if (name === undefined || value === undefined) return;

                        const row = tbody.insertRow();

                        // 创建特征名称单元格，包含简介（PDF中显示为小字）
                        const nameCell = row.insertCell();
                        const displayName = name.replace(/_/g, ' ');

                        if (briefIntroduction && briefIntroduction.trim() !== '') {
                            nameCell.innerHTML = `${displayName}<br><small style="font-style: italic; color: #666; font-size: 9pt;">${escapeHtml(briefIntroduction)}</small>`;
                        } else {
                            nameCell.textContent = displayName;
                        }

                        const valueCell = row.insertCell();
                        let indicator = '';
                        if (lower !== undefined && upper !== undefined) {
                            const numValue = parseFloat(value);
                            const numLower = parseFloat(lower);
                            const numUpper = parseFloat(upper);
                            if (!isNaN(numValue)) {
                                if (numValue > numUpper) {
                                    indicator = '<span class="abnormal-indicator">▲</span>';
                                } else if (numValue < numLower) {
                                    indicator = '<span class="abnormal-indicator">▼</span>';
                                }
                            }
                        }
                        valueCell.innerHTML = `<div style="text-align: center; width: 100%;">${formatNumber(value)}</div>${indicator}`;

                        const refCell = row.insertCell();
                        if (lower !== undefined && upper !== undefined) {
                            refCell.innerHTML = `${formatNumber(lower)} - ${formatNumber(upper)}`;
                        } else {
                            refCell.textContent = 'Not provided';
                        }
                    });

                    if (tbody.rows.length > 0) {
                        categoryDiv.appendChild(table);
                        featuresContainer.appendChild(categoryDiv);
                    }
                });
            }

            // Technical Details - 与详情页完全一致
            document.getElementById('tech-model').textContent = formatValue(analysisResult['Model']);

            const modelPerformance = analysisResult['Model performance'] || {};
            let rmseFormatted = 'Not provided';
            let pearsonFormatted = 'Not provided';

            if (typeof modelPerformance === 'object' && modelPerformance !== null) {
                const rmse = modelPerformance['RMSE'];
                const pearson = modelPerformance['Pearson correlation coefficient'];
                rmseFormatted = rmse !== undefined ? formatNumber(rmse) : 'Not provided';
                pearsonFormatted = pearson !== undefined ? formatNumber(pearson) : 'Not provided';
            } else if (typeof modelPerformance === 'number') {
                // 旧格式兼容
                rmseFormatted = formatNumber(modelPerformance);
            }

            document.getElementById('tech-rmse').innerHTML = rmseFormatted;
            document.getElementById('tech-pearson').innerHTML = pearsonFormatted;

            // Footer
            document.getElementById('footer-id').textContent = `Report for File Name: ${formatValue(item.filename)}`;
        });
    </script>
</body>
</html>
