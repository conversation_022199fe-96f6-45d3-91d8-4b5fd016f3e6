import os
import tempfile
import time
from urllib.parse import quote
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from webdriver_manager.chrome import ChromeDriverManager
from django.conf import settings
from django.urls import reverse
from django.http import HttpRequest


def create_transcription_image(transcription_html, output_path=None):
    """
    将转录HTML内容转换为图片
    
    Args:
        transcription_html (str): 包含颜色标记的转录HTML内容
        output_path (str, optional): 输出图片的路径，如果为None则使用临时文件
    
    Returns:
        str: 生成的图片文件路径
    """
    if output_path is None:
        # 创建临时文件
        temp_file = tempfile.NamedTemporaryFile(suffix='.png', delete=False)
        output_path = temp_file.name
        temp_file.close()
    
    # 设置Chrome选项
    chrome_options = Options()
    chrome_options.add_argument('--headless')  # 无头模式
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    chrome_options.add_argument('--disable-gpu')
    chrome_options.add_argument('--window-size=1200,800')
    chrome_options.add_argument('--disable-extensions')
    chrome_options.add_argument('--disable-plugins')
    chrome_options.add_argument('--disable-images')  # 禁用图片加载以提高速度
    
    # 初始化WebDriver
    try:
        service = Service(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service, options=chrome_options)
        
        # 编码转录内容用于URL传递
        encoded_transcription = quote(transcription_html)
        
        # 构建本地HTML文件URL
        # 注意：在生产环境中，您可能需要使用完整的域名
        local_html_path = os.path.join(settings.BASE_DIR, 'audio_upload', 'templates', 'transcription_capture.html')
        file_url = f"file://{local_html_path}?transcription={encoded_transcription}"
        
        # 加载页面
        driver.get(file_url)
        
        # 等待页面加载完成
        wait = WebDriverWait(driver, 10)
        wait.until(lambda driver: driver.execute_script("return document.body.getAttribute('data-ready') === 'true'"))
        
        # 额外等待一点时间确保渲染完成
        time.sleep(1)
        
        # 找到转录区域元素
        transcription_element = driver.find_element(By.ID, "transcription-capture-area")
        
        # 截取转录区域的截图
        transcription_element.screenshot(output_path)
        
        return output_path
        
    except Exception as e:
        print(f"Error creating transcription image: {e}")
        # 如果出错，创建一个简单的错误图片或返回None
        return None
        
    finally:
        if 'driver' in locals():
            driver.quit()


def create_transcription_image_alternative(transcription_html, output_path=None):
    """
    备用方案：使用HTML字符串直接创建临时HTML文件
    
    Args:
        transcription_html (str): 包含颜色标记的转录HTML内容
        output_path (str, optional): 输出图片的路径
    
    Returns:
        str: 生成的图片文件路径
    """
    if output_path is None:
        temp_file = tempfile.NamedTemporaryFile(suffix='.png', delete=False)
        output_path = temp_file.name
        temp_file.close()
    
    # 创建临时HTML文件
    temp_html = tempfile.NamedTemporaryFile(mode='w', suffix='.html', delete=False, encoding='utf-8')
    
    # 完整的HTML内容
    html_content = f"""<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Transcription Capture</title>
    <style>
        body {{
            font-family: 'Times New Roman', Times, serif;
            background-color: #FFFFFF;
            margin: 0;
            padding: 20px;
            color: #000000;
        }}
        .transcription-container {{
            width: 800px;
            margin: 0 auto;
            background: #FFFFFF;
        }}
        .transcription-box {{
            background: #F8F8F8;
            border: 1px solid #CCC;
            border-left: 4px solid #000000;
            padding: 1rem 1.5rem;
            margin: 0;
        }}
        .transcription-box h4 {{
            font-family: 'Times New Roman', Times, serif;
            font-weight: bold;
            margin: 0 0 1rem 0;
            font-size: 1.1rem;
            color: #000;
        }}
        .transcription-content {{
            margin: 0;
            line-height: 1.7;
            font-style: italic;
            color: #000;
            font-family: 'Times New Roman', Times, serif;
            font-size: 1rem;
        }}
        .transcription-content .word-normal {{ color: #333; }}
        .transcription-content .word-hesitation {{
            color: #ff6b35;
            background-color: rgba(255, 107, 53, 0.1);
            padding: 1px 3px;
            border-radius: 3px;
        }}
        .transcription-content .word-repetition {{
            color: #e74c3c;
            background-color: rgba(231, 76, 60, 0.1);
            padding: 1px 3px;
            border-radius: 3px;
            text-decoration: underline;
        }}
        .transcription-content .word-pause {{
            color: #9b59b6;
            background-color: rgba(155, 89, 182, 0.1);
            padding: 1px 3px;
            border-radius: 3px;
            font-weight: bold;
        }}
        .transcription-content .word-filler {{
            color: #f39c12;
            background-color: rgba(243, 156, 18, 0.1);
            padding: 1px 3px;
            border-radius: 3px;
            font-style: normal;
        }}
        .transcription-content .word-unclear {{
            color: #95a5a6;
            background-color: rgba(149, 165, 166, 0.1);
            padding: 1px 3px;
            border-radius: 3px;
            text-decoration: line-through;
        }}
        .transcription-content .word-emphasis {{
            color: #27ae60;
            background-color: rgba(39, 174, 96, 0.1);
            padding: 1px 3px;
            border-radius: 3px;
            font-weight: bold;
        }}
        .transcription-content .word-error {{
            color: #c0392b;
            background-color: rgba(192, 57, 43, 0.1);
            padding: 1px 3px;
            border-radius: 3px;
            border: 1px solid rgba(192, 57, 43, 0.3);
        }}
    </style>
</head>
<body>
    <div class="transcription-container">
        <div class="transcription-box" id="transcription-capture-area">
            <h4>Enhanced Transcription with Linguistic Analysis</h4>
            <div class="transcription-content">
                {transcription_html}
            </div>
        </div>
    </div>
</body>
</html>"""
    
    temp_html.write(html_content)
    temp_html.close()
    
    # 设置Chrome选项
    chrome_options = Options()
    chrome_options.add_argument('--headless')
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    chrome_options.add_argument('--disable-gpu')
    chrome_options.add_argument('--window-size=1200,800')
    
    try:
        service = Service(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service, options=chrome_options)
        
        # 加载临时HTML文件
        driver.get(f"file://{temp_html.name}")
        
        # 等待页面加载
        time.sleep(2)
        
        # 找到转录区域并截图
        transcription_element = driver.find_element(By.ID, "transcription-capture-area")
        transcription_element.screenshot(output_path)
        
        return output_path
        
    except Exception as e:
        print(f"Error creating transcription image (alternative): {e}")
        return None
        
    finally:
        # 清理临时文件
        if 'driver' in locals():
            driver.quit()
        try:
            os.unlink(temp_html.name)
        except:
            pass
