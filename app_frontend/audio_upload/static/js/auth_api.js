/**
 * Modern Frontend Authentication API Module - VERSION 2.0 (FIXED)
 * API Base URL: Dynamically configured from Django settings
 * Supports complete user management: registration, login, email verification, password reset, etc.
 */

console.log('🔧 Loading auth_api.js VERSION 2.0 (FIXED) - JSON parsing improved');

class AuthAPI {
    constructor() {
        // Use API_BASE_URL from Django settings, fallback to default for development
        this.API_BASE_URL = window.API_CONFIG?.API_BASE_URL || window.API_BASE_URL + '/api' || 'http://192.168.50.180:8001/api';
        console.log('AuthAPI initialization - API_BASE_URL:', this.API_BASE_URL); // Debug info
        console.log('window.API_CONFIG:', window.API_CONFIG); // Debug info
        console.log('window.API_BASE_URL:', window.API_BASE_URL); // Debug info

        this.token = localStorage.getItem('access_token');
        this.refreshToken = localStorage.getItem('refresh_token');
        this.pendingRequests = new Map(); // 防重复请求

        // Bind methods to instance
        this.register = this.register.bind(this);
        this.login = this.login.bind(this);
        this.logout = this.logout.bind(this);
        this.verifyEmail = this.verifyEmail.bind(this);
        this.verifyCode = this.verifyCode.bind(this);
        this.resendVerification = this.resendVerification.bind(this);
        this.requestPasswordReset = this.requestPasswordReset.bind(this);
        this.confirmPasswordReset = this.confirmPasswordReset.bind(this);
    }

    /**
     * 通用API请求方法
     */
    async makeRequest(endpoint, options = {}) {
        const url = `${this.API_BASE_URL}${endpoint}`;
        console.log('API请求URL:', url); // 调试信息

        const defaultOptions = {
            headers: {
                'Content-Type': 'application/json',
                ...options.headers
            }
        };

        // 如果有token，添加到请求头
        if (this.token && !options.skipAuth) {
            defaultOptions.headers['Authorization'] = `Bearer ${this.token}`;
        }

        const finalOptions = { ...defaultOptions, ...options };
        console.log('请求选项:', finalOptions); // 调试信息

        try {
            const response = await fetch(url, finalOptions);
            console.log('响应状态:', response.status, response.statusText); // 调试信息

            // 检查响应的Content-Type是否为JSON
            const contentType = response.headers.get('content-type');
            console.log('响应Content-Type:', contentType); // 调试信息
            const isJson = contentType && contentType.includes('application/json');

            let data;
            if (isJson) {
                try {
                    data = await response.json();
                    console.log('解析的JSON数据:', data); // 调试信息
                } catch (jsonError) {
                    console.error('JSON解析失败:', jsonError);
                    const textResponse = await response.text();
                    console.error('原始响应内容:', textResponse.substring(0, 500));
                    throw new Error(`JSON解析失败: ${jsonError.message}`);
                }
            } else {
                // 如果不是JSON响应，获取文本内容用于错误信息
                const textResponse = await response.text();
                console.error('非JSON响应 (状态码:', response.status, '):', textResponse.substring(0, 500));

                // 如果是网络错误或服务器错误，提供更具体的错误信息
                if (response.status >= 500) {
                    throw new Error(`服务器内部错误 (${response.status}): 请稍后重试`);
                } else if (response.status === 404) {
                    throw new Error(`API接口未找到 (${response.status}): ${url}`);
                } else if (response.status === 403) {
                    throw new Error(`访问被拒绝 (${response.status}): 可能是CORS或权限问题`);
                } else {
                    throw new Error(`服务器返回非JSON响应 (状态码: ${response.status})`);
                }
            }

            // 如果token过期，尝试刷新
            if (response.status === 401 && !options.skipAuth) {
                const refreshed = await this.refreshAccessToken();
                if (refreshed) {
                    // 重新设置Authorization头并重试
                    finalOptions.headers['Authorization'] = `Bearer ${this.token}`;
                    const retryResponse = await fetch(url, finalOptions);

                    // 对重试响应也进行同样的检查
                    const retryContentType = retryResponse.headers.get('content-type');
                    const retryIsJson = retryContentType && retryContentType.includes('application/json');

                    if (retryIsJson) {
                        return await retryResponse.json();
                    } else {
                        const retryTextResponse = await retryResponse.text();
                        console.error('重试请求非JSON响应:', retryTextResponse.substring(0, 200));
                        throw new Error(`重试请求返回非JSON响应 (状态码: ${retryResponse.status})`);
                    }
                }
            }

            // 对于某些API端点，400状态码是正常的业务响应（如注册验证失败）
            // 只有在5xx服务器错误时才抛出异常
            if (response.status >= 500) {
                if (data && data.message) {
                    throw new Error(data.message);
                } else {
                    throw new Error(`服务器错误 ${response.status}: ${response.statusText}`);
                }
            }

            return data;
        } catch (error) {
            console.error('API请求失败:', error);
            throw error;
        }
    }

    /**
     * 用户注册 - 仅需要邮箱和密码
     */
    async register(userData) {
        console.log('🚀 AuthAPI.register called with:', userData.email);
        console.log('📊 Current pending requests:', this.pendingRequests.size);

        // 防重复请求检查
        const requestKey = `register_${userData.email}`;

        if (this.pendingRequests.has(requestKey)) {
            console.log('⚠️ Duplicate register request detected, returning existing promise');
            return this.pendingRequests.get(requestKey);
        }

        console.log('✅ Creating new registration request for:', userData.email);

        // 创建请求Promise
        const requestPromise = this.makeRequest('/register/', {
            method: 'POST',
            body: JSON.stringify({
                email: userData.email,
                password: userData.password,
                password_confirm: userData.passwordConfirm || userData.password_confirm,
                first_name: userData.firstName || userData.first_name || '',
                last_name: userData.lastName || userData.last_name || ''
            }),
            skipAuth: true
        });

        // 存储请求Promise
        this.pendingRequests.set(requestKey, requestPromise);
        console.log('📝 Request stored, pending requests now:', this.pendingRequests.size);

        try {
            const response = await requestPromise;
            console.log('✅ Registration request completed for:', userData.email);
            return response;
        } finally {
            // 请求完成后清理
            this.pendingRequests.delete(requestKey);
            console.log('🧹 Request cleaned up, pending requests now:', this.pendingRequests.size);
        }
    }

    /**
     * 用户登录
     */
    async login(email, password) {
        const response = await this.makeRequest('/login/', {
            method: 'POST',
            body: JSON.stringify({
                email: email,
                password: password
            }),
            skipAuth: true
        });

        if (response.success && response.data && response.data.tokens) {
            // 保存token到localStorage
            this.token = response.data.tokens.access;
            this.refreshToken = response.data.tokens.refresh;
            localStorage.setItem('access_token', this.token);
            localStorage.setItem('refresh_token', this.refreshToken);
            localStorage.setItem('user_info', JSON.stringify(response.data.user));
        }

        return response;
    }

    /**
     * 用户登出
     */
    async logout() {
        const response = await this.makeRequest('/logout/', {
            method: 'POST',
            body: JSON.stringify({
                refresh_token: this.refreshToken
            })
        });

        // 清除本地存储的token
        this.token = null;
        this.refreshToken = null;
        localStorage.removeItem('access_token');
        localStorage.removeItem('refresh_token');
        localStorage.removeItem('user_info');

        return response;
    }

    /**
     * 刷新访问token
     */
    async refreshAccessToken() {
        if (!this.refreshToken) {
            return false;
        }

        try {
            const response = await this.makeRequest('/token/refresh/', {
                method: 'POST',
                body: JSON.stringify({
                    refresh: this.refreshToken
                }),
                skipAuth: true
            });

            if (response.access) {
                this.token = response.access;
                localStorage.setItem('access_token', this.token);
                return true;
            }
        } catch (error) {
            console.error('Token refresh failed:', error);
        }

        return false;
    }

    /**
     * 检查邮箱是否已注册
     */
    async checkEmail(email) {
        const response = await this.makeRequest(`/check-email/?email=${encodeURIComponent(email)}`, {
            method: 'GET',
            skipAuth: true
        });

        return response;
    }

    /**
     * Resend verification email
     */
    async resendVerification(email) {
        const response = await this.makeRequest('/resend-verification/', {
            method: 'POST',
            body: JSON.stringify({
                email: email
            }),
            skipAuth: true
        });

        return response;
    }

    /**
     * 获取用户资料
     */
    async getUserProfile() {
        const response = await this.makeRequest('/profile/', {
            method: 'GET'
        });

        return response;
    }

    /**
     * 更新用户资料
     */
    async updateUserProfile(profileData) {
        const formData = new FormData();

        if (profileData.first_name) formData.append('first_name', profileData.first_name);
        if (profileData.last_name) formData.append('last_name', profileData.last_name);
        if (profileData.avatar) formData.append('avatar', profileData.avatar);

        const response = await this.makeRequest('/profile/', {
            method: 'PUT',
            body: formData,
            headers: {} // 让浏览器自动设置Content-Type for FormData
        });

        return response;
    }

    /**
     * 修改密码
     */
    async changePassword(passwordData) {
        const response = await this.makeRequest('/change-password/', {
            method: 'POST',
            body: JSON.stringify({
                old_password: passwordData.oldPassword,
                new_password: passwordData.newPassword,
                new_password_confirm: passwordData.newPasswordConfirm
            })
        });

        return response;
    }

    /**
     * 获取音频分析历史
     */
    async getAudioHistory() {
        const response = await this.makeRequest('/audio_history/', {
            method: 'GET'
        });

        return response;
    }

    /**
     * 检查用户是否已登录
     */
    isLoggedIn() {
        return !!this.token;
    }

    /**
     * 获取当前用户信息
     */
    getCurrentUser() {
        const userInfo = localStorage.getItem('user_info');
        return userInfo ? JSON.parse(userInfo) : null;
    }

    /**
     * Upload audio file
     */
    async uploadAudio(formData) {
        const response = await this.makeRequest('/audio_upload/', {
            method: 'POST',
            body: formData,
            headers: {} // Let browser automatically set Content-Type for FormData
        });

        return response;
    }

    /**
     * Get audio history records
     */
    async getAudioHistory() {
        const response = await this.makeRequest('/audio_history/', {
            method: 'GET'
        });

        return response;
    }

    /**
     * Email verification
     */
    async verifyEmail(token) {
        const response = await this.makeRequest('/verify-email/', {
            method: 'POST',
            body: JSON.stringify({ token: token }),
            skipAuth: true
        });

        return response;
    }

    /**
     * Verify activation code
     */
    async verifyCode(email, verificationCode) {
        const response = await this.makeRequest('/verify-code/', {
            method: 'POST',
            body: JSON.stringify({
                email: email,
                verification_code: verificationCode
            }),
            skipAuth: true
        });

        return response;
    }

    /**
     * Request password reset
     */
    async requestPasswordReset(email) {
        const response = await this.makeRequest('/password-reset/', {
            method: 'POST',
            body: JSON.stringify({ email: email }),
            skipAuth: true
        });

        return response;
    }

    /**
     * Confirm password reset
     */
    async confirmPasswordReset(token, password, passwordConfirm) {
        const response = await this.makeRequest('/password-reset-confirm/', {
            method: 'POST',
            body: JSON.stringify({
                token: token,
                password: password,
                password_confirm: passwordConfirm
            }),
            skipAuth: true
        });

        return response;
    }

    /**
     * Update user profile
     */
    async updateUserProfile(profileData) {
        const response = await this.makeRequest('/profile/', {
            method: 'PUT',
            body: JSON.stringify(profileData)
        });

        return response;
    }

    /**
     * Health check
     */
    async healthCheck() {
        const response = await this.makeRequest('/health/', {
            method: 'GET',
            skipAuth: true
        });

        return response;
    }
}

// Create global instance
const authAPI = new AuthAPI();

// 导出供其他脚本使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AuthAPI;
}

/**
 * 注册表单处理 - 已移至各页面的内联脚本中处理
 * 此函数保留为空，避免重复事件绑定
 */
function handleRegistration() {
    // 注册处理已移至register.html页面的内联脚本中
    // 此函数保留为空，避免重复事件绑定
    return;
}

/**
 * 登录表单处理
 */
function handleLogin() {
    const loginForm = document.getElementById('loginForm');
    if (!loginForm) return;

    loginForm.addEventListener('submit', async (e) => {
        e.preventDefault();

        const email = document.getElementById('email').value;
        const password = document.getElementById('password').value;
        const submitBtn = document.getElementById('loginBtn');
        const messageDiv = document.getElementById('loginMessage');

        // 显示加载状态
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Signing In...';
        messageDiv.innerHTML = '';

        try {
            const response = await authAPI.login(email, password);

            if (response.success) {
                messageDiv.innerHTML = `
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle"></i>
                        Login successful! Redirecting...
                    </div>
                `;

                // Use global navigation system for redirect
                setTimeout(() => {
                    if (window.globalNav) {
                        window.globalNav.handleLoginSuccess();
                    } else {
                        window.location.href = '/';
                    }
                }, 1500);
            } else {
                let errorMessage = response.message || 'Login failed';
                if (response.errors) {
                    const errors = Object.values(response.errors).flat();
                    errorMessage = errors.join('<br>');
                }

                // Check if it's an account not activated error
                const isAccountNotActivated = errorMessage.includes('账户未激活') || errorMessage.includes('未激活') || errorMessage.includes('请输入验证码') || errorMessage.includes('not activated') || errorMessage.includes('inactive') || errorMessage.includes('verification code');

                if (isAccountNotActivated) {
                    // Account not activated, redirect to verification code page
                    localStorage.setItem('pendingActivationEmail', email);
                    messageDiv.innerHTML = `
                        <div class="alert alert-error">
                            <i class="fas fa-exclamation-circle"></i>
                            ${errorMessage}
                            <div style="margin-top: 15px;">
                                <button onclick="goToVerifyCode('${email}')" class="btn btn-primary" style="background: #667eea; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer; margin-right: 10px;">
                                    <i class="fas fa-key"></i> Enter Verification Code
                                </button>
                                <button onclick="resendActivationCode('${email}')" class="btn btn-secondary" style="background: #6c757d; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;">
                                    <i class="fas fa-envelope"></i> Resend Verification Code
                                </button>
                            </div>
                        </div>
                    `;
                } else {
                    // Other errors
                    messageDiv.innerHTML = `
                        <div class="alert alert-error">
                            <i class="fas fa-exclamation-circle"></i>
                            ${errorMessage}
                        </div>
                    `;
                }
            }
        } catch (error) {
            messageDiv.innerHTML = `
                <div class="alert alert-error">
                    <i class="fas fa-exclamation-circle"></i>
                    Network error, please try again.
                </div>
            `;
        } finally {
            submitBtn.disabled = false;
            submitBtn.innerHTML = '<i class="fas fa-sign-in-alt"></i> Sign In';
        }
    });
}

/**
 * 密码重置表单处理
 */
function handlePasswordReset() {
    const resetForm = document.getElementById('passwordResetForm');
    if (!resetForm) return;

    resetForm.addEventListener('submit', async (e) => {
        e.preventDefault();

        const email = document.getElementById('email').value;
        const submitBtn = document.getElementById('resetBtn');
        const messageDiv = document.getElementById('resetMessage');

        // 显示加载状态
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Sending...';
        messageDiv.innerHTML = '';

        try {
            const response = await authAPI.requestPasswordReset(email);

            if (response.success) {
                messageDiv.innerHTML = `
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle"></i>
                        ${response.message}
                    </div>
                `;
                resetForm.reset();
            } else {
                let errorMessage = response.message || 'Send failed';
                if (response.errors) {
                    const errors = Object.values(response.errors).flat();
                    errorMessage = errors.join('<br>');
                }
                messageDiv.innerHTML = `
                    <div class="alert alert-error">
                        <i class="fas fa-exclamation-circle"></i>
                        ${errorMessage}
                    </div>
                `;
            }
        } catch (error) {
            messageDiv.innerHTML = `
                <div class="alert alert-error">
                    <i class="fas fa-exclamation-circle"></i>
                    Network error, please try again.
                </div>
            `;
        } finally {
            submitBtn.disabled = false;
            submitBtn.innerHTML = '<i class="fas fa-envelope"></i> Send Reset Email';
        }
    });
}

/**
 * 页面加载时初始化
 */
document.addEventListener('DOMContentLoaded', function() {
    // 检查是否有自定义事件处理器标识，避免重复绑定
    if (!window.customAuthHandlers) {
        handleRegistration();
        handleLogin();
        handlePasswordReset();
    }

    // 处理URL参数中的消息
    const urlParams = new URLSearchParams(window.location.search);
    const message = urlParams.get('message');
    const error = urlParams.get('error');

    if (message) {
        const messageDiv = document.getElementById('pageMessage') || document.getElementById('loginMessage') || document.getElementById('registerMessage');
        if (messageDiv) {
            messageDiv.innerHTML = `
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i>
                    ${message}
                </div>
            `;
        }
    }

    if (error) {
        const messageDiv = document.getElementById('pageMessage') || document.getElementById('loginMessage') || document.getElementById('registerMessage');
        if (messageDiv) {
            messageDiv.innerHTML = `
                <div class="alert alert-error">
                    <i class="fas fa-exclamation-circle"></i>
                    ${error}
                </div>
            `;
        }
    }
});

/**
 * Navigate to verification code page
 */
function goToVerifyCode(email) {
    localStorage.setItem('pendingActivationEmail', email);
    if (window.globalNav) {
        window.globalNav.navigateTo(`/verify-code?email=${encodeURIComponent(email)}`, false);
    } else {
        window.location.href = `/verify-code?email=${encodeURIComponent(email)}`;
    }
}

/**
 * Resend verification code
 */
async function resendActivationCode(email) {
    try {
        const response = await authAPI.resendVerification(email);

        const messageDiv = document.getElementById('loginMessage') || document.getElementById('registerMessage');
        if (messageDiv) {
            if (response.success) {
                messageDiv.innerHTML = `
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle"></i>
                        ${response.message || 'Verification code has been resent, please check your email'}
                        <div style="margin-top: 15px;">
                            <button onclick="goToVerifyCode('${email}')" class="btn btn-primary" style="background: #667eea; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;">
                                <i class="fas fa-key"></i> Enter Verification Code
                            </button>
                        </div>
                    </div>
                `;
            } else {
                messageDiv.innerHTML = `
                    <div class="alert alert-error">
                        <i class="fas fa-exclamation-circle"></i>
                        ${response.message || 'Failed to send, please try again later'}
                    </div>
                `;
            }
        }
    } catch (error) {
        const messageDiv = document.getElementById('loginMessage') || document.getElementById('registerMessage');
        if (messageDiv) {
            messageDiv.innerHTML = `
                <div class="alert alert-error">
                    <i class="fas fa-exclamation-circle"></i>
                    Network error, please try again later
                </div>
            `;
        }
    }
}

/**
 * Resend activation email (for compatibility)
 */
async function resendActivationEmail(email) {
    return await resendActivationCode(email);
}
