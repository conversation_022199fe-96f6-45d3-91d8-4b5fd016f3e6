/**
 * Service Worker for History Page Performance Optimization
 * Advanced caching strategy with network-first for API and cache-first for static assets
 */

const CACHE_VERSION = 'v2.0.0';
const STATIC_CACHE = `medvoice-static-${CACHE_VERSION}`;
const API_CACHE = `medvoice-api-${CACHE_VERSION}`;
const RUNTIME_CACHE = `medvoice-runtime-${CACHE_VERSION}`;

// Static assets to cache with preload
const STATIC_ASSETS = [
    '/audio_upload/static/css/upload.css',
    'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css',
    'https://cdn.jsdelivr.net/npm/jwt-decode@3.1.2/build/jwt-decode.min.js',
    'https://fonts.googleapis.com/css2?family=Source+Sans+Pro:wght@300;400;600;700;900&family=Merriweather:wght@300;400;700;900&family=IBM+Plex+Mono:wght@300;400;500;600;700&display=swap'
];

// API endpoints to cache with network-first strategy
const API_ENDPOINTS = [
    '/api/audio_history/',
    '/api/user/profile/'
];

// Cache duration settings
const CACHE_DURATION = {
    static: 7 * 24 * 60 * 60 * 1000, // 7 days
    api: 5 * 60 * 1000, // 5 minutes
    runtime: 24 * 60 * 60 * 1000 // 1 day
};

// Install event - cache static assets
self.addEventListener('install', event => {
    console.log('Service Worker installing...');
    
    event.waitUntil(
        Promise.all([
            caches.open(STATIC_CACHE).then(cache => {
                console.log('Caching static assets...');
                return cache.addAll(STATIC_ASSETS);
            }),
            caches.open(API_CACHE),
            caches.open(RUNTIME_CACHE)
        ]).then(() => {
            console.log('Service Worker installed successfully');
            return self.skipWaiting();
        })
    );
});

// Activate event - clean up old caches
self.addEventListener('activate', event => {
    console.log('Service Worker activating...');
    
    event.waitUntil(
        caches.keys().then(cacheNames => {
            return Promise.all(
                cacheNames.map(cacheName => {
                    if (cacheName.startsWith('medvoice-') && 
                        !cacheName.includes(CACHE_VERSION)) {
                        console.log('Deleting old cache:', cacheName);
                        return caches.delete(cacheName);
                    }
                })
            );
        }).then(() => {
            console.log('Service Worker activated');
            return self.clients.claim();
        })
    );
});

// Fetch event - implement caching strategies
self.addEventListener('fetch', event => {
    const { request } = event;
    const url = new URL(request.url);

    // Skip non-GET requests
    if (request.method !== 'GET') {
        return;
    }

    // Handle API requests with network-first strategy
    if (isApiRequest(url)) {
        event.respondWith(networkFirstStrategy(request));
        return;
    }

    // Handle static assets with cache-first strategy
    if (isStaticAsset(url)) {
        event.respondWith(cacheFirstStrategy(request));
        return;
    }

    // Handle other requests with stale-while-revalidate
    event.respondWith(staleWhileRevalidateStrategy(request));
});

// Check if request is for API
function isApiRequest(url) {
    return API_ENDPOINTS.some(endpoint => url.pathname.includes(endpoint)) ||
           url.pathname.startsWith('/api/');
}

// Check if request is for static asset
function isStaticAsset(url) {
    return STATIC_ASSETS.some(asset => url.href.includes(asset)) ||
           url.pathname.includes('/static/') ||
           url.hostname !== location.hostname;
}

// Network-first strategy for API requests
async function networkFirstStrategy(request) {
    const cache = await caches.open(API_CACHE);
    
    try {
        // Try network first
        const networkResponse = await fetch(request);
        
        if (networkResponse.ok) {
            // Cache successful responses
            const responseClone = networkResponse.clone();
            await cache.put(request, responseClone);
        }
        
        return networkResponse;
    } catch (error) {
        console.log('Network failed, trying cache:', error);
        
        // Fallback to cache
        const cachedResponse = await cache.match(request);
        if (cachedResponse) {
            return cachedResponse;
        }
        
        // Return offline page or error response
        return new Response(
            JSON.stringify({ error: 'Network unavailable' }),
            { 
                status: 503,
                headers: { 'Content-Type': 'application/json' }
            }
        );
    }
}

// Cache-first strategy for static assets
async function cacheFirstStrategy(request) {
    const cache = await caches.open(STATIC_CACHE);
    const cachedResponse = await cache.match(request);
    
    if (cachedResponse) {
        return cachedResponse;
    }
    
    try {
        const networkResponse = await fetch(request);
        if (networkResponse.ok) {
            await cache.put(request, networkResponse.clone());
        }
        return networkResponse;
    } catch (error) {
        console.log('Failed to fetch static asset:', error);
        throw error;
    }
}

// Stale-while-revalidate strategy for other requests
async function staleWhileRevalidateStrategy(request) {
    const cache = await caches.open(RUNTIME_CACHE);
    const cachedResponse = await cache.match(request);
    
    // Fetch from network in background
    const fetchPromise = fetch(request).then(response => {
        if (response.ok) {
            cache.put(request, response.clone());
        }
        return response;
    }).catch(error => {
        console.log('Background fetch failed:', error);
    });
    
    // Return cached version immediately if available
    return cachedResponse || fetchPromise;
}

// Background sync for offline actions
self.addEventListener('sync', event => {
    if (event.tag === 'background-sync') {
        event.waitUntil(doBackgroundSync());
    }
});

async function doBackgroundSync() {
    console.log('Performing background sync...');
    // Implement background sync logic here
}

// Push notification handler
self.addEventListener('push', event => {
    if (event.data) {
        const data = event.data.json();
        const options = {
            body: data.body,
            icon: '/audio_upload/static/images/icon-192x192.png',
            badge: '/audio_upload/static/images/badge-72x72.png',
            vibrate: [100, 50, 100],
            data: {
                dateOfArrival: Date.now(),
                primaryKey: data.primaryKey
            },
            actions: [
                {
                    action: 'explore',
                    title: 'View Details',
                    icon: '/audio_upload/static/images/checkmark.png'
                },
                {
                    action: 'close',
                    title: 'Close',
                    icon: '/audio_upload/static/images/xmark.png'
                }
            ]
        };
        
        event.waitUntil(
            self.registration.showNotification(data.title, options)
        );
    }
});

// Notification click handler
self.addEventListener('notificationclick', event => {
    console.log('Notification clicked:', event.notification);
    
    event.notification.close();
    
    event.waitUntil(
        clients.openWindow('/audio_upload/history/')
    );
});

// Message handler for cache management
self.addEventListener('message', event => {
    if (event.data && event.data.type === 'SKIP_WAITING') {
        self.skipWaiting();
    }
    
    if (event.data && event.data.type === 'CLEAR_CACHE') {
        event.waitUntil(clearAllCaches());
    }
});

async function clearAllCaches() {
    const cacheNames = await caches.keys();
    await Promise.all(
        cacheNames.map(cacheName => caches.delete(cacheName))
    );
    console.log('All caches cleared');
}

// Periodic cache cleanup
setInterval(async () => {
    const cache = await caches.open(API_CACHE);
    const requests = await cache.keys();
    
    for (const request of requests) {
        const response = await cache.match(request);
        const dateHeader = response.headers.get('date');
        
        if (dateHeader) {
            const responseDate = new Date(dateHeader);
            const now = new Date();
            
            if (now - responseDate > CACHE_DURATION.api) {
                await cache.delete(request);
                console.log('Expired cache entry removed:', request.url);
            }
        }
    }
}, 60000); // Check every minute
