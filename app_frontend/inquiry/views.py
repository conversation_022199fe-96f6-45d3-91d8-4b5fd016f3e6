from django.http import JsonResponse
from django.shortcuts import render, redirect
from django.views.decorators.http import require_http_methods
from django.contrib.auth.decorators import login_required, user_passes_test
from django.core.paginator import Paginator
from django.db.models import Q

from django_ratelimit.decorators import ratelimit

from .models import Inquiry
from .forms import InquiryForm
from project.settings import API_BASE_URL, LOCAL_BASE_URL


@require_http_methods(['GET', 'POST'])
def inquiry_view(request):

    if request.method == 'GET':
        context = {
            'API_BASE_URL': API_BASE_URL,
            'LOCAL_BASE_URL': LOCAL_BASE_URL
        }
        return render(request, 'html/contact_us.html', context)

    elif request.method == 'POST':

        form = InquiryForm(request.POST)

        if form.is_valid():
            form.save()
            return redirect("contact-success")

        context = {
            'errors': form.errors,
            'data': request.POST,
            'API_BASE_URL': API_BASE_URL,
            'LOCAL_BASE_URL': LOCAL_BASE_URL
        }
        return render(request, 'html/contact_us.html', context)
    

@require_http_methods(['POST'])
@ratelimit(key='ip', rate='10/min', method=ratelimit.ALL, block=True)
def inquiry_api_view(request):
    """
    Frontend inquiry API view that forwards requests to backend API
    """
    import requests
    import logging

    logger = logging.getLogger(__name__)

    try:
        # Log the incoming request for debugging
        logger.info(f"Contact form submission received from IP: {request.META.get('REMOTE_ADDR')}")

        # Prepare data for backend API
        data = {
            'name': request.POST.get('name', ''),
            'email': request.POST.get('email', ''),
            'inquiry_type': request.POST.get('inquiry_type', '0'),
            'description': request.POST.get('description', '')
        }

        # Log the data being sent (without sensitive info)
        logger.info(f"Sending data to backend: email={data['email']}, type={data['inquiry_type']}")

        # Call backend API
        backend_url = f"{API_BASE_URL}/api/contact/inquiry/"
        logger.info(f"Calling backend URL: {backend_url}")

        response = requests.post(backend_url, data=data, timeout=30)

        logger.info(f"Backend response status: {response.status_code}")

        if response.status_code == 201:
            # Success
            try:
                backend_data = response.json()
                logger.info("Contact form submitted successfully")
                return JsonResponse({
                    'success': True,
                    'message': backend_data.get('message', 'Inquiry submitted successfully')
                }, status=200)
            except Exception as e:
                logger.error(f"Error parsing successful backend response: {e}")
                return JsonResponse({
                    'success': True,
                    'message': 'Inquiry submitted successfully'
                }, status=200)
        else:
            # Error from backend
            logger.error(f"Backend API error: Status {response.status_code}")
            try:
                error_data = response.json()
                error_message = error_data.get('error', 'Failed to submit inquiry')
                logger.error(f"Backend error message: {error_message}")
                return JsonResponse({
                    'success': False,
                    'error': error_message
                }, status=400)
            except Exception as e:
                logger.error(f"Error parsing backend error response: {e}")
                logger.error(f"Raw backend response: {response.text}")
                return JsonResponse({
                    'success': False,
                    'error': 'Failed to submit inquiry'
                }, status=400)

    except requests.exceptions.Timeout:
        logger.error("Backend API request timeout")
        return JsonResponse({
            'success': False,
            'error': 'Request timeout. Please try again.'
        }, status=500)
    except requests.exceptions.ConnectionError as e:
        logger.error(f"Backend API connection error: {e}")
        return JsonResponse({
            'success': False,
            'error': 'Unable to connect to server. Please try again later.'
        }, status=500)
    except Exception as e:
        logger.error(f"Unexpected error in inquiry_api_view: {e}", exc_info=True)
        return JsonResponse({
            'success': False,
            'error': 'An unexpected error occurred. Please try again.'
        }, status=500)


@require_http_methods(['GET'])
def inquiry_success(request):

    return render(request, "components/pages/success.html", {
        'title': 'Inquiry Submitted',
        'description': 'Thank you for taking time to submit an inquiry, our team will be in touch shortly.'
    })


def is_admin_user(user):
    """Check if user is admin (staff or in Data Analysts group)"""
    return user.is_authenticated and (user.is_staff or user.groups.filter(name='Data Analysts').exists())


def message_list_view(request):
    """Admin view to list all messages from backend API"""
    context = {
        'API_BASE_URL': API_BASE_URL,
        'LOCAL_BASE_URL': LOCAL_BASE_URL,
    }
    # Use the working minimal template instead of the problematic main template
    return render(request, 'inquiry/message-minimal.html', context)


def message_debug_view(request):
    """Debug view for message management troubleshooting"""
    context = {
        'API_BASE_URL': API_BASE_URL,
        'LOCAL_BASE_URL': LOCAL_BASE_URL,
    }
    return render(request, 'inquiry/message-debug.html', context)


def message_test_view(request):
    """Simple test view for message API"""
    context = {
        'API_BASE_URL': API_BASE_URL,
        'LOCAL_BASE_URL': LOCAL_BASE_URL,
    }
    return render(request, 'inquiry/message-test.html', context)


def message_simple_view(request):
    """Ultra-simple test view for debugging"""
    context = {
        'API_BASE_URL': API_BASE_URL,
        'LOCAL_BASE_URL': LOCAL_BASE_URL,
    }
    return render(request, 'inquiry/message-simple.html', context)


def message_minimal_view(request):
    """Minimal message management view for debugging"""
    context = {
        'API_BASE_URL': API_BASE_URL,
        'LOCAL_BASE_URL': LOCAL_BASE_URL,
    }
    return render(request, 'inquiry/message-minimal.html', context)