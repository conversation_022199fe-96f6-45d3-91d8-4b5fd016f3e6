# Generated by Django 4.2.11 on 2024-04-21 16:19

from django.db import migrations, models
import phonenumber_field.modelfields


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Inquiry',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.Char<PERSON>ield(max_length=200)),
                ('email', models.EmailField(max_length=254)),
                ('phone', phonenumber_field.modelfields.PhoneNumberField(blank=True, max_length=128, null=True, region=None)),
                ('datetime', models.DateTimeField(auto_now=True)),
                ('inquiry_type', models.PositiveSmallIntegerField(choices=[(0, 'General')], default=0)),
                ('description', models.TextField(max_length=1500)),
            ],
        ),
    ]
