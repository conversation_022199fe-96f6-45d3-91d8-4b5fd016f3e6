{% extends 'html/base.html' %}
{% load static %}

{% block title %}Profile Test - HiSage Health{% endblock %}

{% block content %}
<div class="tw-container tw-mx-auto tw-px-4 tw-py-8">
    <div class="tw-max-w-4xl tw-mx-auto">
        <h1 class="tw-text-3xl tw-font-bold tw-text-gray-900 tw-mb-8">用户Profile测试页面</h1>
        
        <!-- 测试说明 -->
        <div class="tw-bg-blue-50 tw-border tw-border-blue-200 tw-rounded-lg tw-p-6 tw-mb-8">
            <h2 class="tw-text-xl tw-font-semibold tw-text-blue-900 tw-mb-4">测试说明</h2>
            <div class="tw-text-blue-800">
                <p class="tw-mb-2">这个页面用于测试新的用户Profile功能：</p>
                <ul class="tw-list-disc tw-list-inside tw-space-y-1">
                    <li>未登录时：显示 "Sign in" 和 "Sign up" 按钮</li>
                    <li>已登录时：显示用户头像和下拉菜单</li>
                    <li>点击头像可以打开/关闭下拉菜单</li>
                    <li>下拉菜单包含用户信息和操作选项</li>
                    <li>支持响应式设计，在移动端也能正常使用</li>
                </ul>
            </div>
        </div>
        
        <!-- 功能测试区域 -->
        <div class="tw-grid tw-grid-cols-1 md:tw-grid-cols-2 tw-gap-8">
            <!-- 登录状态模拟 -->
            <div class="tw-bg-white tw-rounded-lg tw-shadow-md tw-p-6">
                <h3 class="tw-text-lg tw-font-semibold tw-mb-4">登录状态模拟</h3>
                
                <div class="tw-space-y-4">
                    <button id="simulate-login" class="tw-w-full tw-bg-green-600 tw-text-white tw-py-2 tw-px-4 tw-rounded-lg hover:tw-bg-green-700 tw-transition-colors">
                        模拟登录
                    </button>
                    
                    <button id="simulate-logout" class="tw-w-full tw-bg-red-600 tw-text-white tw-py-2 tw-px-4 tw-rounded-lg hover:tw-bg-red-700 tw-transition-colors">
                        模拟登出
                    </button>
                    
                    <div class="tw-text-sm tw-text-gray-600">
                        <p><strong>当前状态：</strong> <span id="auth-status">检查中...</span></p>
                    </div>
                </div>
            </div>
            
            <!-- Profile信息显示 -->
            <div class="tw-bg-white tw-rounded-lg tw-shadow-md tw-p-6">
                <h3 class="tw-text-lg tw-font-semibold tw-mb-4">Profile信息</h3>
                
                <div class="tw-space-y-3 tw-text-sm">
                    <div>
                        <strong>用户名：</strong> 
                        <span id="display-name">-</span>
                    </div>
                    <div>
                        <strong>邮箱：</strong> 
                        <span id="display-email">-</span>
                    </div>
                    <div>
                        <strong>用户ID：</strong> 
                        <span id="display-user-id">-</span>
                    </div>
                    <div>
                        <strong>头像：</strong> 
                        <span id="display-avatar">默认头像</span>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 功能特性 -->
        <div class="tw-bg-white tw-rounded-lg tw-shadow-md tw-p-6 tw-mt-8">
            <h3 class="tw-text-lg tw-font-semibold tw-mb-4">新Profile功能特性</h3>
            
            <div class="tw-grid tw-grid-cols-1 md:tw-grid-cols-2 lg:tw-grid-cols-3 tw-gap-6">
                <div class="tw-text-center">
                    <div class="tw-w-12 tw-h-12 tw-bg-blue-100 tw-rounded-full tw-flex tw-items-center tw-justify-center tw-mx-auto tw-mb-3">
                        <i class="bi bi-person-circle tw-text-blue-600 tw-text-xl"></i>
                    </div>
                    <h4 class="tw-font-semibold tw-mb-2">现代化设计</h4>
                    <p class="tw-text-sm tw-text-gray-600">采用最新的UI设计趋势，美观且易用</p>
                </div>
                
                <div class="tw-text-center">
                    <div class="tw-w-12 tw-h-12 tw-bg-green-100 tw-rounded-full tw-flex tw-items-center tw-justify-center tw-mx-auto tw-mb-3">
                        <i class="bi bi-phone tw-text-green-600 tw-text-xl"></i>
                    </div>
                    <h4 class="tw-font-semibold tw-mb-2">响应式设计</h4>
                    <p class="tw-text-sm tw-text-gray-600">在所有设备上都能完美显示</p>
                </div>
                
                <div class="tw-text-center">
                    <div class="tw-w-12 tw-h-12 tw-bg-purple-100 tw-rounded-full tw-flex tw-items-center tw-justify-center tw-mx-auto tw-mb-3">
                        <i class="bi bi-lightning tw-text-purple-600 tw-text-xl"></i>
                    </div>
                    <h4 class="tw-font-semibold tw-mb-2">流畅动画</h4>
                    <p class="tw-text-sm tw-text-gray-600">平滑的过渡动画提升用户体验</p>
                </div>
                
                <div class="tw-text-center">
                    <div class="tw-w-12 tw-h-12 tw-bg-yellow-100 tw-rounded-full tw-flex tw-items-center tw-justify-center tw-mx-auto tw-mb-3">
                        <i class="bi bi-shield-check tw-text-yellow-600 tw-text-xl"></i>
                    </div>
                    <h4 class="tw-font-semibold tw-mb-2">安全认证</h4>
                    <p class="tw-text-sm tw-text-gray-600">JWT token安全认证机制</p>
                </div>
                
                <div class="tw-text-center">
                    <div class="tw-w-12 tw-h-12 tw-bg-red-100 tw-rounded-full tw-flex tw-items-center tw-justify-center tw-mx-auto tw-mb-3">
                        <i class="bi bi-gear tw-text-red-600 tw-text-xl"></i>
                    </div>
                    <h4 class="tw-font-semibold tw-mb-2">丰富功能</h4>
                    <p class="tw-text-sm tw-text-gray-600">Profile管理、设置、历史记录等</p>
                </div>
                
                <div class="tw-text-center">
                    <div class="tw-w-12 tw-h-12 tw-bg-indigo-100 tw-rounded-full tw-flex tw-items-center tw-justify-center tw-mx-auto tw-mb-3">
                        <i class="bi bi-code tw-text-indigo-600 tw-text-xl"></i>
                    </div>
                    <h4 class="tw-font-semibold tw-mb-2">现代技术</h4>
                    <p class="tw-text-sm tw-text-gray-600">ES6+、CSS3、现代浏览器API</p>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const authStatus = document.getElementById('auth-status');
    const displayName = document.getElementById('display-name');
    const displayEmail = document.getElementById('display-email');
    const displayUserId = document.getElementById('display-user-id');
    const displayAvatar = document.getElementById('display-avatar');
    
    // 模拟登录
    document.getElementById('simulate-login').addEventListener('click', function() {
        // 创建模拟的JWT token
        const mockUserData = {
            email: '<EMAIL>',
            display_name: '张小明',
            full_name: '小明 张'
        };
        
        // 模拟token（实际应用中由后端生成）
        const mockToken = btoa(JSON.stringify({
            exp: Math.floor(Date.now() / 1000) + 3600, // 1小时后过期
            email: mockUserData.email
        }));
        
        localStorage.setItem('access_token', `header.${mockToken}.signature`);
        localStorage.setItem('mock_user_data', JSON.stringify(mockUserData));
        
        updateDisplay();
        
        // 触发profile更新
        if (window.userProfile) {
            window.userProfile.checkAuthStatus();
        }
    });
    
    // 模拟登出
    document.getElementById('simulate-logout').addEventListener('click', function() {
        localStorage.removeItem('access_token');
        localStorage.removeItem('refresh_token');
        localStorage.removeItem('mock_user_data');
        
        updateDisplay();
        
        // 触发profile更新
        if (window.userProfile) {
            window.userProfile.checkAuthStatus();
        }
    });
    
    function updateDisplay() {
        const token = localStorage.getItem('access_token');
        const mockUserData = localStorage.getItem('mock_user_data');
        
        if (token && mockUserData) {
            const userData = JSON.parse(mockUserData);
            authStatus.textContent = '已登录';
            authStatus.className = 'tw-text-green-600 tw-font-semibold';
            
            displayName.textContent = userData.display_name;
            displayEmail.textContent = userData.email;
            displayUserId.textContent = userData.email; // 使用邮箱作为标识
            displayAvatar.textContent = '默认头像';
        } else {
            authStatus.textContent = '未登录';
            authStatus.className = 'tw-text-red-600 tw-font-semibold';
            
            displayName.textContent = '-';
            displayEmail.textContent = '-';
            displayUserId.textContent = '-';
            displayAvatar.textContent = '默认头像';
        }
    }
    
    // 初始化显示
    updateDisplay();
});
</script>
{% endblock %}
