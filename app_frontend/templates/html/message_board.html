{% extends 'base.html' %}
{% load static %}

{% block content %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Message Board - HiSage Health</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Merriweather:wght@300;400;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <script src="{% static 'js/global_navigation.js' %}?v=1.0"></script>
    <style>
/* Message Board Styles - Home Page Style */
:root {
    --primary-blue: #2563eb;
    --primary-blue-dark: #1d4ed8;
    --secondary-blue: #3b82f6;
    --accent-teal: #0d9488;
    --accent-green: #059669;
    --text-dark: #1f2937;
    --text-gray: #6b7280;
    --text-light: #9ca3af;
    --bg-light: #f8fafc;
    --bg-white: #ffffff;
    --border-light: #e5e7eb;
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    line-height: 1.6;
    color: var(--text-dark);
    background-color: var(--bg-white);
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.section {
    padding: 80px 0;
}

.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    text-align: center;
    margin-bottom: 1rem;
    color: var(--text-dark);
}

.section-subtitle {
    font-size: 1.25rem;
    text-align: center;
    color: var(--text-gray);
    margin-bottom: 3rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}



/* Card Styles */
.card {
    background: var(--bg-white);
    border-radius: 16px;
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--border-light);
    overflow: hidden;
    transition: all 0.3s ease;
    margin-bottom: 2rem;
}

.card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
}

.card-header {
    padding: 2rem;
    background: var(--bg-light);
    border-bottom: 1px solid var(--border-light);
}

.card-body {
    padding: 2rem;
}

/* Form Styles */
.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: var(--text-dark);
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.form-control {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 2px solid var(--border-light);
    border-radius: 8px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background-color: var(--bg-white);
    color: var(--text-dark);
}

.form-control:focus {
    outline: none;
    border-color: var(--primary-blue);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.form-check {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.form-check-input {
    width: auto;
}

/* Button Styles */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s ease;
    gap: 0.5rem;
}

.btn-primary {
    background-color: var(--primary-blue);
    color: white;
}

.btn-primary:hover {
    background-color: var(--primary-blue-dark);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-secondary {
    background-color: var(--text-gray);
    color: white;
}

.btn-secondary:hover {
    background-color: var(--text-dark);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}



/* Message Styles */
.message-item {
    padding: 2rem;
    border-bottom: 1px solid var(--border-light);
    transition: background-color 0.3s ease;
}

.message-item:hover {
    background-color: var(--bg-light);
}

.message-item:last-child {
    border-bottom: none;
}

.message-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
}

.message-title {
    font-size: 1.3rem;
    font-weight: 600;
    color: var(--primary-blue);
    margin: 0 0 0.75rem 0;
    line-height: 1.3;
    cursor: pointer;
    transition: color 0.3s ease;
}

.message-title:hover {
    color: var(--primary-blue-dark);
    text-decoration: underline;
}

.message-content-preview {
    color: var(--text-gray);
    font-size: 0.95rem;
    line-height: 1.5;
    margin-bottom: 1rem;
    padding: 0.5rem 0;
    border-left: 3px solid var(--border-light);
    padding-left: 1rem;
}

.message-meta {
    font-size: 0.9rem;
    color: var(--text-gray);
    display: flex;
    gap: 1rem;
    align-items: center;
}



.reply-count {
    background-color: var(--primary-blue);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 500;
}

/* Search Styles */
.search-form {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 2rem;
}

.search-form input {
    flex: 1;
    padding: 0.6rem 1rem;
    border: 2px solid var(--border-light);
    border-radius: 8px;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.search-form input:focus {
    outline: none;
    border-color: var(--primary-blue);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.search-form button {
    padding: 0.6rem 1rem;
    background-color: var(--primary-blue);
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s ease;
}

.search-form button:hover {
    background-color: var(--primary-blue-dark);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

/* Pagination Styles */
.pagination {
    display: flex;
    justify-content: center;
    gap: 0.5rem;
    margin: 2rem 0;
}

.pagination a, .pagination span {
    padding: 0.5rem 1rem;
    border: 1px solid var(--border-light);
    border-radius: 8px;
    text-decoration: none;
    color: var(--primary-blue);
    cursor: pointer;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background-color: var(--primary-blue);
    color: white;
    border-color: var(--primary-blue);
    transform: translateY(-1px);
}

.pagination .current {
    background-color: var(--primary-blue);
    color: white;
    border-color: var(--primary-blue);
}

/* Alert Styles */
.alert {
    padding: 1rem;
    border-radius: 8px;
    margin-bottom: 1rem;
    border: 1px solid transparent;
}

.alert-success {
    background-color: #f0fdf4;
    color: var(--accent-green);
    border-color: #bbf7d0;
}

.alert-error {
    background-color: #fef2f2;
    color: #dc2626;
    border-color: #fecaca;
}

/* Modal Styles */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
}

.modal-content {
    background: white;
    border-radius: 16px;
    box-shadow: var(--shadow-xl);
    max-width: 90vw;
    max-height: 90vh;
    width: 800px;
    overflow: hidden;
}

.modal-header {
    background: var(--primary-blue);
    color: white;
    padding: 1.5rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    font-size: 1.5rem;
    font-weight: 600;
}

.modal-close {
    background: none;
    border: none;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 6px;
    transition: background-color 0.3s ease;
}

.modal-close:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.modal-body {
    padding: 2rem;
    max-height: 70vh;
    overflow-y: auto;
}

.message-detail {
    margin-bottom: 2rem;
}

.message-detail-header {
    border-bottom: 1px solid var(--border-light);
    padding-bottom: 1rem;
    margin-bottom: 1.5rem;
}

.message-detail-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 0.5rem;
}

.message-detail-meta {
    color: var(--text-gray);
    font-size: 0.9rem;
}

.message-detail-content {
    font-size: 1rem;
    line-height: 1.6;
    color: var(--text-dark);
    white-space: pre-wrap;
}

.message-detail-image {
    max-width: 100%;
    height: auto;
    border-radius: 8px;
    margin: 1rem 0;
}



/* Responsive Design */
@media (max-width: 768px) {
    .section {
        padding: 60px 0;
    }
    
    .card-header,
    .card-body {
        padding: 1.5rem;
    }
    
    .message-header {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }
    
    .message-meta {
        flex-wrap: wrap;
    }

    .modal-content {
        width: 95vw;
        margin: 1rem;
    }

    .modal-body {
        padding: 1.5rem;
    }

    .search-form {
        flex-direction: column;
    }
}
    </style>
</head>
<body>
<!-- Message Board Section -->
<section class="section">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <!-- Alert Messages -->
                <div id="alert-container"></div>

                <!-- Post New Message Card -->
                <div class="card">
                    <div class="card-header">
                        <h2 class="section-title mb-0">Post a New Message</h2>
                        <p class="section-subtitle mb-0">Share your thoughts, ask questions, and connect with the community</p>
                        <div id="guest-notice" class="alert alert-info mt-2" style="display: none;">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>Welcome!</strong> You can view all messages without logging in. To post messages or reply, please
                            <a href="/login/" class="alert-link" data-login-link="true">login</a> or
                            <a href="/register/" class="alert-link">register</a>.

                        </div>
                    </div>
                    
                    <div class="card-body">
                        <form id="message-form" enctype="multipart/form-data">
                            <div class="form-group">
                                <label for="title" class="form-label">Message Title</label>
                                <input type="text" id="title" name="title" class="form-control" placeholder="Enter message title..." maxlength="200" required>
                            </div>
                            
                            <div class="form-group">
                                <label for="content" class="form-label">Message Content</label>
                                <textarea id="content" name="content" class="form-control" placeholder="Write your message here..." rows="6" required></textarea>
                            </div>
                            
                            <div class="form-group">
                                <label for="image" class="form-label">Attach Image (Optional)</label>
                                <input type="file" id="image" name="image" class="form-control" accept="image/*">
                            </div>
                            
                            <div class="form-check">
                                <input type="checkbox" id="is_anonymous" name="is_anonymous" class="form-check-input" checked>
                                <label for="is_anonymous">Post anonymously</label>
                                <small class="form-text text-muted">Check this box to post anonymously</small>
                            </div>
                            
                            <div id="anonymous-fields" style="display: block;">
                                <div class="form-group">
                                    <label for="anonymous_name" class="form-label">Display Name</label>
                                    <input type="text" id="anonymous_name" name="anonymous_name" class="form-control" placeholder="Your display name (optional)" maxlength="100" value="Anonymous">
                                    <small class="form-text text-muted">Name to display (optional, defaults to 'Anonymous')</small>
                                </div>
                                
                                <div class="form-group">
                                    <label for="anonymous_email" class="form-label">Email Address</label>
                                    <input type="email" id="anonymous_email" name="anonymous_email" class="form-control" placeholder="<EMAIL> (optional)">
                                    <small class="form-text text-muted">Email address (optional, will not be displayed publicly)</small>
                                </div>
                            </div>
                            
                            <button type="submit" class="btn btn-primary" id="submit-btn">
                                <i class="fas fa-paper-plane"></i>Post Message
                            </button>
                        </form>
                    </div>
                </div>

                <!-- Messages List Card -->
                <div class="card">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h2 class="section-title mb-0">Recent Messages</h2>
                                <p class="section-subtitle mb-0">(<span id="total-messages">0</span> total)</p>
                            </div>
                            
                            <!-- Search Form -->
                            <div class="search-form">
                                <input type="text" id="search-input" placeholder="Search messages...">
                                <button type="button" id="search-btn"><i class="fas fa-search"></i></button>
                                <button type="button" id="clear-search-btn" class="btn btn-secondary" style="display: none;">Clear</button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card-body" style="padding: 0;">
                        <div id="messages-container">
                            <!-- Messages will be loaded here -->
                        </div>
                    </div>
                </div>

                <!-- Pagination -->
                <div id="pagination-container" style="display: none;">
                    <!-- Pagination will be loaded here -->
                </div>

                <!-- Loading Spinner -->
                <div id="loading-spinner" style="display: none; text-align: center; padding: 2rem;">
                    <i class="fas fa-spinner fa-spin" style="font-size: 2rem; color: var(--primary-blue);"></i>
                    <p>Loading...</p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Message Detail Modal -->
<div id="message-detail-modal" class="modal-overlay" style="display: none;">
    <div class="modal-content">
        <div class="modal-header">
            <h3 id="modal-title">Message Details</h3>
            <button type="button" class="modal-close" onclick="closeMessageModal()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="modal-body" id="modal-body">
            <!-- Message details will be loaded here -->
        </div>
    </div>
</div>

<script>
// Configuration
window.API_BASE_URL = "{{ API_BASE_URL }}";
window.LOCAL_BASE_URL = "{{ LOCAL_BASE_URL }}";

// Global variables
let currentPage = 1;
let currentSearch = '';
let isLoading = false;

// Authentication functions
function isUserAuthenticated() {
    const token = localStorage.getItem('access_token');
    if (!token || token === 'null' || token === '') {
        return false;
    }

    try {
        const payload = JSON.parse(atob(token.split('.')[1]));
        const currentTime = Date.now() / 1000;

        if (payload.exp < currentTime) {
            console.log('❌ Token expired');
            localStorage.removeItem('access_token');
            localStorage.removeItem('refresh_token');
            localStorage.removeItem('user_info');
            return false;
        }

        return true;
    } catch (error) {
        console.error('❌ Error checking token:', error);
        localStorage.removeItem('access_token');
        localStorage.removeItem('refresh_token');
        localStorage.removeItem('user_info');
        return false;
    }
}

function getCurrentUser() {
    const userInfo = localStorage.getItem('user_info');
    return userInfo ? JSON.parse(userInfo) : null;
}



// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 Message Board initialized');
    console.log('🔐 User authenticated:', isUserAuthenticated());

    // Show appropriate UI based on authentication status
    updateUIForAuthStatus();

    setupMessageForm();
    setupSearchForm();
    setupAnonymousToggle();
    loadMessages();
});

// Update UI based on authentication status
function updateUIForAuthStatus() {
    const guestNotice = document.getElementById('guest-notice');
    const messageForm = document.getElementById('message-form');

    if (!isUserAuthenticated()) {
        // Show guest notice and hide form for unauthenticated users
        guestNotice.style.display = 'block';
        messageForm.style.display = 'none';
    } else {
        // Hide guest notice and show form for authenticated users
        guestNotice.style.display = 'none';
        messageForm.style.display = 'block';
    }
}

// Setup message form
function setupMessageForm() {
    const form = document.getElementById('message-form');
    form.addEventListener('submit', handleMessageSubmit);
}

// Setup search form
function setupSearchForm() {
    const searchBtn = document.getElementById('search-btn');
    const clearBtn = document.getElementById('clear-search-btn');
    const searchInput = document.getElementById('search-input');

    searchBtn.addEventListener('click', handleSearch);
    clearBtn.addEventListener('click', clearSearch);

    searchInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            handleSearch();
        }
    });
}

// Setup anonymous toggle
function setupAnonymousToggle() {
    const anonymousCheckbox = document.getElementById('is_anonymous');
    const anonymousFields = document.getElementById('anonymous-fields');

    function toggleAnonymousFields() {
        if (anonymousCheckbox.checked) {
            anonymousFields.style.display = 'block';
        } else {
            anonymousFields.style.display = 'none';
        }
    }

    if (isUserAuthenticated()) {
        const user = getCurrentUser();
        const label = document.querySelector('label[for="is_anonymous"]');
        if (label && user) {
            label.innerHTML = `Post anonymously <small class="form-text text-muted">Uncheck to post as ${user.first_name || user.email.split('@')[0]}</small>`;
        }
    }

    toggleAnonymousFields();
    anonymousCheckbox.addEventListener('change', toggleAnonymousFields);
}

// Handle message form submission
async function handleMessageSubmit(e) {
    e.preventDefault();

    if (isLoading) return;

    const submitBtn = document.getElementById('submit-btn');
    const originalText = submitBtn.innerHTML;

    try {
        isLoading = true;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Posting...';
        submitBtn.disabled = true;

        const formData = new FormData(e.target);
        const headers = {};
        if (isUserAuthenticated()) {
            const token = localStorage.getItem('access_token');
            headers['Authorization'] = `Bearer ${token}`;
        }

        const response = await fetch(`${window.API_BASE_URL}/api/message-board/`, {
            method: 'POST',
            headers: headers,
            body: formData
        });

        const data = await response.json();

        if (data.success) {
            showAlert('Message posted successfully!', 'success');
            e.target.reset();
            document.getElementById('is_anonymous').checked = true;
            document.getElementById('anonymous_name').value = 'Anonymous';
            setupAnonymousToggle();
            loadMessages();
        } else {
            showAlert('Failed to post message: ' + (data.error || 'Unknown error'), 'error');
        }

    } catch (error) {
        console.error('Error posting message:', error);
        showAlert('Failed to post message. Please try again.', 'error');
    } finally {
        isLoading = false;
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    }
}

// Handle search
function handleSearch() {
    const searchInput = document.getElementById('search-input');
    const searchValue = searchInput.value.trim();

    currentSearch = searchValue;
    currentPage = 1;

    if (searchValue) {
        document.getElementById('clear-search-btn').style.display = 'inline-block';
    }

    loadMessages();
}

// Clear search
function clearSearch() {
    document.getElementById('search-input').value = '';
    document.getElementById('clear-search-btn').style.display = 'none';
    currentSearch = '';
    currentPage = 1;
    loadMessages();
}

// Load messages from API
async function loadMessages() {
    if (isLoading) return;

    try {
        isLoading = true;
        showLoading(true);

        let url = `${window.API_BASE_URL}/api/message-board/?page=${currentPage}`;
        if (currentSearch) {
            url += `&search=${encodeURIComponent(currentSearch)}`;
        }

        const headers = {};
        if (isUserAuthenticated()) {
            const token = localStorage.getItem('access_token');
            headers['Authorization'] = `Bearer ${token}`;
        }

        const response = await fetch(url, {
            method: 'GET',
            headers: headers
        });
        const data = await response.json();

        if (data.success) {
            displayMessages(data.data.messages);
            displayPagination(data.data.pagination);
            document.getElementById('total-messages').textContent = data.data.pagination.total;
        } else {
            showAlert('Failed to load messages: ' + (data.error || 'Unknown error'), 'error');
        }

    } catch (error) {
        console.error('Error loading messages:', error);
        showAlert('Failed to load messages. Please try again.', 'error');
    } finally {
        isLoading = false;
        showLoading(false);
    }
}

// Display messages
function displayMessages(messages) {
    const container = document.getElementById('messages-container');

    if (messages.length === 0) {
        container.innerHTML = `
            <div class="message-item" style="text-align: center; color: var(--text-gray);">
                <i class="fas fa-comments" style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.5;"></i>
                <h4>No messages found</h4>
                ${currentSearch ?
                    `<p>No messages match your search for "${currentSearch}".</p>
                     <button onclick="clearSearch()" class="btn btn-primary">View All Messages</button>` :
                    `<p>${isUserAuthenticated() ?
                        'Be the first to post a message!' :
                        'No messages yet. <a href="/login/" data-login-link="true">Login</a> to post the first message!'
                    }</p>`
                }
            </div>
        `;
        return;
    }

    container.innerHTML = messages.map(message => `
        <div class="message-item">
            <div class="message-header">
                <div style="flex: 1;">
                    <h4 class="message-title" onclick="viewMessageDetail('${message.id}')">
                        ${escapeHtml(message.title)}
                    </h4>
                    <div class="message-content-preview">
                        ${escapeHtml(message.content_preview || message.content).substring(0, 200)}${(message.content_preview || message.content).length > 200 ? '...' : ''}
                    </div>
                    <div class="message-meta">
                        <span><i class="fas fa-user"></i> ${escapeHtml(message.author_name)}</span>
                        <span><i class="fas fa-clock"></i> ${message.time_ago}</span>
                        ${message.replies_count > 0 ?
                            `<span class="reply-count">
                                <i class="fas fa-reply"></i> ${message.replies_count} repl${message.replies_count == 1 ? 'y' : 'ies'}
                            </span>` : ''
                        }
                    </div>
                </div>
            </div>
        </div>
    `).join('');
}

// Show loading spinner
function showLoading(show) {
    const spinner = document.getElementById('loading-spinner');
    spinner.style.display = show ? 'block' : 'none';
}

// Show alert message
function showAlert(message, type) {
    const container = document.getElementById('alert-container');
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type}`;
    alertDiv.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'}"></i>
        ${message}
    `;

    container.appendChild(alertDiv);

    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.parentNode.removeChild(alertDiv);
        }
    }, 5000);
}

// Escape HTML to prevent XSS
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

// View message detail
async function viewMessageDetail(messageId) {
    try {
        showLoading(true);

        const headers = {};
        if (isUserAuthenticated()) {
            const token = localStorage.getItem('access_token');
            headers['Authorization'] = `Bearer ${token}`;
        }

        const response = await fetch(`${window.API_BASE_URL}/api/message-board/${messageId}/`, {
            method: 'GET',
            headers: headers
        });

        const data = await response.json();

        if (data.success) {
            displayMessageDetail(data.data);
            document.getElementById('message-detail-modal').style.display = 'flex';
            document.body.style.overflow = 'hidden';
        } else {
            showAlert('Failed to load message details: ' + (data.error || 'Unknown error'), 'error');
        }

    } catch (error) {
        console.error('Error loading message details:', error);
        showAlert('Failed to load message details. Please try again.', 'error');
    } finally {
        showLoading(false);
    }
}

// Display message detail in modal
function displayMessageDetail(message) {
    const modalTitle = document.getElementById('modal-title');
    const modalBody = document.getElementById('modal-body');

    modalTitle.textContent = 'Message Details';

    modalBody.innerHTML = `
        <div class="message-detail">
            <div class="message-detail-header">
                <h2 class="message-detail-title">${escapeHtml(message.title)}</h2>
                <div class="message-detail-meta">
                    <span><i class="fas fa-user"></i> ${escapeHtml(message.author_name)}</span>
                    <span style="margin-left: 1rem;"><i class="fas fa-clock"></i> ${message.created_at}</span>
                </div>
            </div>
            <div class="message-detail-content">
                ${escapeHtml(message.content)}
            </div>
            ${message.image ? `<img src="${message.image}" alt="Message image" class="message-detail-image">` : ''}
        </div>

        ${message.replies && message.replies.length > 0 ? `
            <div class="replies-section">
                <h3>Replies (${message.replies.length})</h3>
                ${message.replies.map(reply => `
                    <div class="reply-item" style="border-left: 3px solid var(--primary-blue); padding-left: 1rem; margin: 1rem 0;">
                        <div class="reply-meta" style="color: var(--text-gray); font-size: 0.9rem; margin-bottom: 0.5rem;">
                            <span><i class="fas fa-user"></i> ${escapeHtml(reply.author_name)}</span>
                            <span style="margin-left: 1rem;"><i class="fas fa-clock"></i> ${reply.created_at}</span>
                        </div>
                        <div class="reply-content" style="color: var(--text-dark);">
                            ${escapeHtml(reply.content)}
                        </div>
                    </div>
                `).join('')}
            </div>
        ` : ''}

        ${isUserAuthenticated() ? `
            <div class="reply-form-section" style="margin-top: 2rem; padding-top: 2rem; border-top: 1px solid var(--border-light);">
                <h4>Add a Reply</h4>
                <form id="reply-form" data-message-id="${message.id}">
                    <div class="form-group">
                        <textarea id="reply-content" name="content" class="form-control" rows="4" placeholder="Write your reply..." required></textarea>
                    </div>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-reply"></i> Post Reply
                    </button>
                </form>
            </div>
        ` : `
            <div class="login-prompt" style="margin-top: 2rem; padding-top: 2rem; border-top: 1px solid var(--border-light); text-align: center;">
                <p><a href="/login/" class="btn btn-primary" data-login-link="true">Login to Reply</a></p>
            </div>
        `}
    `;

    // Setup reply form if user is authenticated
    if (isUserAuthenticated()) {
        const replyForm = document.getElementById('reply-form');
        if (replyForm) {
            replyForm.addEventListener('submit', handleReplySubmit);
        }
    }
}

// Close message modal
function closeMessageModal() {
    const modal = document.getElementById('message-detail-modal');
    modal.style.display = 'none';
    document.body.style.overflow = '';
}

// Handle reply form submission
async function handleReplySubmit(e) {
    e.preventDefault();

    const messageId = e.target.dataset.messageId;
    const content = document.getElementById('reply-content').value.trim();

    if (!content) {
        showAlert('Please enter a reply message.', 'error');
        return;
    }

    const submitBtn = e.target.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;

    try {
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Posting...';
        submitBtn.disabled = true;

        const headers = {
            'Content-Type': 'application/json'
        };
        if (isUserAuthenticated()) {
            const token = localStorage.getItem('access_token');
            headers['Authorization'] = `Bearer ${token}`;
        }

        const response = await fetch(`${window.API_BASE_URL}/api/message-board/${messageId}/reply/`, {
            method: 'POST',
            headers: headers,
            body: JSON.stringify({ content: content })
        });

        const data = await response.json();

        if (data.success) {
            showAlert('Reply posted successfully!', 'success');
            // Refresh the message detail
            viewMessageDetail(messageId);
            // Refresh the messages list
            loadMessages();
        } else {
            showAlert('Failed to post reply: ' + (data.error || 'Unknown error'), 'error');
        }

    } catch (error) {
        console.error('Error posting reply:', error);
        showAlert('Failed to post reply. Please try again.', 'error');
    } finally {
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    }
}



// Display pagination
function displayPagination(pagination) {
    const container = document.getElementById('pagination-container');

    if (pagination.total_pages <= 1) {
        container.style.display = 'none';
        return;
    }

    container.style.display = 'block';

    let paginationHTML = '<div class="pagination">';

    if (pagination.page > 1) {
        paginationHTML += `
            <span onclick="goToPage(1)">&laquo; First</span>
            <span onclick="goToPage(${pagination.page - 1})">&lsaquo; Previous</span>
        `;
    }

    paginationHTML += `<span class="current">Page ${pagination.page} of ${pagination.total_pages}</span>`;

    if (pagination.page < pagination.total_pages) {
        paginationHTML += `
            <span onclick="goToPage(${pagination.page + 1})">Next &rsaquo;</span>
            <span onclick="goToPage(${pagination.total_pages})">Last &raquo;</span>
        `;
    }

    paginationHTML += '</div>';
    container.innerHTML = paginationHTML;
}

// Go to specific page
function goToPage(page) {
    currentPage = page;
    loadMessages();
}


</script>
</body>
</html>
{% endblock %}
