<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Messages - HiSage Health Admin</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Inter', sans-serif;
        }
        .admin-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
        }
        .message-card {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 1.5rem;
            transition: transform 0.2s;
        }
        .message-card:hover {
            transform: translateY(-2px);
        }
        .message-header {
            background: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
            padding: 1rem 1.5rem;
            border-radius: 10px 10px 0 0;
        }
        .message-body {
            padding: 1.5rem;
        }
        .inquiry-type-badge {
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
        }
        .search-filters {
            background: white;
            border-radius: 10px;
            padding: 1.5rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        .message-meta {
            font-size: 0.9rem;
            color: #6c757d;
        }
        .message-content {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 1rem;
            margin-top: 1rem;
            border-left: 4px solid #667eea;
        }
        .no-messages {
            text-align: center;
            padding: 3rem;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="admin-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h1><i class="fas fa-envelope me-3"></i>Contact Messages</h1>
                    <p class="mb-0">Manage and respond to user inquiries</p>
                </div>
                <div class="col-md-6 text-end">
                    <span class="badge bg-light text-dark">Total Messages: {{ total_messages }}</span>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- Search and Filters -->
        <div class="search-filters">
            <form method="GET" class="row g-3">
                <div class="col-md-4">
                    <label for="search" class="form-label">
                        <i class="fas fa-search me-2"></i>Search Messages
                    </label>
                    <input type="text" class="form-control" id="search" name="search" 
                           value="{{ search_query }}" placeholder="Search by name, email, or content...">
                </div>
                <div class="col-md-3">
                    <label for="type" class="form-label">
                        <i class="fas fa-filter me-2"></i>Message Type
                    </label>
                    <select class="form-control" id="type" name="type">
                        <option value="">All Types</option>
                        {% for choice_value, choice_label in inquiry_choices %}
                            <option value="{{ choice_value }}" {% if inquiry_type == choice_value|stringformat:"s" %}selected{% endif %}>
                                {{ choice_label }}
                            </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-3 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary me-2">
                        <i class="fas fa-search me-1"></i>Filter
                    </button>
                    <a href="{% url 'message_list' %}" class="btn btn-outline-secondary">
                        <i class="fas fa-times me-1"></i>Clear
                    </a>
                </div>
                <div class="col-md-2 d-flex align-items-end">
                    <a href="{% url 'dashboard' %}" class="btn btn-outline-primary">
                        <i class="fas fa-chart-line me-1"></i>Dashboard
                    </a>
                </div>
            </form>
        </div>

        <!-- Error Message -->
        {% if error_message %}
            <div class="alert alert-warning">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <strong>Notice:</strong> {{ error_message }}
            </div>
        {% endif %}

        <!-- Messages List -->
        {% if page_obj and page_obj.object_list %}
            {% for inquiry in page_obj.object_list %}
                <div class="message-card">
                    <div class="message-header">
                        <div class="row align-items-center">
                            <div class="col-md-6">
                                <h5 class="mb-1">
                                    <i class="fas fa-user me-2"></i>{% if inquiry.name %}{{ inquiry.name }}{% else %}Anonymous User{% endif %}
                                </h5>
                                <div class="message-meta">
                                    <i class="fas fa-envelope me-1"></i>{{ inquiry.email }}
                                </div>
                            </div>
                            <div class="col-md-6 text-end">
                                <span class="badge inquiry-type-badge 
                                    {% if inquiry.inquiry_type == 0 %}bg-primary
                                    {% elif inquiry.inquiry_type == 1 %}bg-danger
                                    {% elif inquiry.inquiry_type == 2 %}bg-success
                                    {% elif inquiry.inquiry_type == 3 %}bg-info
                                    {% else %}bg-secondary{% endif %}">
                                    {{ inquiry.get_inquiry_type_display }}
                                </span>
                                <div class="message-meta mt-1">
                                    <i class="fas fa-clock me-1"></i>{{ inquiry.datetime|date:"M d, Y H:i" }}
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="message-body">
                        <div class="message-content">
                            <p class="mb-0">{{ inquiry.description|linebreaks }}</p>
                        </div>
                        <div class="mt-3">
                            <small class="text-muted">
                                Message ID: {{ inquiry.id }} | 
                                Received: {{ inquiry.datetime|timesince }} ago
                            </small>
                        </div>
                    </div>
                </div>
            {% endfor %}

            <!-- Pagination -->
            {% if page_obj.has_other_pages %}
                <nav aria-label="Messages pagination">
                    <ul class="pagination justify-content-center">
                        {% if page_obj.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page=1{% if search_query %}&search={{ search_query }}{% endif %}{% if inquiry_type %}&type={{ inquiry_type }}{% endif %}">
                                    <i class="fas fa-angle-double-left"></i>
                                </a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if inquiry_type %}&type={{ inquiry_type }}{% endif %}">
                                    <i class="fas fa-angle-left"></i>
                                </a>
                            </li>
                        {% endif %}

                        {% for num in page_obj.paginator.page_range %}
                            {% if page_obj.number == num %}
                                <li class="page-item active">
                                    <span class="page-link">{{ num }}</span>
                                </li>
                            {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ num }}{% if search_query %}&search={{ search_query }}{% endif %}{% if inquiry_type %}&type={{ inquiry_type }}{% endif %}">{{ num }}</a>
                                </li>
                            {% endif %}
                        {% endfor %}

                        {% if page_obj.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if inquiry_type %}&type={{ inquiry_type }}{% endif %}">
                                    <i class="fas fa-angle-right"></i>
                                </a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if search_query %}&search={{ search_query }}{% endif %}{% if inquiry_type %}&type={{ inquiry_type }}{% endif %}">
                                    <i class="fas fa-angle-double-right"></i>
                                </a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
            {% endif %}

        {% else %}
            <div class="no-messages">
                <i class="fas fa-inbox fa-3x mb-3"></i>
                <h3>No Messages Found</h3>
                <p>{% if search_query or inquiry_type %}No messages match your search criteria.{% else %}No messages have been received yet.{% endif %}</p>
                {% if search_query or inquiry_type %}
                    <a href="{% url 'message_list' %}" class="btn btn-primary">View All Messages</a>
                {% endif %}
            </div>
        {% endif %}
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
