{% load static %}
{% load custom_tags %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Settings API Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 2rem;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 2rem;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 1rem;
        }
        .status {
            padding: 1rem;
            border-radius: 4px;
            margin: 1rem 0;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        pre { background: #f8f9fa; padding: 1rem; border-radius: 4px; overflow-x: auto; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 4px;
            cursor: pointer;
            margin: 0.5rem;
        }
        button:hover { background: #0056b3; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Settings API Test</h1>
        
        <h2>Configuration</h2>
        <div id="config-status" class="status warning">Testing configuration...</div>
        <pre id="config-details"></pre>

        <h2>Authentication</h2>
        <div id="auth-status" class="status warning">Testing authentication...</div>
        <pre id="auth-details"></pre>

        <h2>User Profile API</h2>
        <div id="api-status" class="status warning">Testing user profile API...</div>
        <pre id="api-response"></pre>

        <h2>Actions</h2>
        <button onclick="testUserProfile()">Test User Profile API</button>
        <button onclick="testWithFallback()">Test with Fallback</button>
        <button onclick="window.location.href='/user/settings/'">Go to Settings</button>
    </div>

    <!-- Configuration -->
    <script>
        // 使用Django模板变量传递配置
        window.API_BASE_URL = "{{ API_BASE_URL }}";
        window.LOCAL_BASE_URL = "{{ LOCAL_BASE_URL }}";
        window.CSRF_TOKEN = "{{ csrf_token }}";

        // 备用配置（如果模板变量为空）
        if (!window.API_BASE_URL || window.API_BASE_URL === '') {
            window.API_BASE_URL = "http://**************:8001";
        }
        if (!window.LOCAL_BASE_URL || window.LOCAL_BASE_URL === '') {
            window.LOCAL_BASE_URL = "http://**************:8000";
        }

        console.log('🔧 Settings test configuration:', {
            API_BASE_URL: window.API_BASE_URL,
            LOCAL_BASE_URL: window.LOCAL_BASE_URL,
            CSRF_TOKEN: window.CSRF_TOKEN ? 'Present' : 'Missing'
        });
    </script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            testConfiguration();
            testAuthentication();
        });

        function testConfiguration() {
            const configStatus = document.getElementById('config-status');
            const configDetails = document.getElementById('config-details');
            
            const config = {
                API_BASE_URL: window.API_BASE_URL,
                LOCAL_BASE_URL: window.LOCAL_BASE_URL,
                CSRF_TOKEN: window.CSRF_TOKEN ? 'Present' : 'Missing',
                access_token: localStorage.getItem('access_token') ? 'Present' : 'Missing'
            };
            
            configDetails.textContent = JSON.stringify(config, null, 2);
            
            if (window.API_BASE_URL && window.API_BASE_URL !== '') {
                configStatus.className = 'status success';
                configStatus.textContent = '✅ Configuration loaded successfully';
            } else {
                configStatus.className = 'status error';
                configStatus.textContent = '❌ Configuration failed - API_BASE_URL missing';
            }
        }

        function testAuthentication() {
            const authStatus = document.getElementById('auth-status');
            const authDetails = document.getElementById('auth-details');
            
            const token = localStorage.getItem('access_token');
            
            if (!token) {
                authStatus.className = 'status error';
                authStatus.textContent = '❌ No authentication token found';
                authDetails.textContent = 'Please log in first';
                return;
            }

            try {
                // Simple JWT decode (without verification)
                const payload = JSON.parse(atob(token.split('.')[1]));
                const now = Date.now() / 1000;
                
                authDetails.textContent = JSON.stringify({
                    user_id: payload.user_id,
                    email: payload.email,
                    exp: new Date(payload.exp * 1000).toISOString(),
                    expired: payload.exp < now
                }, null, 2);
                
                if (payload.exp < now) {
                    authStatus.className = 'status error';
                    authStatus.textContent = '❌ Authentication token expired';
                } else {
                    authStatus.className = 'status success';
                    authStatus.textContent = '✅ Authentication token valid';
                }
            } catch (error) {
                authStatus.className = 'status error';
                authStatus.textContent = '❌ Invalid authentication token';
                authDetails.textContent = error.message;
            }
        }

        async function testUserProfile() {
            const apiStatus = document.getElementById('api-status');
            const apiResponse = document.getElementById('api-response');
            
            apiStatus.className = 'status warning';
            apiStatus.textContent = '🔄 Testing user profile API...';
            
            const token = localStorage.getItem('access_token');
            
            if (!token) {
                apiStatus.className = 'status error';
                apiStatus.textContent = '❌ No authentication token';
                apiResponse.textContent = 'Please log in first';
                return;
            }

            try {
                const response = await fetch(`${window.API_BASE_URL}/api/user/profile/`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                const responseText = await response.text();
                
                apiResponse.textContent = `Status: ${response.status}\n\nResponse:\n${responseText}`;
                
                if (response.ok) {
                    apiStatus.className = 'status success';
                    apiStatus.textContent = '✅ User profile API working';
                } else {
                    apiStatus.className = 'status error';
                    apiStatus.textContent = `❌ API Error: ${response.status}`;
                }
            } catch (error) {
                apiStatus.className = 'status error';
                apiStatus.textContent = '❌ Network error';
                apiResponse.textContent = `Error: ${error.message}`;
            }
        }

        async function testWithFallback() {
            const apiStatus = document.getElementById('api-status');
            const apiResponse = document.getElementById('api-response');
            
            apiStatus.className = 'status warning';
            apiStatus.textContent = '🔄 Testing with fallback data...';
            
            // Simulate fallback user data
            const fallbackData = {
                success: true,
                user: {
                    id: 'test-user-id',
                    email: '<EMAIL>',
                    first_name: 'Test',
                    last_name: 'User',
                    date_joined: new Date().toISOString(),
                    avatar: null
                }
            };
            
            apiResponse.textContent = JSON.stringify(fallbackData, null, 2);
            apiStatus.className = 'status success';
            apiStatus.textContent = '✅ Fallback data ready';
        }
    </script>
</body>
</html>
