<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Registration</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; }
        input { width: 300px; padding: 8px; }
        button { padding: 10px 20px; background: #007cba; color: white; border: none; cursor: pointer; }
        .message { margin-top: 20px; padding: 10px; border-radius: 4px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <h1>Test Registration (Isolated)</h1>
    
    <form id="testRegisterForm">
        <div class="form-group">
            <label for="email">Email:</label>
            <input type="email" id="email" name="email" required>
        </div>
        
        <div class="form-group">
            <label for="password">Password:</label>
            <input type="password" id="password" name="password" required>
        </div>
        
        <div class="form-group">
            <label for="password_confirm">Confirm Password:</label>
            <input type="password" id="password_confirm" name="password_confirm" required>
        </div>
        
        <div class="form-group">
            <label for="first_name">First Name:</label>
            <input type="text" id="first_name" name="first_name">
        </div>
        
        <div class="form-group">
            <label for="last_name">Last Name:</label>
            <input type="text" id="last_name" name="last_name">
        </div>
        
        <button type="submit" id="submitBtn">Register</button>
    </form>
    
    <div id="message"></div>
    
    <script>
        // 简单的API调用，不使用AuthAPI类
        document.getElementById('testRegisterForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            console.log('🧪 Test registration form submitted');
            
            const formData = new FormData(e.target);
            const userData = {
                email: formData.get('email'),
                password: formData.get('password'),
                password_confirm: formData.get('password_confirm'),
                first_name: formData.get('first_name') || '',
                last_name: formData.get('last_name') || ''
            };
            
            const submitBtn = document.getElementById('submitBtn');
            const messageDiv = document.getElementById('message');
            
            // 禁用按钮
            submitBtn.disabled = true;
            submitBtn.textContent = 'Registering...';
            
            try {
                console.log('🚀 Making direct API call to register');
                
                const response = await fetch('http://127.0.0.1:8001/api/register/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(userData)
                });
                
                console.log('📊 Response status:', response.status);
                
                const data = await response.json();
                console.log('📊 Response data:', data);
                
                if (response.status === 201 && data.success) {
                    messageDiv.innerHTML = `<div class="message success">Registration successful! ${data.message}</div>`;
                } else {
                    let errorMsg = data.message || 'Registration failed';
                    if (data.errors) {
                        const errors = Object.values(data.errors).flat();
                        errorMsg = errors.join(', ');
                    }
                    messageDiv.innerHTML = `<div class="message error">Registration failed: ${errorMsg}</div>`;
                }
                
            } catch (error) {
                console.error('❌ Registration error:', error);
                messageDiv.innerHTML = `<div class="message error">Network error: ${error.message}</div>`;
            } finally {
                submitBtn.disabled = false;
                submitBtn.textContent = 'Register';
            }
        });
    </script>
</body>
</html>
