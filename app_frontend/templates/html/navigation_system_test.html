{% extends 'html/base.html' %}
{% load static %}

{% block title %}Global Navigation System Test{% endblock %}

{% block content %}
<div class="container" style="max-width: 1000px; margin: 100px auto; padding: 2rem;">
    <h1 style="text-align: center; color: #2563eb; margin-bottom: 2rem;">
        🌐 Global Navigation System Test v2.0
    </h1>
    
    <div class="alert alert-info" style="background: #e0f2fe; border: 1px solid #0288d1; padding: 1rem; border-radius: 8px; margin-bottom: 2rem;">
        <h4>🧪 Test Instructions</h4>
        <p>This page tests the new global navigation system. All navigation should now:</p>
        <ul>
            <li>Check authentication automatically before navigating to protected pages</li>
            <li>Redirect to login if authentication is required but user is not logged in</li>
            <li>Return to the intended page after successful login</li>
            <li>Work consistently across all pages</li>
        </ul>
    </div>

    <!-- Authentication Status -->
    <div class="test-section" style="background: white; padding: 1.5rem; margin: 1rem 0; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
        <h3>🔐 Authentication Status</h3>
        <div id="auth-status" style="padding: 1rem; border-radius: 4px; margin: 1rem 0;">
            <strong>Status:</strong> <span id="auth-text">Checking...</span>
        </div>
        <button onclick="checkAuthStatus()" style="background: #2563eb; color: white; border: none; padding: 0.5rem 1rem; border-radius: 4px; cursor: pointer;">
            Refresh Status
        </button>
    </div>

    <!-- Login/Logout Actions -->
    <div class="test-section" style="background: white; padding: 1.5rem; margin: 1rem 0; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
        <h3>🔑 Authentication Actions</h3>
        <div style="display: flex; gap: 1rem; flex-wrap: wrap;">
            <button onclick="goToLogin()" style="background: #059669; color: white; border: none; padding: 0.5rem 1rem; border-radius: 4px; cursor: pointer;">
                Go to Login
            </button>
            <button onclick="testLogout()" style="background: #dc2626; color: white; border: none; padding: 0.5rem 1rem; border-radius: 4px; cursor: pointer;">
                Test Logout
            </button>
        </div>
    </div>

    <!-- Public Navigation Tests -->
    <div class="test-section" style="background: white; padding: 1.5rem; margin: 1rem 0; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
        <h3>🌍 Public Pages (No Authentication Required)</h3>
        <div style="display: flex; gap: 1rem; flex-wrap: wrap;">
            <a href="/about/" data-nav-link="true" style="background: #6b7280; color: white; padding: 0.5rem 1rem; border-radius: 4px; text-decoration: none;">
                About Us
            </a>
            <a href="/contact-us/" data-nav-link="true" style="background: #6b7280; color: white; padding: 0.5rem 1rem; border-radius: 4px; text-decoration: none;">
                Contact Us
            </a>
            <a href="/message-board/" data-nav-link="true" style="background: #6b7280; color: white; padding: 0.5rem 1rem; border-radius: 4px; text-decoration: none;">
                Message Board
            </a>
        </div>
    </div>

    <!-- Protected Navigation Tests -->
    <div class="test-section" style="background: white; padding: 1.5rem; margin: 1rem 0; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
        <h3>🔒 Protected Pages (Authentication Required)</h3>
        <div style="display: flex; gap: 1rem; flex-wrap: wrap;">
            <a href="/profile/" data-auth-required="true" style="background: #dc2626; color: white; padding: 0.5rem 1rem; border-radius: 4px; text-decoration: none;">
                User Profile
            </a>
            <a href="/audio_upload/history/" data-auth-required="true" style="background: #dc2626; color: white; padding: 0.5rem 1rem; border-radius: 4px; text-decoration: none;">
                Audio History
            </a>
            <a href="/audio_upload/" data-auth-required="true" style="background: #dc2626; color: white; padding: 0.5rem 1rem; border-radius: 4px; text-decoration: none;">
                Audio Upload
            </a>
            <a href="/notifications/" data-auth-required="true" style="background: #dc2626; color: white; padding: 0.5rem 1rem; border-radius: 4px; text-decoration: none;">
                Notifications
            </a>
            <a href="/dashboard/" data-auth-required="true" style="background: #dc2626; color: white; padding: 0.5rem 1rem; border-radius: 4px; text-decoration: none;">
                Dashboard
            </a>
        </div>
    </div>

    <!-- JavaScript Function Tests -->
    <div class="test-section" style="background: white; padding: 1.5rem; margin: 1rem 0; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
        <h3>⚡ JavaScript Function Tests</h3>
        <div style="display: flex; gap: 1rem; flex-wrap: wrap;">
            <button onclick="navigateTo('/about/', false)" style="background: #6b7280; color: white; border: none; padding: 0.5rem 1rem; border-radius: 4px; cursor: pointer;">
                Navigate to About (No Auth)
            </button>
            <button onclick="navigateTo('/profile/', true)" style="background: #dc2626; color: white; border: none; padding: 0.5rem 1rem; border-radius: 4px; cursor: pointer;">
                Navigate to Profile (Auth Required)
            </button>
            <button onclick="navigateTo('/audio_upload/', true)" style="background: #dc2626; color: white; border: none; padding: 0.5rem 1rem; border-radius: 4px; cursor: pointer;">
                Navigate to Audio Upload (Auth Required)
            </button>
        </div>
    </div>

    <!-- Test Results -->
    <div class="test-section" style="background: white; padding: 1.5rem; margin: 1rem 0; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
        <h3>📋 Test Results</h3>
        <div id="test-results" style="background: #f8f9fa; padding: 1rem; border-radius: 4px; min-height: 100px; font-family: monospace; white-space: pre-wrap;">
Test results will appear here...
        </div>
        <button onclick="clearResults()" style="background: #6b7280; color: white; border: none; padding: 0.5rem 1rem; border-radius: 4px; cursor: pointer; margin-top: 1rem;">
            Clear Results
        </button>
    </div>
</div>

<script>
// Test functions
function checkAuthStatus() {
    const authStatus = document.getElementById('auth-status');
    const authText = document.getElementById('auth-text');
    const results = document.getElementById('test-results');
    
    const isAuth = globalNav ? globalNav.isAuthenticated() : false;
    const token = localStorage.getItem('access_token');
    const returnUrl = sessionStorage.getItem('navigation_return_url');
    
    if (isAuth) {
        authStatus.style.background = '#d4edda';
        authStatus.style.color = '#155724';
        authText.textContent = 'Authenticated ✅';
    } else {
        authStatus.style.background = '#f8d7da';
        authStatus.style.color = '#721c24';
        authText.textContent = 'Not Authenticated ❌';
    }
    
    const timestamp = new Date().toLocaleTimeString();
    results.textContent += `[${timestamp}] Authentication Check:\n`;
    results.textContent += `  - Is Authenticated: ${isAuth}\n`;
    results.textContent += `  - Has Token: ${!!token}\n`;
    results.textContent += `  - Token Preview: ${token ? token.substring(0, 30) + '...' : 'None'}\n`;
    results.textContent += `  - Saved Return URL: ${returnUrl || 'None'}\n`;
    results.textContent += `  - Current URL: ${window.location.href}\n\n`;
    
    results.scrollTop = results.scrollHeight;
}

function testLogout() {
    const results = document.getElementById('test-results');
    const timestamp = new Date().toLocaleTimeString();
    
    localStorage.removeItem('access_token');
    localStorage.removeItem('refresh_token');
    localStorage.removeItem('user_info');
    sessionStorage.removeItem('navigation_return_url');
    
    results.textContent += `[${timestamp}] Logout Test: Cleared all tokens and storage\n\n`;
    results.scrollTop = results.scrollHeight;
    
    checkAuthStatus();
}

function clearResults() {
    document.getElementById('test-results').textContent = 'Test results cleared...\n\n';
}

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    console.log('🧪 Navigation test page loaded');
    checkAuthStatus();
    
    // Update status every 10 seconds
    setInterval(checkAuthStatus, 10000);
});
</script>

<style>
.test-section {
    border-left: 4px solid #2563eb;
}

.test-section h3 {
    color: #2563eb;
    margin-bottom: 1rem;
}

.alert {
    border-left: 4px solid #0288d1;
}

.alert h4 {
    color: #0288d1;
    margin-bottom: 0.5rem;
}
</style>
{% endblock %}
