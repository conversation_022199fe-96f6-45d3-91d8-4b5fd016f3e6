{% extends 'html/base.html' %}
{% load static %}

{% block title %}Profile Dropdown Test - HiSage Health{% endblock %}

{% block content %}
<div class="tw-container tw-mx-auto tw-px-4 tw-py-8">
    <div class="tw-max-w-4xl tw-mx-auto">
        <h1 class="tw-text-3xl tw-font-bold tw-text-gray-900 tw-mb-8">Profile下拉菜单测试</h1>
        
        <!-- 测试说明 -->
        <div class="tw-bg-blue-50 tw-border tw-border-blue-200 tw-rounded-lg tw-p-6 tw-mb-8">
            <h2 class="tw-text-xl tw-font-semibold tw-text-blue-900 tw-mb-4">测试说明</h2>
            <div class="tw-text-blue-800">
                <p class="tw-mb-4">这个页面用于测试修复后的Profile下拉菜单：</p>
                <ol class="tw-list-decimal tw-list-inside tw-space-y-2">
                    <li>点击右上角的"模拟登录"按钮</li>
                    <li>观察右上角出现用户头像</li>
                    <li>点击用户头像打开下拉菜单</li>
                    <li>验证下拉菜单不被遮挡</li>
                    <li>测试在不同屏幕尺寸下的表现</li>
                </ol>
            </div>
        </div>
        
        <!-- 快速登录按钮 -->
        <div class="tw-bg-white tw-rounded-lg tw-shadow-md tw-p-6 tw-mb-8">
            <h3 class="tw-text-lg tw-font-semibold tw-mb-4">快速测试</h3>
            
            <div class="tw-flex tw-gap-4 tw-flex-wrap">
                <button id="quick-login" class="tw-bg-green-600 tw-text-white tw-py-2 tw-px-4 tw-rounded-lg hover:tw-bg-green-700 tw-transition-colors">
                    🚀 模拟登录
                </button>
                
                <button id="quick-logout" class="tw-bg-red-600 tw-text-white tw-py-2 tw-px-4 tw-rounded-lg hover:tw-bg-red-700 tw-transition-colors">
                    🚪 模拟登出
                </button>
                
                <button id="toggle-dropdown" class="tw-bg-blue-600 tw-text-white tw-py-2 tw-px-4 tw-rounded-lg hover:tw-bg-blue-700 tw-transition-colors">
                    📋 切换下拉菜单
                </button>
            </div>
        </div>
        
        <!-- 修复内容说明 -->
        <div class="tw-bg-white tw-rounded-lg tw-shadow-md tw-p-6 tw-mb-8">
            <h3 class="tw-text-lg tw-font-semibold tw-mb-4">🔧 修复内容</h3>
            
            <div class="tw-grid tw-grid-cols-1 md:tw-grid-cols-2 tw-gap-6">
                <div>
                    <h4 class="tw-font-semibold tw-text-green-600 tw-mb-2">✅ 已修复的问题</h4>
                    <ul class="tw-text-sm tw-space-y-1">
                        <li>• 下拉菜单被其他元素遮挡</li>
                        <li>• z-index层级不够高</li>
                        <li>• 定位计算不准确</li>
                        <li>• 移动端显示异常</li>
                        <li>• 点击外部区域无法关闭</li>
                    </ul>
                </div>
                
                <div>
                    <h4 class="tw-font-semibold tw-text-blue-600 tw-mb-2">🚀 新增功能</h4>
                    <ul class="tw-text-sm tw-space-y-1">
                        <li>• 使用fixed定位确保显示</li>
                        <li>• 添加透明遮罩层</li>
                        <li>• 智能位置计算</li>
                        <li>• 响应式设计优化</li>
                        <li>• 更高的z-index (9999)</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <!-- 技术细节 -->
        <div class="tw-bg-gray-50 tw-rounded-lg tw-p-6">
            <h3 class="tw-text-lg tw-font-semibold tw-mb-4">🛠️ 技术实现</h3>
            
            <div class="tw-space-y-4 tw-text-sm">
                <div>
                    <strong>定位方式：</strong>
                    <code class="tw-bg-gray-200 tw-px-2 tw-py-1 tw-rounded">position: fixed</code>
                    - 相对于视窗定位，不受父元素影响
                </div>
                
                <div>
                    <strong>层级设置：</strong>
                    <code class="tw-bg-gray-200 tw-px-2 tw-py-1 tw-rounded">z-index: 9999</code>
                    - 确保在所有元素之上
                </div>
                
                <div>
                    <strong>遮罩层：</strong>
                    <code class="tw-bg-gray-200 tw-px-2 tw-py-1 tw-rounded">z-index: 9998</code>
                    - 透明遮罩，点击关闭下拉菜单
                </div>
                
                <div>
                    <strong>智能定位：</strong>
                    动态计算位置，防止超出屏幕边界
                </div>
                
                <div>
                    <strong>响应式设计：</strong>
                    移动端自适应宽度和位置
                </div>
            </div>
        </div>
        
        <!-- 测试区域 -->
        <div class="tw-mt-8 tw-h-96 tw-bg-gradient-to-br tw-from-purple-400 tw-to-pink-400 tw-rounded-lg tw-flex tw-items-center tw-justify-center tw-text-white">
            <div class="tw-text-center">
                <h3 class="tw-text-2xl tw-font-bold tw-mb-4">测试区域</h3>
                <p>这个区域用于测试下拉菜单是否会被遮挡</p>
                <p class="tw-mt-2 tw-text-sm tw-opacity-75">点击右上角的用户头像测试下拉菜单</p>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // 快速登录
    document.getElementById('quick-login').addEventListener('click', function() {
        // 创建模拟token
        const payload = {
            email: '<EMAIL>',
            exp: Math.floor(Date.now() / 1000) + 3600
        };
        
        const mockToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.' + btoa(JSON.stringify(payload)) + '.signature';
        
        localStorage.setItem('access_token', mockToken);
        
        // 触发profile更新
        if (window.userProfile) {
            window.userProfile.checkAuthStatus();
        }
        
        // 显示成功消息
        this.textContent = '✅ 已登录';
        this.classList.remove('tw-bg-green-600', 'hover:tw-bg-green-700');
        this.classList.add('tw-bg-green-500');
        
        setTimeout(() => {
            this.textContent = '🚀 模拟登录';
            this.classList.remove('tw-bg-green-500');
            this.classList.add('tw-bg-green-600', 'hover:tw-bg-green-700');
        }, 2000);
    });
    
    // 快速登出
    document.getElementById('quick-logout').addEventListener('click', function() {
        localStorage.removeItem('access_token');
        localStorage.removeItem('refresh_token');
        
        if (window.userProfile) {
            window.userProfile.checkAuthStatus();
        }
        
        // 显示成功消息
        this.textContent = '✅ 已登出';
        this.classList.remove('tw-bg-red-600', 'hover:tw-bg-red-700');
        this.classList.add('tw-bg-red-500');
        
        setTimeout(() => {
            this.textContent = '🚪 模拟登出';
            this.classList.remove('tw-bg-red-500');
            this.classList.add('tw-bg-red-600', 'hover:tw-bg-red-700');
        }, 2000);
    });
    
    // 切换下拉菜单
    document.getElementById('toggle-dropdown').addEventListener('click', function() {
        if (window.userProfile) {
            window.userProfile.toggleDropdown();
        }
    });
});
</script>
{% endblock %}
