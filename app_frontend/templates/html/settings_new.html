{% extends 'html/base.html' %}
{% load static %}

{% block title %}账户设置 - 认知健康{% endblock %}

{% block content %}
<div class="tw-min-h-screen tw-bg-gray-50 tw-py-8">
    <div class="tw-max-w-4xl tw-mx-auto tw-px-4 sm:tw-px-6 lg:tw-px-8">
        <!-- 页面标题 -->
        <div class="tw-mb-8">
            <h1 class="tw-text-3xl tw-font-bold tw-text-gray-900">账户设置</h1>
            <p class="tw-mt-2 tw-text-gray-600">管理您的账户安全和个人偏好</p>
        </div>

        <div class="tw-grid tw-grid-cols-1 lg:tw-grid-cols-3 tw-gap-8">
            <!-- 侧边导航 -->
            <div class="lg:tw-col-span-1">
                <nav class="tw-bg-white tw-shadow tw-rounded-lg tw-p-6">
                    <ul class="tw-space-y-2">
                        <li>
                            <a href="#profile" class="settings-nav-item active" data-section="profile">
                                <i class="fas fa-user tw-mr-3"></i>
                                个人信息
                            </a>
                        </li>
                        <li>
                            <a href="#security" class="settings-nav-item" data-section="security">
                                <i class="fas fa-lock tw-mr-3"></i>
                                安全设置
                            </a>
                        </li>
                        <li>
                            <a href="#avatar" class="settings-nav-item" data-section="avatar">
                                <i class="fas fa-image tw-mr-3"></i>
                                头像设置
                            </a>
                        </li>
                    </ul>
                </nav>
            </div>

            <!-- 主要内容区域 -->
            <div class="lg:tw-col-span-2">
                <!-- 个人信息设置 -->
                <div id="profile-section" class="settings-section">
                    <div class="tw-bg-white tw-shadow tw-rounded-lg tw-p-6">
                        <h2 class="tw-text-xl tw-font-semibold tw-text-gray-900 tw-mb-6">个人信息</h2>
                        
                        <form id="profile-form" class="tw-space-y-6">
                            <div class="tw-grid tw-grid-cols-1 sm:tw-grid-cols-2 tw-gap-6">
                                <div>
                                    <label for="first-name" class="tw-block tw-text-sm tw-font-medium tw-text-gray-700">名字</label>
                                    <input type="text" id="first-name" name="first_name" 
                                           class="tw-mt-1 tw-block tw-w-full tw-border-gray-300 tw-rounded-md tw-shadow-sm tw-focus:tw-ring-blue-500 tw-focus:tw-border-blue-500">
                                </div>
                                
                                <div>
                                    <label for="last-name" class="tw-block tw-text-sm tw-font-medium tw-text-gray-700">姓氏</label>
                                    <input type="text" id="last-name" name="last_name" 
                                           class="tw-mt-1 tw-block tw-w-full tw-border-gray-300 tw-rounded-md tw-shadow-sm tw-focus:tw-ring-blue-500 tw-focus:tw-border-blue-500">
                                </div>
                            </div>
                            
                            <div>
                                <label for="email-display" class="tw-block tw-text-sm tw-font-medium tw-text-gray-700">邮箱地址</label>
                                <input type="email" id="email-display" disabled 
                                       class="tw-mt-1 tw-block tw-w-full tw-border-gray-300 tw-rounded-md tw-shadow-sm tw-bg-gray-50 tw-text-gray-500">
                                <p class="tw-mt-2 tw-text-sm tw-text-gray-500">邮箱地址不可修改</p>
                            </div>
                            
                            <div class="tw-flex tw-justify-end">
                                <button type="submit" 
                                        class="tw-bg-blue-600 tw-text-white tw-px-6 tw-py-2 tw-rounded-md tw-hover:tw-bg-blue-700 tw-transition-colors">
                                    保存更改
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- 安全设置 -->
                <div id="security-section" class="settings-section tw-hidden">
                    <div class="tw-bg-white tw-shadow tw-rounded-lg tw-p-6">
                        <h2 class="tw-text-xl tw-font-semibold tw-text-gray-900 tw-mb-6">安全设置</h2>
                        
                        <form id="password-form" class="tw-space-y-6">
                            <div>
                                <label for="old-password" class="tw-block tw-text-sm tw-font-medium tw-text-gray-700">当前密码</label>
                                <input type="password" id="old-password" name="old_password" required
                                       class="tw-mt-1 tw-block tw-w-full tw-border-gray-300 tw-rounded-md tw-shadow-sm tw-focus:tw-ring-blue-500 tw-focus:tw-border-blue-500">
                            </div>
                            
                            <div>
                                <label for="new-password" class="tw-block tw-text-sm tw-font-medium tw-text-gray-700">新密码</label>
                                <input type="password" id="new-password" name="new_password" required minlength="8"
                                       class="tw-mt-1 tw-block tw-w-full tw-border-gray-300 tw-rounded-md tw-shadow-sm tw-focus:tw-ring-blue-500 tw-focus:tw-border-blue-500">
                                <p class="tw-mt-2 tw-text-sm tw-text-gray-500">密码至少8个字符</p>
                            </div>
                            
                            <div>
                                <label for="confirm-password" class="tw-block tw-text-sm tw-font-medium tw-text-gray-700">确认新密码</label>
                                <input type="password" id="confirm-password" name="new_password_confirm" required
                                       class="tw-mt-1 tw-block tw-w-full tw-border-gray-300 tw-rounded-md tw-shadow-sm tw-focus:tw-ring-blue-500 tw-focus:tw-border-blue-500">
                            </div>
                            
                            <div class="tw-flex tw-justify-end">
                                <button type="submit" 
                                        class="tw-bg-red-600 tw-text-white tw-px-6 tw-py-2 tw-rounded-md tw-hover:tw-bg-red-700 tw-transition-colors">
                                    修改密码
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- 头像设置 -->
                <div id="avatar-section" class="settings-section tw-hidden">
                    <div class="tw-bg-white tw-shadow tw-rounded-lg tw-p-6">
                        <h2 class="tw-text-xl tw-font-semibold tw-text-gray-900 tw-mb-6">头像设置</h2>
                        
                        <div class="tw-space-y-6">
                            <div class="tw-flex tw-items-center tw-space-x-6">
                                <div class="tw-relative">
                                    <img id="current-avatar" src="{% static 'assets/images/default-avatar.svg' %}" 
                                         alt="当前头像" class="tw-w-24 tw-h-24 tw-rounded-full tw-object-cover">
                                </div>
                                <div>
                                    <h3 class="tw-text-lg tw-font-medium tw-text-gray-900">当前头像</h3>
                                    <p class="tw-text-sm tw-text-gray-500">支持 JPG、PNG 格式，建议尺寸 200x200 像素</p>
                                </div>
                            </div>
                            
                            <div>
                                <label for="avatar-upload" class="tw-block tw-text-sm tw-font-medium tw-text-gray-700 tw-mb-2">
                                    选择新头像
                                </label>
                                <input type="file" id="avatar-upload" accept="image/*" 
                                       class="tw-block tw-w-full tw-text-sm tw-text-gray-500 tw-file:tw-mr-4 tw-file:tw-py-2 tw-file:tw-px-4 tw-file:tw-rounded-md tw-file:tw-border-0 tw-file:tw-text-sm tw-file:tw-font-medium tw-file:tw-bg-blue-50 tw-file:tw-text-blue-700 tw-hover:tw-file:tw-bg-blue-100">
                            </div>
                            
                            <div class="tw-flex tw-justify-end tw-space-x-3">
                                <button type="button" id="remove-avatar-btn"
                                        class="tw-bg-gray-600 tw-text-white tw-px-6 tw-py-2 tw-rounded-md tw-hover:tw-bg-gray-700 tw-transition-colors">
                                    移除头像
                                </button>
                                <button type="button" id="upload-avatar-btn" disabled
                                        class="tw-bg-blue-600 tw-text-white tw-px-6 tw-py-2 tw-rounded-md tw-hover:tw-bg-blue-700 tw-transition-colors tw-disabled:tw-opacity-50">
                                    上传头像
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 消息提示 -->
<div id="message-container" class="tw-fixed tw-top-4 tw-right-4 tw-z-50"></div>

<style>
.settings-nav-item {
    @apply tw-flex tw-items-center tw-px-3 tw-py-2 tw-text-sm tw-font-medium tw-text-gray-700 tw-rounded-md tw-hover:tw-bg-gray-100 tw-transition-colors;
}

.settings-nav-item.active {
    @apply tw-bg-blue-100 tw-text-blue-700;
}

.settings-section {
    @apply tw-transition-all tw-duration-300;
}
</style>

<script>
// 从Django传递API配置到前端
window.API_CONFIG = {
    API_BASE_URL: '{{ API_BASE_URL }}/api',
    LOCAL_BASE_URL: '{{ LOCAL_BASE_URL }}'
};
</script>
<script src="{% static 'js/auth_api.js' %}?v=2.0"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const authAPI = new AuthAPI();
    let selectedAvatarFile = null;
    
    // 检查登录状态
    if (!authAPI.isLoggedIn()) {
        window.location.href = '/login/';
        return;
    }

    // 初始化
    loadUserProfile();
    bindEvents();

    async function loadUserProfile() {
        try {
            const response = await authAPI.getUserProfile();
            if (response.success) {
                updateProfileDisplay(response.data);
            }
        } catch (error) {
            console.error('加载用户资料失败:', error);
            showMessage('加载用户资料失败', 'error');
        }
    }

    function updateProfileDisplay(userData) {
        // 更新个人信息表单
        document.getElementById('first-name').value = userData.first_name || '';
        document.getElementById('last-name').value = userData.last_name || '';
        document.getElementById('email-display').value = userData.email || '';
        
        // 更新头像
        if (userData.avatar) {
            document.getElementById('current-avatar').src = userData.avatar;
        }
    }

    function bindEvents() {
        // 导航切换
        document.querySelectorAll('.settings-nav-item').forEach(item => {
            item.addEventListener('click', handleNavClick);
        });

        // 表单提交
        document.getElementById('profile-form').addEventListener('submit', handleProfileSave);
        document.getElementById('password-form').addEventListener('submit', handlePasswordChange);

        // 头像相关
        document.getElementById('avatar-upload').addEventListener('change', handleAvatarSelect);
        document.getElementById('upload-avatar-btn').addEventListener('click', handleAvatarUpload);
        document.getElementById('remove-avatar-btn').addEventListener('click', handleAvatarRemove);
    }

    function handleNavClick(event) {
        event.preventDefault();
        
        const targetSection = event.currentTarget.dataset.section;
        
        // 更新导航状态
        document.querySelectorAll('.settings-nav-item').forEach(item => {
            item.classList.remove('active');
        });
        event.currentTarget.classList.add('active');
        
        // 显示对应的设置区域
        document.querySelectorAll('.settings-section').forEach(section => {
            section.classList.add('tw-hidden');
        });
        document.getElementById(`${targetSection}-section`).classList.remove('tw-hidden');
    }

    async function handleProfileSave(event) {
        event.preventDefault();
        
        const formData = new FormData(event.target);
        const profileData = {
            first_name: formData.get('first_name'),
            last_name: formData.get('last_name')
        };

        try {
            const response = await authAPI.updateUserProfile(profileData);
            if (response.success) {
                showMessage('个人信息更新成功', 'success');
                loadUserProfile();
            } else {
                showMessage('个人信息更新失败', 'error');
            }
        } catch (error) {
            console.error('个人信息更新失败:', error);
            showMessage('个人信息更新失败', 'error');
        }
    }

    async function handlePasswordChange(event) {
        event.preventDefault();
        
        const formData = new FormData(event.target);
        const passwordData = {
            oldPassword: formData.get('old_password'),
            newPassword: formData.get('new_password'),
            newPasswordConfirm: formData.get('new_password_confirm')
        };

        // 验证密码确认
        if (passwordData.newPassword !== passwordData.newPasswordConfirm) {
            showMessage('两次输入的新密码不一致', 'error');
            return;
        }

        try {
            const response = await authAPI.changePassword(passwordData);
            if (response.success) {
                showMessage('密码修改成功', 'success');
                event.target.reset();
            } else {
                showMessage('密码修改失败', 'error');
            }
        } catch (error) {
            console.error('密码修改失败:', error);
            showMessage('密码修改失败', 'error');
        }
    }

    function handleAvatarSelect(event) {
        const file = event.target.files[0];
        if (file) {
            selectedAvatarFile = file;
            document.getElementById('upload-avatar-btn').disabled = false;
            
            // 预览头像
            const reader = new FileReader();
            reader.onload = function(e) {
                document.getElementById('current-avatar').src = e.target.result;
            };
            reader.readAsDataURL(file);
        }
    }

    async function handleAvatarUpload() {
        if (!selectedAvatarFile) return;

        try {
            const response = await authAPI.updateUserProfile({ avatar: selectedAvatarFile });
            if (response.success) {
                showMessage('头像上传成功', 'success');
                selectedAvatarFile = null;
                document.getElementById('upload-avatar-btn').disabled = true;
                document.getElementById('avatar-upload').value = '';
                loadUserProfile();
            } else {
                showMessage('头像上传失败', 'error');
            }
        } catch (error) {
            console.error('头像上传失败:', error);
            showMessage('头像上传失败', 'error');
        }
    }

    async function handleAvatarRemove() {
        try {
            const response = await authAPI.updateUserProfile({ avatar: null });
            if (response.success) {
                showMessage('头像移除成功', 'success');
                document.getElementById('current-avatar').src = '{% static "assets/images/default-avatar.svg" %}';
                loadUserProfile();
            } else {
                showMessage('头像移除失败', 'error');
            }
        } catch (error) {
            console.error('头像移除失败:', error);
            showMessage('头像移除失败', 'error');
        }
    }

    function showMessage(message, type) {
        const container = document.getElementById('message-container');
        const messageDiv = document.createElement('div');
        messageDiv.className = `tw-p-4 tw-rounded-md tw-mb-4 tw-transition-all tw-duration-300 ${
            type === 'success' ? 'tw-bg-green-100 tw-text-green-800' : 'tw-bg-red-100 tw-text-red-800'
        }`;
        messageDiv.textContent = message;
        
        container.appendChild(messageDiv);
        
        setTimeout(() => {
            messageDiv.remove();
        }, 3000);
    }
});
</script>
{% endblock %}
