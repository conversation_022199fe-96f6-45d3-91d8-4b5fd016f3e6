{% load static %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Set New Password - Cognitive Health</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            position: relative;
            overflow: hidden;
        }

        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/><circle cx="10" cy="60" r="0.5" fill="white" opacity="0.1"/><circle cx="90" cy="40" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            pointer-events: none;
        }

        .confirm-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 24px;
            box-shadow: 0 32px 64px rgba(0, 0, 0, 0.12), 0 0 0 1px rgba(255, 255, 255, 0.2);
            overflow: hidden;
            width: 100%;
            max-width: 440px;
            animation: slideUp 0.8s cubic-bezier(0.16, 1, 0.3, 1);
            position: relative;
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(40px) scale(0.96);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        .confirm-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 48px 32px 40px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .confirm-header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }

        .confirm-header h1 {
            font-size: 28px;
            margin-bottom: 8px;
            font-weight: 700;
            letter-spacing: -0.02em;
            position: relative;
            z-index: 1;
        }

        .confirm-header p {
            opacity: 0.9;
            font-size: 16px;
            font-weight: 400;
            position: relative;
            z-index: 1;
        }

        .confirm-form {
            padding: 28px 32px;
        }

        .form-group {
            margin-bottom: 18px;
            position: relative;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #374151;
            font-size: 14px;
            letter-spacing: -0.01em;
        }

        .form-group input {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 400;
            transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
            background: #ffffff;
            color: #111827;
        }

        .form-group input:focus {
            outline: none;
            border-color: #667eea;
            background: #ffffff;
            box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
            transform: translateY(-1px);
        }

        .form-group input::placeholder {
            color: #9ca3af;
            font-weight: 400;
        }

        .confirm-btn {
            width: 100%;
            padding: 12px 24px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
            margin-bottom: 18px;
            position: relative;
            overflow: hidden;
        }

        .confirm-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .confirm-btn:hover::before {
            left: 100%;
        }

        .confirm-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 20px 40px rgba(102, 126, 234, 0.3);
        }

        .confirm-btn:active {
            transform: translateY(0);
        }

        .confirm-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .alert {
            padding: 12px 16px;
            border-radius: 12px;
            margin-bottom: 16px;
            display: flex;
            align-items: center;
            gap: 12px;
            font-weight: 500;
            font-size: 14px;
        }

        .alert-success {
            background: #ecfdf5;
            color: #065f46;
            border: 1px solid #a7f3d0;
        }

        .alert-error {
            background: #fef2f2;
            color: #991b1b;
            border: 1px solid #fecaca;
        }

        .password-requirements {
            background: #f3f4f6;
            border: 1px solid #d1d5db;
            color: #6b7280;
            padding: 14px;
            border-radius: 8px;
            margin-bottom: 18px;
            font-size: 14px;
        }

        .password-requirements ul {
            margin: 10px 0 0 20px;
        }

        .password-requirements li {
            margin-bottom: 5px;
        }

        .back-links {
            text-align: center;
            margin-top: 16px;
            padding-top: 16px;
            border-top: 1px solid #e5e7eb;
        }

        .back-links p {
            color: #6b7280;
            font-size: 14px;
        }

        .back-links a {
            color: #667eea;
            text-decoration: none;
            font-weight: 600;
            transition: color 0.2s ease;
        }

        .back-links a:hover {
            color: #5a67d8;
            text-decoration: underline;
        }

        /* Back button removed */

        .required {
            color: #ef4444;
        }

        @media (max-width: 768px) {
            body {
                padding: 16px;
            }

            .confirm-container {
                border-radius: 20px;
            }

            .confirm-header {
                padding: 40px 24px 32px;
            }

            .confirm-header h1 {
                font-size: 28px;
            }

            .confirm-form {
                padding: 32px 24px;
            }

            /* Back button removed */
        }
    </style>
</head>
<body>
    <div class="confirm-container">
        <div class="confirm-header">
            <h1><i class="fas fa-brain"></i> HiSage</h1>
            <p>Set Your New Password</p>
        </div>

        <div class="confirm-form">
            <div class="password-requirements">
                <strong><i class="fas fa-shield-alt"></i> Password Requirements:</strong>
                <ul>
                    <li>At least 8 characters</li>
                    <li>Contains uppercase and lowercase letters</li>
                    <li>Contains numbers</li>
                    <li>Recommended to include special characters</li>
                </ul>
            </div>

            <div id="confirmMessage"></div>

            <form id="passwordResetConfirmForm">
                <input type="hidden" id="token" name="token">
                
                <div class="form-group">
                    <label for="password">New Password <span class="required">*</span></label>
                    <input type="password" id="password" name="password" required placeholder="Enter new password">
                </div>

                <div class="form-group">
                    <label for="password_confirm">Confirm New Password <span class="required">*</span></label>
                    <input type="password" id="password_confirm" name="password_confirm" required placeholder="Enter new password again">
                </div>

                <button type="submit" id="confirmBtn" class="confirm-btn">
                    <i class="fas fa-check"></i> Reset Password
                </button>
            </form>

            <div class="back-links">
                <p>Remember your password? <a href="/login">Sign In</a></p>
            </div>
        </div>
    </div>

    <script>
        // Pass API configuration from Django to frontend
        window.API_BASE_URL = "{{ API_BASE_URL }}";
        window.LOCAL_BASE_URL = "{{ LOCAL_BASE_URL }}";
        window.API_CONFIG = {
            API_BASE_URL: '{{ API_BASE_URL }}/api',
            LOCAL_BASE_URL: '{{ LOCAL_BASE_URL }}'
        };
        // Mark this page has custom event handlers to prevent auth_api.js from duplicate binding
        window.customAuthHandlers = true;
    </script>
    <script src="{% static 'js/auth_api.js' %}?v=2.0"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🔧 Password reset confirm page DOMContentLoaded - customAuthHandlers:', window.customAuthHandlers);

            const urlParams = new URLSearchParams(window.location.search);
            const token = urlParams.get('token');
            const form = document.getElementById('passwordResetConfirmForm');
            const confirmBtn = document.getElementById('confirmBtn');
            const messageDiv = document.getElementById('confirmMessage');
            const authAPI = new AuthAPI();

            let isSubmitting = false; // Prevent duplicate submission

            // Get token from URL parameters
            if (token) {
                document.getElementById('token').value = token;
            } else {
                messageDiv.innerHTML = `
                    <div class="alert alert-error">
                        <i class="fas fa-exclamation-circle"></i>
                        Invalid reset link, please request password reset again.
                    </div>
                `;
                form.style.display = 'none';
                return;
            }

            // Check if event listener already added
            if (form.dataset.listenerAdded) {
                console.log('⚠️ Event listener already added, skipping');
                return;
            }

            // Mark event listener as added
            form.dataset.listenerAdded = 'true';
            console.log('✅ Adding password reset confirm event listener');

            // Handle form submission
            form.addEventListener('submit', async function(e) {
                e.preventDefault();
                console.log('📝 Password reset confirm form submitted');

                if (isSubmitting) {
                    console.log('⚠️ Form already submitting, ignoring');
                    return;
                }

                const token = document.getElementById('token').value;
                const password = document.getElementById('password').value;
                const passwordConfirm = document.getElementById('password_confirm').value;

                // Show loading state
                isSubmitting = true;
                confirmBtn.disabled = true;
                confirmBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Resetting...';
                messageDiv.innerHTML = '';

                try {
                    console.log('🔄 Sending password reset confirm request');
                    const response = await authAPI.confirmPasswordReset(token, password, passwordConfirm);
                    console.log('📨 Password reset confirm response:', response);

                    if (response.success) {
                        console.log('✅ Password reset successful');
                        messageDiv.innerHTML = `
                            <div class="alert alert-success">
                                <i class="fas fa-check-circle"></i>
                                ${response.message}
                            </div>
                        `;
                        form.reset();

                        // Redirect to login page after 3 seconds
                        setTimeout(() => {
                            window.location.href = '/login?message=Password reset successful, please log in with new password';
                        }, 3000);
                    } else {
                        console.log('❌ Password reset failed:', response);
                        let errorMessage = response.message || 'Reset failed';
                        if (response.errors) {
                            const errors = Object.values(response.errors).flat();
                            errorMessage = errors.join('<br>');
                        }
                        messageDiv.innerHTML = `
                            <div class="alert alert-error">
                                <i class="fas fa-exclamation-circle"></i>
                                ${errorMessage}
                            </div>
                        `;
                    }
                } catch (error) {
                    console.error('❌ Password reset confirm error:', error);
                    messageDiv.innerHTML = `
                        <div class="alert alert-error">
                            <i class="fas fa-exclamation-circle"></i>
                            Network error, please try again.
                        </div>
                    `;
                } finally {
                    isSubmitting = false;
                    confirmBtn.disabled = false;
                    confirmBtn.innerHTML = '<i class="fas fa-check"></i> Reset Password';
                }
            });
        });
    </script>
</body>
</html>
