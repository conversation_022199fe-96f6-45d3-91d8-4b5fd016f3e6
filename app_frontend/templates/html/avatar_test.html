<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Avatar Upload Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 0 auto;
            padding: 2rem;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 2rem;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 1rem;
        }
        .status {
            padding: 1rem;
            border-radius: 4px;
            margin: 1rem 0;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        pre { background: #f8f9fa; padding: 1rem; border-radius: 4px; overflow-x: auto; font-size: 0.9em; }
        .avatar-preview {
            width: 100px;
            height: 100px;
            border-radius: 50%;
            border: 2px solid #ddd;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 1rem 0;
            background: #f0f0f0;
        }
        .avatar-preview img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 50%;
        }
        input[type="file"] {
            margin: 1rem 0;
            padding: 0.5rem;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 4px;
            cursor: pointer;
            margin: 0.5rem;
        }
        button:hover { background: #0056b3; }
        button:disabled { background: #ccc; cursor: not-allowed; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Avatar Upload Test</h1>
        
        <h2>Current User Info</h2>
        <div id="user-status" class="status warning">Loading user info...</div>
        <pre id="user-info"></pre>

        <h2>Avatar Upload</h2>
        <div class="avatar-preview" id="avatar-preview">
            <span>No Avatar</span>
        </div>
        
        <input type="file" id="avatar-input" accept="image/*">
        <br>

        <h2>Authentication Test</h2>
        <div id="auth-status" class="status warning">Testing authentication...</div>
        <pre id="auth-details"></pre>

        <button onclick="testAuth()" style="background: #28a745;">Test Backend Auth</button>
        <button onclick="uploadAvatar()" id="upload-btn" disabled>Upload Avatar</button>

        <h2>Upload Status</h2>
        <div id="upload-status" class="status warning">Ready to upload</div>
        <pre id="upload-response"></pre>

        <div style="margin-top: 2rem;">
            <a href="/user/settings/" style="
                display: inline-block;
                background: #28a745;
                color: white;
                padding: 0.75rem 1.5rem;
                text-decoration: none;
                border-radius: 4px;
            ">Go to Settings</a>
        </div>
    </div>

    <script>
        // 使用Django模板变量传递配置
        window.API_BASE_URL = "{{ API_BASE_URL }}";
        window.LOCAL_BASE_URL = "{{ LOCAL_BASE_URL }}";
        window.CSRF_TOKEN = "{{ csrf_token }}";
        
        // 备用配置
        if (!window.LOCAL_BASE_URL || window.LOCAL_BASE_URL === '') {
            window.LOCAL_BASE_URL = "http://**************:8000";
        }

        let selectedFile = null;

        document.addEventListener('DOMContentLoaded', function() {
            loadUserInfo();
            testAuth();

            document.getElementById('avatar-input').addEventListener('change', function(e) {
                selectedFile = e.target.files[0];
                if (selectedFile) {
                    document.getElementById('upload-btn').disabled = false;

                    // 预览图片
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        document.getElementById('avatar-preview').innerHTML =
                            `<img src="${e.target.result}" alt="Preview">`;
                    };
                    reader.readAsDataURL(selectedFile);
                } else {
                    document.getElementById('upload-btn').disabled = true;
                }
            });
        });

        async function loadUserInfo() {
            const userStatus = document.getElementById('user-status');
            const userInfo = document.getElementById('user-info');
            
            try {
                const response = await fetch(`${window.LOCAL_BASE_URL}/user/api/profile/`, {
                    credentials: 'same-origin'
                });
                
                if (response.ok) {
                    const data = await response.json();
                    userStatus.className = 'status success';
                    userStatus.textContent = '✅ User info loaded';
                    userInfo.textContent = JSON.stringify(data, null, 2);
                    
                    // 显示当前头像
                    let avatarUrl = data.avatar_url || data.user?.avatar;
                    if (avatarUrl && avatarUrl !== '/static/assets/images/default-avatar.png') {
                        // 确保URL是完整的
                        if (avatarUrl.startsWith('/media/')) {
                            avatarUrl = window.API_BASE_URL + avatarUrl;
                        }
                        document.getElementById('avatar-preview').innerHTML =
                            `<img src="${avatarUrl}" alt="Current avatar">`;
                    }
                } else {
                    userStatus.className = 'status error';
                    userStatus.textContent = '❌ Failed to load user info';
                    userInfo.textContent = `Error: ${response.status} ${response.statusText}`;
                }
            } catch (error) {
                userStatus.className = 'status error';
                userStatus.textContent = '❌ Network error';
                userInfo.textContent = error.message;
            }
        }

        async function uploadAvatar() {
            if (!selectedFile) return;

            const uploadStatus = document.getElementById('upload-status');
            const uploadResponse = document.getElementById('upload-response');
            const uploadBtn = document.getElementById('upload-btn');

            uploadBtn.disabled = true;
            uploadStatus.className = 'status warning';
            uploadStatus.textContent = '🔄 Uploading...';

            const formData = new FormData();
            formData.append('avatar', selectedFile);

            try {
                // 使用后端API进行头像上传
                const uploadUrl = `${window.API_BASE_URL}/api/user/profile/`;
                console.log('Uploading to:', uploadUrl);

                // 获取认证token
                const token = localStorage.getItem('access_token');
                if (!token) {
                    throw new Error('No authentication token found. Please log in first.');
                }
                console.log('Auth Token:', token ? 'Present' : 'Missing');

                const response = await fetch(uploadUrl, {
                    method: 'PUT',
                    headers: {
                        'Authorization': `Bearer ${token}`
                    },
                    body: formData
                });

                const responseText = await response.text();
                console.log('Response:', response.status, responseText);

                if (response.ok) {
                    const data = JSON.parse(responseText);
                    uploadStatus.className = 'status success';
                    uploadStatus.textContent = '✅ Upload successful';
                    uploadResponse.textContent = JSON.stringify(data, null, 2);

                    // 更新头像预览
                    let avatarUrl = data.user?.avatar || data.avatar_url || data.avatar;
                    if (avatarUrl) {
                        // 确保URL是完整的
                        if (avatarUrl.startsWith('/media/')) {
                            avatarUrl = window.API_BASE_URL + avatarUrl;
                        }
                        // 添加时间戳避免缓存
                        avatarUrl += '?t=' + Date.now();
                        document.getElementById('avatar-preview').innerHTML =
                            `<img src="${avatarUrl}" alt="Uploaded avatar">`;
                    }
                } else {
                    uploadStatus.className = 'status error';
                    uploadStatus.textContent = `❌ Upload failed: ${response.status}`;
                    uploadResponse.textContent = responseText;

                    // 特殊处理认证错误
                    if (response.status === 401) {
                        uploadStatus.textContent += ' - Authentication required';
                        uploadResponse.textContent += '\n\nPlease log in to the backend first.';
                    }
                }
            } catch (error) {
                uploadStatus.className = 'status error';
                uploadStatus.textContent = '❌ Upload error';
                uploadResponse.textContent = error.message;
            } finally {
                uploadBtn.disabled = false;
            }
        }

        async function testAuth() {
            const authStatus = document.getElementById('auth-status');
            const authDetails = document.getElementById('auth-details');

            authStatus.className = 'status warning';
            authStatus.textContent = '🔄 Testing backend authentication...';

            const token = localStorage.getItem('access_token');

            if (!token) {
                authStatus.className = 'status error';
                authStatus.textContent = '❌ No authentication token found';
                authDetails.textContent = 'Please log in to the backend first.\nGo to: ' + window.API_BASE_URL + '/admin/';
                return;
            }

            try {
                // 测试后端用户资料API
                const response = await fetch(`${window.API_BASE_URL}/api/user/profile/`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });

                const responseText = await response.text();

                if (response.ok) {
                    const data = JSON.parse(responseText);
                    authStatus.className = 'status success';
                    authStatus.textContent = '✅ Backend authentication successful';
                    authDetails.textContent = JSON.stringify(data, null, 2);
                } else {
                    authStatus.className = 'status error';
                    authStatus.textContent = `❌ Backend authentication failed: ${response.status}`;
                    authDetails.textContent = responseText;

                    if (response.status === 401) {
                        authDetails.textContent += '\n\nToken may be expired. Please log in again.';
                    }
                }
            } catch (error) {
                authStatus.className = 'status error';
                authStatus.textContent = '❌ Authentication test error';
                authDetails.textContent = error.message;
            }
        }
    </script>
</body>
</html>
