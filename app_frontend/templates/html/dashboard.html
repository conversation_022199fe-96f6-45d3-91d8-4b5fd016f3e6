<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HiSage Health - Data Dashboard</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Inter', sans-serif;
        }
        .dashboard-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
        }
        .stat-card {
            background: white;
            border-radius: 10px;
            padding: 1.5rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 1.5rem;
            transition: transform 0.2s;
        }
        .stat-card:hover {
            transform: translateY(-2px);
        }
        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }
        .stat-label {
            color: #6c757d;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        .chart-container {
            background: white;
            border-radius: 10px;
            padding: 1.5rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 1.5rem;
        }
        .activity-item {
            padding: 0.75rem;
            border-bottom: 1px solid #eee;
        }
        .activity-item:last-child {
            border-bottom: none;
        }
        .loading {
            text-align: center;
            padding: 2rem;
            color: #6c757d;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            padding: 1rem;
            border-radius: 5px;
            margin: 1rem 0;
        }
        .login-required {
            text-align: center;
            padding: 3rem;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .user-info {
            font-size: 0.9rem;
        }
        .user-email {
            font-weight: bold;
            color: #495057;
        }
        .user-name {
            color: #6c757d;
            font-size: 0.85rem;
        }
        .user-date {
            color: #6c757d;
            font-size: 0.8rem;
        }
        .analysis-item, .donation-item {
            background: #f8f9fa;
            border-radius: 4px;
            padding: 0.5rem;
            margin-bottom: 0.5rem;
            font-size: 0.85rem;
        }
        .analysis-item:last-child, .donation-item:last-child {
            margin-bottom: 0;
        }
        .status-badge {
            font-size: 0.75rem;
        }
        .table td {
            vertical-align: top;
            padding: 1rem 0.75rem;
        }
        .summary-stats {
            background: #e9ecef;
            border-radius: 4px;
            padding: 0.5rem;
            margin-bottom: 0.5rem;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="dashboard-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h1><i class="fas fa-chart-line me-3"></i>HiSage Health - Data Dashboard</h1>
                    <p class="mb-0">Welcome to the administrative dashboard</p>
                </div>
                <div class="col-md-6 text-end">
                    <a href="/message/" class="btn btn-outline-light me-2">
                        <i class="fas fa-envelope me-1"></i>Messages
                    </a>
                    <a href="/" class="btn btn-outline-light me-2">
                        <i class="fas fa-home me-1"></i>Home
                    </a>
                    <span id="last-updated" class="badge bg-light text-dark">Loading...</span>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- Login Required Message -->
        <div id="login-required" class="login-required" style="display: none;">
            <i class="fas fa-lock fa-3x mb-3 text-muted"></i>
            <h3>Authentication Required</h3>
            <p>You need to be logged in with admin privileges to access this dashboard.</p>
            <a href="/login/" class="btn btn-primary">Login</a>
        </div>

        <!-- Dashboard Content -->
        <div id="dashboard-content" style="display: none;">
            <!-- Statistics Cards -->
            <div class="row" id="stats-cards">
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-number text-primary" id="total-users">-</div>
                        <div class="stat-label">Total Users</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-number text-success" id="active-users">-</div>
                        <div class="stat-label">Active Users</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-number text-info" id="total-analyses">-</div>
                        <div class="stat-label">Audio Analyses</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-number text-warning" id="total-donations">$-</div>
                        <div class="stat-label">Total Donations</div>
                    </div>
                </div>
            </div>

            <!-- User List Section -->
            <div class="row">
                <div class="col-12">
                    <div class="chart-container">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h5><i class="fas fa-users me-2"></i>User Information List</h5>
                            <div>
                                <button class="btn btn-sm btn-outline-primary" onclick="loadUserList(1)">
                                    <i class="fas fa-refresh me-1"></i>Refresh
                                </button>
                            </div>
                        </div>

                        <!-- Loading indicator -->
                        <div id="user-list-loading" class="loading">Loading user data...</div>

                        <!-- User list container -->
                        <div id="user-list-container" style="display: none;">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead class="table-light">
                                        <tr>
                                            <th>User Info</th>
                                            <th>Audio Analyses</th>
                                            <th>Donations</th>
                                            <th>Status</th>
                                        </tr>
                                    </thead>
                                    <tbody id="user-list-tbody">
                                        <!-- User data will be populated here -->
                                    </tbody>
                                </table>
                            </div>

                            <!-- Pagination -->
                            <nav aria-label="User list pagination">
                                <ul class="pagination justify-content-center" id="pagination-container">
                                    <!-- Pagination will be populated here -->
                                </ul>
                            </nav>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Configuration
        const API_BASE_URL = '{{ API_BASE_URL }}';
        const LOCAL_BASE_URL = '{{ LOCAL_BASE_URL }}';
        
        // Global variables
        let charts = {};
        
        // Utility functions
        function formatDate(dateString) {
            const date = new Date(dateString);
            return date.toLocaleDateString('en-US');
        }
        
        function formatDateTime(dateString) {
            const date = new Date(dateString);
            return date.toLocaleString('en-US');
        }
        
        function showError(message) {
            console.error('Dashboard Error:', message);
            // You can add error notification UI here
        }
        
        function getAuthToken() {
            return localStorage.getItem('access_token');
        }
        
        function isAuthenticated() {
            const token = getAuthToken();
            return token && token !== 'null' && token !== '';
        }
        
        async function makeAuthenticatedRequest(url, options = {}) {
            const token = getAuthToken();
            if (!token) {
                throw new Error('No authentication token found');
            }
            
            const headers = {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json',
                ...options.headers
            };
            
            const response = await fetch(url, {
                ...options,
                headers
            });
            
            if (response.status === 401) {
                // Token expired or invalid
                localStorage.removeItem('access_token');
                localStorage.removeItem('refresh_token');
                throw new Error('Authentication required');
            }
            
            return response;
        }
        
        // Check authentication and show appropriate content
        function checkAuthentication() {
            if (!isAuthenticated()) {
                document.getElementById('login-required').style.display = 'block';
                document.getElementById('dashboard-content').style.display = 'none';
                return false;
            } else {
                document.getElementById('login-required').style.display = 'none';
                document.getElementById('dashboard-content').style.display = 'block';
                return true;
            }
        }
        
        // Load user statistics (simplified for stats cards only)
        async function loadUserStats() {
            try {
                const response = await makeAuthenticatedRequest(`${API_BASE_URL}/api/dashboard/user-stats/`);
                const result = await response.json();

                if (result.success) {
                    const data = result.data;

                    // Update statistics cards
                    document.getElementById('total-users').textContent = data.total_users;
                    document.getElementById('active-users').textContent = data.active_users;
                } else {
                    showError(result.error || 'Failed to load user statistics');
                }
            } catch (error) {
                if (error.message === 'Authentication required') {
                    checkAuthentication();
                } else {
                    showError('Failed to load user statistics: ' + error.message);
                }
            }
        }
        
        // Load analysis statistics (simplified for stats cards only)
        async function loadAnalysisStats() {
            try {
                const response = await makeAuthenticatedRequest(`${API_BASE_URL}/api/dashboard/analysis-stats/`);
                const result = await response.json();

                if (result.success) {
                    const data = result.data;
                    document.getElementById('total-analyses').textContent = data.total_analyses;
                } else {
                    showError(result.error || 'Failed to load analysis statistics');
                }
            } catch (error) {
                if (error.message === 'Authentication required') {
                    checkAuthentication();
                } else {
                    showError('Failed to load analysis statistics: ' + error.message);
                }
            }
        }

        // Load donation statistics (simplified for stats cards only)
        async function loadDonationStats() {
            try {
                const response = await makeAuthenticatedRequest(`${API_BASE_URL}/api/dashboard/donation-stats/`);
                const result = await response.json();

                if (result.success) {
                    const data = result.data;
                    document.getElementById('total-donations').textContent = '$' + data.total_amount.toFixed(2);
                } else {
                    showError(result.error || 'Failed to load donation statistics');
                }
            } catch (error) {
                if (error.message === 'Authentication required') {
                    checkAuthentication();
                } else {
                    showError('Failed to load donation statistics: ' + error.message);
                }
            }
        }

        // Load user list with detailed information
        async function loadUserList(page = 1) {
            try {
                document.getElementById('user-list-loading').style.display = 'block';
                document.getElementById('user-list-container').style.display = 'none';

                const response = await makeAuthenticatedRequest(`${API_BASE_URL}/api/dashboard/user-list/?page=${page}&page_size=20`);
                const result = await response.json();

                if (result.success) {
                    const data = result.data;
                    const tbody = document.getElementById('user-list-tbody');

                    // Clear existing content
                    tbody.innerHTML = '';

                    // Populate user data
                    data.users.forEach(user => {
                        const row = document.createElement('tr');

                        // User Info Column
                        const userInfoHtml = `
                            <div class="user-info">
                                <div class="user-email">${user.email}</div>
                                <div class="user-name">${user.full_name || 'Name not set'}</div>
                                <div class="user-date">Joined: ${formatDate(user.date_joined)}</div>
                                ${user.last_login ? `<div class="user-date">Last login: ${formatDate(user.last_login)}</div>` : '<div class="user-date">Never logged in</div>'}
                            </div>
                        `;

                        // Audio Analyses Column
                        const analysesHtml = `
                            <div class="summary-stats">
                                <strong>Total: ${user.analyses.total}</strong> | Completed: ${user.analyses.completed}
                            </div>
                            ${user.analyses.recent.map(analysis => `
                                <div class="analysis-item">
                                    <div><strong>${analysis.filename}</strong></div>
                                    <div>Speaker: ${analysis.relationship} (Age: ${analysis.age})</div>
                                    <div class="d-flex justify-content-between align-items-center">
                                        <small>${formatDateTime(analysis.upload_time)}</small>
                                        <span class="badge status-badge ${analysis.status === 'completed' ? 'bg-success' : analysis.status === 'processing' ? 'bg-warning' : 'bg-danger'}">${analysis.status}</span>
                                    </div>
                                </div>
                            `).join('')}
                            ${user.analyses.recent.length === 0 ? '<div class="text-muted">No analyses yet</div>' : ''}
                        `;

                        // Donations Column
                        const donationsHtml = `
                            <div class="summary-stats">
                                <strong>Total: $${user.donations.total_amount.toFixed(2)}</strong> | Count: ${user.donations.count}
                            </div>
                            ${user.donations.recent.map(donation => `
                                <div class="donation-item">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <strong>$${donation.amount.toFixed(2)}</strong>
                                        <span class="badge status-badge ${donation.status === 'completed' ? 'bg-success' : 'bg-warning'}">${donation.status}</span>
                                    </div>
                                    <small>${formatDateTime(donation.created_at)}</small>
                                    ${donation.completed_at ? `<div><small>Completed: ${formatDateTime(donation.completed_at)}</small></div>` : ''}
                                </div>
                            `).join('')}
                            ${user.donations.recent.length === 0 ? '<div class="text-muted">No donations yet</div>' : ''}
                        `;

                        // Status Column
                        const statusHtml = `
                            <div class="text-center">
                                <span class="badge ${user.is_active ? 'bg-success' : 'bg-secondary'} mb-2">
                                    ${user.is_active ? 'Active' : 'Inactive'}
                                </span>
                                <div class="small text-muted">
                                    ID: ${user.id.substring(0, 8)}...
                                </div>
                            </div>
                        `;

                        row.innerHTML = `
                            <td>${userInfoHtml}</td>
                            <td>${analysesHtml}</td>
                            <td>${donationsHtml}</td>
                            <td>${statusHtml}</td>
                        `;

                        tbody.appendChild(row);
                    });

                    // Update pagination
                    updatePagination(data.pagination);

                    document.getElementById('user-list-loading').style.display = 'none';
                    document.getElementById('user-list-container').style.display = 'block';
                } else {
                    showError(result.error || 'Failed to load user list');
                }
            } catch (error) {
                if (error.message === 'Authentication required') {
                    checkAuthentication();
                } else {
                    showError('Failed to load user list: ' + error.message);
                }
                document.getElementById('user-list-loading').style.display = 'none';
            }
        }

        // Update pagination controls
        function updatePagination(pagination) {
            const container = document.getElementById('pagination-container');
            container.innerHTML = '';

            if (pagination.total_pages <= 1) {
                return;
            }

            // Previous button
            const prevLi = document.createElement('li');
            prevLi.className = `page-item ${pagination.page <= 1 ? 'disabled' : ''}`;
            prevLi.innerHTML = `<a class="page-link" href="#" onclick="loadUserList(${pagination.page - 1}); return false;">Previous</a>`;
            container.appendChild(prevLi);

            // Page numbers
            const startPage = Math.max(1, pagination.page - 2);
            const endPage = Math.min(pagination.total_pages, pagination.page + 2);

            for (let i = startPage; i <= endPage; i++) {
                const li = document.createElement('li');
                li.className = `page-item ${i === pagination.page ? 'active' : ''}`;
                li.innerHTML = `<a class="page-link" href="#" onclick="loadUserList(${i}); return false;">${i}</a>`;
                container.appendChild(li);
            }

            // Next button
            const nextLi = document.createElement('li');
            nextLi.className = `page-item ${pagination.page >= pagination.total_pages ? 'disabled' : ''}`;
            nextLi.innerHTML = `<a class="page-link" href="#" onclick="loadUserList(${pagination.page + 1}); return false;">Next</a>`;
            container.appendChild(nextLi);
        }

        // Initialize dashboard
        async function initializeDashboard() {
            if (!checkAuthentication()) {
                return;
            }

            try {
                await Promise.all([
                    loadUserStats(),
                    loadAnalysisStats(),
                    loadDonationStats(),
                    loadUserList(1)
                ]);

                document.getElementById('last-updated').textContent = 'Last updated: ' + new Date().toLocaleString('en-US');
            } catch (error) {
                showError('Failed to initialize dashboard: ' + error.message);
            }
        }

        // Page load event
        document.addEventListener('DOMContentLoaded', function() {
            initializeDashboard();

            // Auto-refresh every 5 minutes
            setInterval(() => {
                if (checkAuthentication()) {
                    loadUserStats();
                    loadAnalysisStats();
                    loadDonationStats();
                    // Don't auto-refresh user list to avoid disrupting pagination
                }
            }, 5 * 60 * 1000);
        });
    </script>
</body>
</html>
