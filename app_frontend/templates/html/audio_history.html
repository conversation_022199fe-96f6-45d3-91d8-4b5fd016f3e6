{% load static %}
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>音频分析历史 - 认知健康</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://js.stripe.com/v3/"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: white;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            padding: 40px;
            margin-bottom: 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            font-size: 32px;
            font-weight: 700;
            color: #2d3748;
        }

        .new-analysis-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            text-decoration: none;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .new-analysis-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
            color: white;
            text-decoration: none;
        }

        .history-grid {
            display: grid;
            gap: 20px;
        }

        .history-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
            padding: 25px;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            cursor: pointer;
        }

        .history-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
        }

        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 15px;
        }

        .card-title {
            font-size: 18px;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 5px;
        }

        .card-date {
            color: #a0aec0;
            font-size: 14px;
        }

        .card-status {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-completed {
            background: #c6f6d5;
            color: #22543d;
        }

        .status-processing {
            background: #fed7aa;
            color: #9c4221;
        }

        .status-failed {
            background: #fed7d7;
            color: #742a2a;
        }

        .card-content {
            margin-bottom: 15px;
        }

        .card-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-bottom: 15px;
        }

        .info-item {
            display: flex;
            flex-direction: column;
            gap: 2px;
        }

        .info-label {
            font-size: 12px;
            color: #a0aec0;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .info-value {
            font-size: 14px;
            font-weight: 500;
            color: #2d3748;
        }

        .card-actions {
            display: flex;
            gap: 10px;
            justify-content: flex-end;
        }

        .action-btn {
            padding: 8px 16px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: #667eea;
            color: white;
        }

        .btn-primary:hover {
            background: #5a6fd8;
        }

        .btn-secondary {
            background: #e2e8f0;
            color: #4a5568;
        }

        .btn-secondary:hover {
            background: #cbd5e0;
        }

        .empty-state {
            text-align: center;
            padding: 80px 20px;
            color: #a0aec0;
        }

        .empty-state i {
            font-size: 64px;
            margin-bottom: 20px;
        }

        .empty-state h3 {
            font-size: 24px;
            margin-bottom: 10px;
        }

        .back-btn {
            position: fixed;
            top: 20px;
            left: 20px;
            background: rgba(255, 255, 255, 0.9);
            border: none;
            border-radius: 50px;
            padding: 15px 20px;
            color: #667eea;
            font-size: 16px;
            cursor: pointer;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            z-index: 1000;
        }

        .back-btn:hover {
            background: rgba(255, 255, 255, 1);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        /* 捐赠模态框样式 */
        .modal {
            display: none !important;
            position: fixed !important;
            z-index: 99999 !important;
            left: 0 !important;
            top: 0 !important;
            width: 100% !important;
            height: 100% !important;
            background-color: rgba(0, 0, 0, 0.8) !important;
            backdrop-filter: blur(5px);
        }

        .modal.show {
            display: block !important;
        }

        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 0;
            border-radius: 20px;
            width: 90%;
            max-width: 500px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            animation: modalSlideIn 0.3s ease-out;
        }

        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: translateY(-50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .modal-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 25px 30px;
            border-radius: 20px 20px 0 0;
            text-align: center;
        }

        .modal-header h2 {
            margin: 0;
            font-size: 24px;
            font-weight: 600;
        }

        .modal-body {
            padding: 30px;
        }

        .donation-amounts {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
            margin-bottom: 25px;
        }

        .amount-option {
            padding: 15px;
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            background: white;
        }

        .amount-option:hover {
            border-color: #667eea;
            background: #f7fafc;
        }

        .amount-option.selected {
            border-color: #667eea;
            background: #667eea;
            color: white;
        }

        .amount-option.no-donation {
            grid-column: span 2;
            background: #f7fafc;
            color: #718096;
        }

        .amount-option.no-donation.selected {
            background: #e2e8f0;
            color: #4a5568;
        }

        .payment-section {
            margin-top: 25px;
            padding-top: 25px;
            border-top: 1px solid #e2e8f0;
        }

        .payment-section h3 {
            margin-bottom: 15px;
            color: #2d3748;
            font-size: 18px;
        }

        #card-element {
            padding: 15px;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            background: white;
        }

        #card-errors {
            color: #e53e3e;
            margin-top: 10px;
            font-size: 14px;
        }

        .modal-actions {
            display: flex;
            gap: 15px;
            margin-top: 25px;
        }

        .modal-btn {
            flex: 1;
            padding: 15px;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-donate {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-donate:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }

        .btn-donate:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        .btn-skip {
            background: #e2e8f0;
            color: #4a5568;
        }

        .btn-skip:hover {
            background: #cbd5e0;
        }

        .close {
            position: absolute;
            right: 20px;
            top: 20px;
            color: white;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            opacity: 0.7;
            transition: opacity 0.3s ease;
        }

        .close:hover {
            opacity: 1;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }

        .spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @media (max-width: 768px) {
            .header {
                flex-direction: column;
                gap: 20px;
                text-align: center;
            }

            .card-info {
                grid-template-columns: 1fr;
            }

            .card-actions {
                justify-content: center;
            }

            .container {
                padding: 10px;
            }

            .modal-content {
                width: 95%;
                margin: 10% auto;
            }

            .donation-amounts {
                grid-template-columns: 1fr;
            }

            .amount-option.no-donation {
                grid-column: span 1;
            }
        }
    </style>
</head>
<body>
    <button class="back-btn" onclick="goBack()">
        <i class="fas fa-arrow-left"></i> 返回
    </button>

    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1><i class="fas fa-history"></i> 音频分析历史</h1>
            <div style="display: flex; gap: 10px;">
                <button onclick="window.showDonationModal('test-id')" style="background: #e53e3e; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;">测试模态框</button>
                <a href="/audio_upload/" class="new-analysis-btn">
                    <i class="fas fa-plus"></i> 新建分析
                </a>
            </div>
        </div>

        <!-- History Grid -->
        <div id="history-grid" class="history-grid">
            <!-- 动态加载内容 -->
        </div>
    </div>

    <!-- 捐赠模态框 -->
    <div id="donationModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <span class="close" onclick="window.hideDonationModal()">&times;</span>
                <h2><i class="fas fa-heart"></i> 支持我们的研究</h2>
                <p style="margin: 10px 0 0 0; opacity: 0.9;">您的捐赠将帮助我们改进认知健康分析技术</p>
            </div>
            <div class="modal-body">
                <div class="donation-amounts">
                    <div class="amount-option" data-amount="50">
                        <div style="font-size: 18px; font-weight: 600;">$50</div>
                        <div style="font-size: 12px; opacity: 0.8;">基础支持</div>
                    </div>
                    <div class="amount-option" data-amount="100">
                        <div style="font-size: 18px; font-weight: 600;">$100</div>
                        <div style="font-size: 12px; opacity: 0.8;">标准支持</div>
                    </div>
                    <div class="amount-option selected" data-amount="150">
                        <div style="font-size: 18px; font-weight: 600;">$150</div>
                        <div style="font-size: 12px; opacity: 0.8;">推荐金额</div>
                    </div>
                    <div class="amount-option" data-amount="200">
                        <div style="font-size: 18px; font-weight: 600;">$200</div>
                        <div style="font-size: 12px; opacity: 0.8;">慷慨支持</div>
                    </div>
                    <div class="amount-option" data-amount="250">
                        <div style="font-size: 18px; font-weight: 600;">$250</div>
                        <div style="font-size: 12px; opacity: 0.8;">高级支持</div>
                    </div>
                    <div class="amount-option" data-amount="300">
                        <div style="font-size: 18px; font-weight: 600;">$300</div>
                        <div style="font-size: 12px; opacity: 0.8;">专业支持</div>
                    </div>
                    <div class="amount-option" data-amount="350">
                        <div style="font-size: 18px; font-weight: 600;">$350</div>
                        <div style="font-size: 12px; opacity: 0.8;">企业支持</div>
                    </div>
                    <div class="amount-option" data-amount="400">
                        <div style="font-size: 18px; font-weight: 600;">$400</div>
                        <div style="font-size: 12px; opacity: 0.8;">高端支持</div>
                    </div>
                    <div class="amount-option" data-amount="450">
                        <div style="font-size: 18px; font-weight: 600;">$450</div>
                        <div style="font-size: 12px; opacity: 0.8;">顶级支持</div>
                    </div>
                    <div class="amount-option" data-amount="500">
                        <div style="font-size: 18px; font-weight: 600;">$500</div>
                        <div style="font-size: 12px; opacity: 0.8;">最高支持</div>
                    </div>
                    <div class="amount-option no-donation" data-amount="0">
                        <div style="font-size: 16px; font-weight: 600;">暂不捐赠</div>
                        <div style="font-size: 12px; opacity: 0.8;">直接查看分析结果</div>
                    </div>
                </div>

                <div id="payment-section" class="payment-section">
                    <h3><i class="fas fa-credit-card"></i> 支付信息</h3>
                    <div id="card-element">
                        <!-- Stripe Elements会在这里插入信用卡表单 -->
                    </div>
                    <div id="card-errors" role="alert"></div>
                </div>

                <div class="loading" id="loading">
                    <div class="spinner"></div>
                    <p>正在处理支付...</p>
                </div>

                <div class="modal-actions">
                    <button id="donate-btn" class="modal-btn btn-donate">
                        <i class="fas fa-heart"></i> 捐赠 $150
                    </button>
                    <button id="skip-btn" class="modal-btn btn-skip" onclick="
                        window.hideDonationModal();
                        const analysisId = window.currentAnalysisId;
                        if (analysisId) {
                            window.location.href = '/audio-detail/' + analysisId + '/';
                        } else {
                            console.error('No analysis ID found');
                        }">
                        <i class="fas fa-arrow-right"></i> 跳过捐赠
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局错误处理
        window.addEventListener('error', function(e) {
            console.error('Global JavaScript error:', e.error);
        });

        // API配置
        const API_BASE_URL = '{{ API_BASE_URL }}';

        // Stripe配置
        let stripe = null;
        let elements, cardElement;
        let currentAnalysisId = null;
        let selectedAmount = 150;

        // 全局简单函数
        window.showDonationModal = function(analysisId) {
            console.log('showDonationModal called with:', analysisId);
            const modal = document.getElementById('donationModal');
            if (modal) {
                modal.style.display = 'block';
                modal.style.position = 'fixed';
                modal.style.zIndex = '99999';
                modal.style.top = '0';
                modal.style.left = '0';
                modal.style.width = '100%';
                modal.style.height = '100%';
                modal.style.backgroundColor = 'rgba(0, 0, 0, 0.8)';
                document.body.style.overflow = 'hidden';
                window.currentAnalysisId = analysisId;
                console.log('Modal shown successfully');
                return true;
            } else {
                console.error('Modal not found');
                alert('错误：找不到捐赠模态框！');
                return false;
            }
        };

        window.hideDonationModal = function() {
            const modal = document.getElementById('donationModal');
            if (modal) {
                modal.style.display = 'none';
                document.body.style.overflow = 'auto';
                console.log('Modal hidden');
            }
        };

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM loaded, initializing...');
            console.log('API_BASE_URL:', API_BASE_URL);

            loadAnalysisHistory();

            // 检查模态框
            setTimeout(function() {
                const modal = document.getElementById('donationModal');
                console.log('Modal element check:', modal ? 'Found' : 'Not found');
                if (modal) {
                    console.log('Modal is ready');
                    // 确保模态框初始状态正确
                    modal.style.display = 'none';
                } else {
                    console.error('Modal not found!');
                }
            }, 100);
        });

        // 初始化Stripe
        async function initializeStripe() {
            console.log('Initializing Stripe...');
            try {
                const response = await fetch(`${API_BASE_URL}/api/stripe/config/`);
                const data = await response.json();

                if (data.success) {
                    console.log('Stripe config loaded successfully');
                    stripe = Stripe(data.data.publishable_key);
                } else {
                    console.error('Failed to load Stripe config:', data.message);
                    // 使用默认公钥作为后备
                    stripe = Stripe('pk_test_51234567890abcdef');
                }
            } catch (error) {
                console.error('Error loading Stripe config:', error);
                // 使用默认公钥作为后备
                stripe = Stripe('pk_test_51234567890abcdef');
            }

            // 无论Stripe是否成功初始化，都要初始化模态框的基本功能
            initializeDonationModal();
        }

        // 测试模态框函数
        function testModal() {
            console.log('Test modal button clicked');
            const modal = document.getElementById('donationModal');
            if (modal) {
                console.log('Modal found, showing...');
                modal.classList.add('show');
                modal.style.display = 'block';
                modal.style.zIndex = '99999';
                modal.style.position = 'fixed';
                modal.style.top = '0';
                modal.style.left = '0';
                modal.style.width = '100%';
                modal.style.height = '100%';
                modal.style.backgroundColor = 'rgba(255, 0, 0, 0.8)';
                document.body.style.overflow = 'hidden';
                console.log('Test modal should now be visible with red background');
            } else {
                console.error('Modal not found!');
                alert('模态框元素未找到！');
            }
        }

        // 返回上一页
        function goBack() {
            window.history.back();
        }

        // 加载分析历史
        async function loadAnalysisHistory() {
            try {
                const token = localStorage.getItem('access_token');
                if (!token) {
                    window.location.href = '/login/';
                    return;
                }

                const response = await fetch(`${API_BASE_URL}/api/audio_history/`, {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    displayAnalysisHistory(data.data || data.results || data);
                } else if (response.status === 401) {
                    localStorage.removeItem('access_token');
                    window.location.href = '/login/';
                } else {
                    console.error('Failed to load analysis history');
                }
            } catch (error) {
                console.error('Error loading analysis history:', error);
            }
        }

        // 显示分析历史
        function displayAnalysisHistory(analyses) {
            const container = document.getElementById('history-grid');
            
            if (!analyses || analyses.length === 0) {
                container.innerHTML = `
                    <div class="empty-state">
                        <i class="fas fa-microphone-slash"></i>
                        <h3>暂无分析记录</h3>
                        <p>开始您的第一次音频分析吧！</p>
                        <a href="/audio_upload/" class="new-analysis-btn" style="margin-top: 20px; display: inline-block;">
                            <i class="fas fa-microphone"></i> 开始分析
                        </a>
                    </div>
                `;
                return;
            }

            container.innerHTML = analyses.map(analysis => `
                <div class="history-card" onclick="window.showDonationModal('${analysis.id}')">
                    <div class="card-header">
                        <div>
                            <div class="card-title">音频分析 #${analysis.id.slice(-8)}</div>
                            <div class="card-date">${formatDate(analysis.upload_time)}</div>
                        </div>
                        <span class="card-status ${getStatusClass(analysis.status)}">${getStatusText(analysis.status)}</span>
                    </div>
                    
                    <div class="card-content">
                        <div class="card-info">
                            <div class="info-item">
                                <div class="info-label">文件名</div>
                                <div class="info-value">${analysis.filename || '未知'}</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">关系</div>
                                <div class="info-value">${analysis.relationship || 'N/A'}</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">年龄</div>
                                <div class="info-value">${analysis.age || 'N/A'}</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">职业</div>
                                <div class="info-value">${analysis.occupation || 'N/A'}</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card-actions">
                        <button onclick="event.stopPropagation(); window.showDonationModal('${analysis.id}');" class="action-btn btn-primary">
                            <i class="fas fa-eye"></i> 查看详情
                        </button>
                        ${analysis.status === 'completed' ? `
                            <button onclick="event.stopPropagation(); downloadReport('${analysis.id}')" class="action-btn btn-secondary">
                                <i class="fas fa-download"></i> 下载报告
                            </button>
                        ` : ''}
                    </div>
                </div>
            `).join('');
        }

        // 格式化日期
        function formatDate(dateString) {
            const date = new Date(dateString);
            return date.toLocaleString('zh-CN');
        }

        // 获取状态样式类
        function getStatusClass(status) {
            switch (status) {
                case 'completed': return 'status-completed';
                case 'processing': return 'status-processing';
                case 'failed': return 'status-failed';
                default: return 'status-processing';
            }
        }

        // 获取状态文本
        function getStatusText(status) {
            switch (status) {
                case 'completed': return '已完成';
                case 'processing': return '处理中';
                case 'failed': return '失败';
                default: return '未知';
            }
        }

        // 初始化捐赠模态框
        function initializeDonationModal() {
            console.log('Initializing donation modal...');

            // 检查模态框元素是否存在
            const modal = document.getElementById('donationModal');
            if (!modal) {
                console.error('Donation modal element not found');
                return;
            }

            console.log('Donation modal element found');

            // 如果Stripe已初始化，则初始化Stripe Elements
            if (stripe) {
                console.log('Initializing Stripe Elements...');
                try {
                    elements = stripe.elements();
                    cardElement = elements.create('card', {
                        style: {
                            base: {
                                fontSize: '16px',
                                color: '#424770',
                                '::placeholder': {
                                    color: '#aab7c4',
                                },
                            },
                        },
                    });
                    cardElement.mount('#card-element');

                    // 监听卡片输入错误
                    cardElement.on('change', function(event) {
                        const displayError = document.getElementById('card-errors');
                        if (event.error) {
                            displayError.textContent = event.error.message;
                        } else {
                            displayError.textContent = '';
                        }
                    });
                    console.log('Stripe Elements initialized successfully');
                } catch (error) {
                    console.error('Error initializing Stripe Elements:', error);
                }
            } else {
                console.warn('Stripe not available, payment functionality will be limited');
            }

            // 金额选择事件
            document.querySelectorAll('.amount-option').forEach(option => {
                option.addEventListener('click', function() {
                    document.querySelectorAll('.amount-option').forEach(opt => opt.classList.remove('selected'));
                    this.classList.add('selected');
                    selectedAmount = parseInt(this.dataset.amount);

                    const donateBtn = document.getElementById('donate-btn');
                    const paymentSection = document.getElementById('payment-section');

                    if (selectedAmount === 0) {
                        donateBtn.innerHTML = '<i class="fas fa-arrow-right"></i> 直接查看详情';
                        paymentSection.style.display = 'none';
                    } else {
                        donateBtn.innerHTML = `<i class="fas fa-heart"></i> 捐赠 $${selectedAmount}`;
                        paymentSection.style.display = 'block';
                    }
                });
            });

            // 关闭模态框
            try {
                const closeBtn = document.querySelector('.close');
                if (closeBtn) {
                    closeBtn.addEventListener('click', closeModal);
                    console.log('Close button event listener added');
                } else {
                    console.error('Close button not found');
                }

                const modal = document.getElementById('donationModal');
                if (modal) {
                    modal.addEventListener('click', function(e) {
                        if (e.target === this) {
                            closeModal();
                        }
                    });
                    console.log('Modal click event listener added');
                } else {
                    console.error('Modal element not found for click event');
                }
            } catch (error) {
                console.error('Error setting up modal event listeners:', error);
            }

            // 捐赠按钮事件
            try {
                const donateBtn = document.getElementById('donate-btn');
                if (donateBtn) {
                    donateBtn.addEventListener('click', handleDonation);
                    console.log('Donate button event listener added');
                } else {
                    console.error('Donate button not found');
                }
            } catch (error) {
                console.error('Error setting up donate button:', error);
            }

            // 跳过按钮事件
            try {
                const skipBtn = document.getElementById('skip-btn');
                if (skipBtn) {
                    skipBtn.addEventListener('click', function() {
                        closeModal();
                        if (currentAnalysisId) {
                            window.location.href = `/audio-detail/${currentAnalysisId}/`;
                        }
                    });
                    console.log('Skip button event listener added');
                } else {
                    console.error('Skip button not found');
                }
            } catch (error) {
                console.error('Error setting up skip button:', error);
            }
        }

        // 查看分析详情 - 先显示捐赠模态框
        function viewAnalysisDetail(analysisId) {
            console.log('viewAnalysisDetail called with ID:', analysisId);
            currentAnalysisId = analysisId;

            // 直接显示模态框，不依赖其他函数
            const modal = document.getElementById('donationModal');
            if (modal) {
                console.log('Found modal, showing directly...');
                modal.classList.add('show');
                modal.style.display = 'block';
                modal.style.zIndex = '99999';
                modal.style.position = 'fixed';
                modal.style.top = '0';
                modal.style.left = '0';
                modal.style.width = '100%';
                modal.style.height = '100%';
                modal.style.backgroundColor = 'rgba(0, 0, 0, 0.8)';
                document.body.style.overflow = 'hidden';
                console.log('Modal displayed directly with all styles');
            } else {
                console.error('Modal not found in viewAnalysisDetail');
                alert('错误：找不到捐赠模态框！');
            }
        }

        // 显示捐赠模态框
        function showDonationModal() {
            console.log('Showing donation modal...');
            const modal = document.getElementById('donationModal');
            if (!modal) {
                console.error('Cannot show modal: donationModal element not found');
                alert('错误：找不到捐赠模态框元素！');
                return;
            }

            console.log('Modal element found, current display:', modal.style.display);
            console.log('Modal computed display:', window.getComputedStyle(modal).display);

            modal.style.display = 'block';
            modal.style.visibility = 'visible';
            modal.style.opacity = '1';
            document.body.style.overflow = 'hidden';

            console.log('Modal display set to block');
            console.log('New computed display:', window.getComputedStyle(modal).display);

            // 强制重绘
            modal.offsetHeight;

            console.log('Donation modal should now be visible');
        }

        // 跳过捐赠
        function skipDonation() {
            console.log('Skip donation clicked');
            closeModal();
            const analysisId = window.currentAnalysisId || currentAnalysisId;
            if (analysisId) {
                window.location.href = `/audio-detail/${analysisId}/`;
            } else {
                console.error('No analysis ID found');
            }
        }

        // 关闭捐赠模态框
        function closeModal() {
            const modal = document.getElementById('donationModal');
            if (modal) {
                modal.classList.remove('show');
                modal.style.display = 'none';
                document.body.style.overflow = 'auto';
                currentAnalysisId = null;
                console.log('Modal closed');
            }
        }

        // 处理捐赠
        async function handleDonation() {
            if (selectedAmount === 0) {
                // 直接跳转到详情页
                closeModal();
                window.location.href = `/audio-detail/${currentAnalysisId}/`;
                return;
            }

            const donateBtn = document.getElementById('donate-btn');
            const loading = document.getElementById('loading');

            try {
                donateBtn.disabled = true;
                loading.style.display = 'block';

                const token = localStorage.getItem('access_token');
                if (!token) {
                    alert('请先登录');
                    window.location.href = '/login/';
                    return;
                }

                // 创建捐赠记录和支付意图
                const response = await fetch(`${API_BASE_URL}/api/donations/create/`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        audio_analysis_id: currentAnalysisId,
                        amount: selectedAmount
                    })
                });

                const data = await response.json();

                if (data.success) {
                    // 确认支付
                    const result = await stripe.confirmCardPayment(data.data.client_secret, {
                        payment_method: {
                            card: cardElement,
                            billing_details: {
                                name: '捐赠者'
                            }
                        }
                    });

                    if (result.error) {
                        throw new Error(result.error.message);
                    } else {
                        // 支付成功，确认捐赠
                        await confirmDonation(result.paymentIntent.id);

                        alert('感谢您的捐赠！支付成功。');
                        closeModal();
                        window.location.href = `/audio-detail/${currentAnalysisId}/`;
                    }
                } else {
                    throw new Error(data.message || '创建捐赠失败');
                }
            } catch (error) {
                console.error('捐赠处理失败:', error);
                alert(`捐赠失败: ${error.message}`);
            } finally {
                donateBtn.disabled = false;
                loading.style.display = 'none';
            }
        }

        // 确认捐赠支付
        async function confirmDonation(paymentIntentId) {
            const token = localStorage.getItem('access_token');

            const response = await fetch(`${API_BASE_URL}/api/donations/confirm/`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    payment_intent_id: paymentIntentId
                })
            });

            const data = await response.json();
            if (!data.success) {
                throw new Error(data.message || '确认支付失败');
            }

            return data;
        }

        // 下载报告
        function downloadReport(analysisId) {
            // 实现下载报告功能
            console.log('Download report for:', analysisId);
        }
    </script>

    <!-- 确保模态框功能的备用脚本 -->
    <script>
        // 备用的简单模态框控制
        window.showModal = function() {
            const modal = document.getElementById('donationModal');
            if (modal) {
                modal.style.display = 'block';
                modal.style.position = 'fixed';
                modal.style.zIndex = '99999';
                modal.style.top = '0';
                modal.style.left = '0';
                modal.style.width = '100%';
                modal.style.height = '100%';
                modal.style.backgroundColor = 'rgba(0, 0, 0, 0.8)';
                document.body.style.overflow = 'hidden';
                console.log('Modal shown via backup function');
            }
        };

        window.hideModal = function() {
            const modal = document.getElementById('donationModal');
            if (modal) {
                modal.style.display = 'none';
                document.body.style.overflow = 'auto';
                console.log('Modal hidden via backup function');
            }
        };

        // 确保在页面完全加载后设置事件
        window.addEventListener('load', function() {
            console.log('Window loaded, setting up backup events...');

            // 为所有查看详情按钮添加事件
            setTimeout(function() {
                const buttons = document.querySelectorAll('.btn-primary');
                buttons.forEach(function(button) {
                    if (button.textContent.includes('查看详情')) {
                        button.addEventListener('click', function(e) {
                            e.preventDefault();
                            e.stopPropagation();
                            console.log('View details button clicked (backup handler)');
                            showModal();
                        });
                    }
                });
                console.log('Backup event handlers set for', buttons.length, 'buttons');
            }, 1000);
        });
    </script>
</body>
</html>
