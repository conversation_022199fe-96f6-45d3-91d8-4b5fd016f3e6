{% extends 'base.html' %}
{% load static %}

{% block content %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>About Us - HiSage Health</title>
    <meta name="description" content="Meet the team behind HiSage Health and learn about our journey in revolutionizing dementia screening through AI technology.">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Merriweather:wght@300;400;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <style>
    :root {
        --primary-blue: #2563eb;
        --primary-blue-dark: #1d4ed8;
        --secondary-blue: #3b82f6;
        --accent-teal: #0d9488;
        --accent-green: #059669;
        --text-dark: #1f2937;
        --text-gray: #6b7280;
        --text-light: #9ca3af;
        --bg-light: #f8fafc;
        --bg-white: #ffffff;
        --border-light: #e5e7eb;
        --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
        --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
        --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
        --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
    }

    * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
    }

    body {
        font-family: 'Inter', sans-serif;
        line-height: 1.6;
        color: var(--text-dark);
        background-color: var(--bg-white);
    }

    .container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 20px;
    }

    .section {
        padding: 50px 0;
    }

    .section-title {
        font-size: 2.5rem;
        font-weight: 700;
        text-align: center;
        margin-bottom: 1rem;
        color: var(--text-dark);
    }

    .section-subtitle {
        font-size: 1.2rem;
        text-align: center;
        color: var(--text-gray);
        margin-bottom: 2rem;
        max-width: 600px;
        margin-left: auto;
        margin-right: auto;
    }

    /* Hero Section */
    .hero {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 60px 0;
        text-align: center;
        position: relative;
        overflow: hidden;
    }

    .hero::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><radialGradient id="a" cx="50%" cy="50%"><stop offset="0%" stop-color="%23ffffff" stop-opacity="0.1"/><stop offset="100%" stop-color="%23ffffff" stop-opacity="0"/></radialGradient></defs><circle cx="200" cy="200" r="100" fill="url(%23a)"/><circle cx="800" cy="300" r="150" fill="url(%23a)"/><circle cx="400" cy="700" r="120" fill="url(%23a)"/></svg>');
        opacity: 0.3;
    }

    .hero-content {
        position: relative;
        z-index: 1;
    }

    .hero h1 {
        font-size: 3.5rem;
        font-weight: 700;
        margin-bottom: 1.5rem;
        line-height: 1.2;
    }

    .hero p {
        font-size: 1.25rem;
        margin-bottom: 2rem;
        opacity: 0.9;
        max-width: 600px;
        margin-left: auto;
        margin-right: auto;
    }

    .cta-button {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        background: var(--accent-teal);
        color: white;
        padding: 1rem 2rem;
        border-radius: 50px;
        text-decoration: none;
        font-weight: 600;
        font-size: 1.1rem;
        transition: all 0.3s ease;
        box-shadow: var(--shadow-lg);
        border: none;
        cursor: pointer;
        font-family: inherit;
    }

    .cta-button:hover {
        background: var(--accent-green);
        transform: translateY(-2px);
        box-shadow: var(--shadow-xl);
        color: white;
        text-decoration: none;
    }

    /* Stats Grid */
    .stats {
        background: var(--bg-light);
        padding: 60px 0;
    }

    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 2rem;
        text-align: center;
    }

    .stat-item {
        background: white;
        padding: 2rem;
        border-radius: 12px;
        box-shadow: var(--shadow-sm);
    }

    .stat-number {
        font-size: 3rem;
        font-weight: 700;
        color: var(--primary-blue);
        margin-bottom: 0.5rem;
    }

    .stat-label {
        font-size: 1rem;
        color: var(--text-gray);
        font-weight: 500;
    }

    /* Features Grid */
    .features-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
        gap: 2rem;
        margin-top: 2rem;
    }

    .feature-card {
        background: white;
        padding: 2rem;
        border-radius: 16px;
        box-shadow: var(--shadow-md);
        transition: all 0.3s ease;
        border: 1px solid var(--border-light);
    }

    .feature-card:hover {
        transform: translateY(-4px);
        box-shadow: var(--shadow-xl);
    }

    .feature-icon {
        width: 60px;
        height: 60px;
        background: linear-gradient(135deg, var(--primary-blue), var(--secondary-blue));
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 1.5rem;
    }

    .feature-icon i {
        font-size: 1.5rem;
        color: white;
    }

    .feature-title {
        font-size: 1.25rem;
        font-weight: 600;
        margin-bottom: 1rem;
        color: var(--text-dark);
    }

    .feature-description {
        color: var(--text-gray);
        line-height: 1.6;
    }

    /* Team Cards */
    .team-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 2rem;
        margin-top: 2rem;
    }

    .team-card {
        background: var(--bg-light);
        padding: 2rem;
        border-radius: 12px;
        text-align: center;
        box-shadow: var(--shadow-sm);
        transition: all 0.3s ease;
    }

    .team-card:hover {
        transform: translateY(-4px);
        box-shadow: var(--shadow-md);
    }

    .team-avatar {
        width: 80px;
        height: 80px;
        background: linear-gradient(135deg, var(--primary-blue), var(--secondary-blue));
        border-radius: 50%;
        margin: 0 auto 1.5rem;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .team-avatar i {
        font-size: 2rem;
        color: white;
    }

    .team-name {
        color: var(--text-dark);
        margin-bottom: 1rem;
        font-size: 1.1rem;
        font-weight: 600;
    }

    .team-description {
        color: var(--text-gray);
        line-height: 1.6;
        margin-bottom: 1.5rem;
        font-size: 0.9rem;
    }

    .team-stats {
        background: white;
        padding: 1rem;
        border-radius: 8px;
    }

    .team-stats div {
        font-size: 0.9rem;
        color: var(--text-gray);
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .hero h1 {
            font-size: 2.5rem;
        }

        .section-title {
            font-size: 2rem;
        }

        .features-grid {
            grid-template-columns: 1fr;
        }

        .team-grid {
            grid-template-columns: 1fr;
        }

        .stats-grid {
            grid-template-columns: repeat(2, 1fr);
        }
    }

    @media (max-width: 480px) {
        .stats-grid {
            grid-template-columns: 1fr;
        }

        .hero h1 {
            font-size: 2rem;
        }

        .stat-number {
            font-size: 2rem;
        }
    }





    /* Responsive Design */
    @media (max-width: 768px) {
        .hero h1 {
            font-size: 2.5rem;
        }

        .hero p {
            font-size: 1.1rem;
        }

        .section-title {
            font-size: 2rem;
        }

        .mission-grid {
            grid-template-columns: 1fr;
            gap: 2rem;
        }

        /* The Beginning section responsive */
        .container > div[style*="grid-template-columns: 1fr 1fr"] {
            grid-template-columns: 1fr !important;
            gap: 2rem !important;
        }

        .team-grid {
            grid-template-columns: 1fr;
        }

        .stats-grid {
            grid-template-columns: repeat(2, 1fr);
        }

        .values-grid {
            grid-template-columns: 1fr;
        }

        .tech-grid {
            grid-template-columns: 1fr;
        }

        .container {
            padding: 0 15px;
        }

        .section {
            padding: 40px 0;
        }

        .hero {
            padding: 50px 0 40px;
        }
    }

    @media (max-width: 480px) {
        .hero h1 {
            font-size: 2rem;
        }

        .stats-grid {
            grid-template-columns: 1fr;
        }

        .stat-number {
            font-size: 2rem;
        }

        .team-card {
            padding: 2rem;
        }

        .value-card {
            padding: 2rem;
        }
    }
</style>
</head>

<body>
    <!-- The Beginning Section -->
    <section class="section" style="background: white; padding-top: 80px;">
        <div class="container">
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 4rem; align-items: flex-start; margin-top: 1.5rem;">
                <div>
                    <h3 style="color: var(--text-dark); font-size: 1.8rem; margin-bottom: 1.5rem;">The Beginning</h3>
                    <p style="color: var(--text-gray); line-height: 1.8; margin-bottom: 1.5rem;">
                        HiSage Health was founded in 2020 by a team of passionate researchers and clinicians who witnessed the devastating impact of late-stage dementia diagnosis on countless families. Frustrated by the limitations of traditional cognitive assessments, our founders brought together expertise in neurology, artificial intelligence, and speech pathology.
                    </p>
                    <p style="color: var(--text-gray); line-height: 1.8; margin-bottom: 1.5rem;">
                        Our founders shared a vision: to harness the power of artificial intelligence and speech analysis to detect cognitive decline years before symptoms become apparent to families and doctors.
                    </p>
                    <p style="color: var(--text-gray); line-height: 1.8;">
                        What started as a research project in a small university lab has grown into a comprehensive platform designed to help families gain precious time for planning and intervention through early cognitive screening.
                    </p>
                </div>
                <div>
                    <h3 style="color: var(--text-dark); font-size: 1.8rem; margin-bottom: 1.5rem;">Our Mission</h3>
                    <p style="color: var(--text-gray); line-height: 1.8; margin-bottom: 1.5rem;">
                        To democratize early dementia detection through cutting-edge AI technology, making cognitive health screening accessible, accurate, and affordable for everyone worldwide.
                    </p>
                    <p style="color: var(--text-gray); line-height: 1.8; margin-bottom: 1.5rem;">
                        We believe that early detection shouldn't be a privilege reserved for those with access to specialized medical centers. Our AI-powered platform democratizes cognitive health screening, making it accessible to anyone, anywhere.
                    </p>
                    <p style="color: var(--text-gray); line-height: 1.8;">
                        Through the power of speech analysis and machine learning, we're not just detecting dementia earlier – we're giving families precious time to plan, prepare, and pursue treatments that could slow progression.
                    </p>
                </div>
            </div>
        </div>
    </section>



    <!-- Our Technology Section -->
    <section class="section" style="background: white;">
        <div class="container">
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 4rem; align-items: flex-start; margin-top: 1.5rem;">
                <div>
                    <h3 style="color: var(--text-dark); font-size: 1.8rem; margin-bottom: 1.5rem;">Speech Analysis Engine</h3>
                    <p style="color: var(--text-gray); line-height: 1.8; margin-bottom: 1.5rem;">
                        Our proprietary algorithms analyze over 3000 speech features including pause patterns, semantic fluency, and linguistic complexity to detect subtle cognitive changes years before traditional methods.
                    </p>
                    <p style="color: var(--text-gray); line-height: 1.8; margin-bottom: 1.5rem;">
                        With real-time processing capability, our speech analysis engine provides 60+ speech biomarkers analyzed with clinical validation across populations.
                    </p>
                    <p style="color: var(--text-gray); line-height: 1.8;">
                        This core AI technology represents years of research and development, enabling healthcare providers to detect cognitive decline.
                    </p>
                </div>
                <div>
                    <h3 style="color: var(--text-dark); font-size: 1.8rem; margin-bottom: 1.5rem;">Our Technology</h3>
                    <p style="color: var(--text-gray); line-height: 1.8; margin-bottom: 1.5rem;">
                        Our platform combines predictive analytics with privacy-first design to deliver comprehensive cognitive health screening.
                    </p>
                    <p style="color: var(--text-gray); line-height: 1.8; margin-bottom: 1.5rem;">
                        Built with enterprise-grade security and HIPAA compliance, all data is encrypted end-to-end. Our federated learning approach ensures privacy while improving model performance through continuous learning.
                    </p>
                    <p style="color: var(--text-gray); line-height: 1.8;">
                        With early detection capability, risk stratification models, and personalized recommendations, we're revolutionizing how cognitive health is monitored and managed globally.
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- Company Milestones Section -->
    <section class="section" style="background: var(--bg-light);">
        <div class="container">
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 4rem; align-items: flex-start; margin-top: 1.5rem;">
                <div>
                    <h3 style="color: var(--text-dark); font-size: 1.8rem; margin-bottom: 1.5rem;">Global Impact</h3>
                    <p style="color: var(--text-gray); line-height: 1.8; margin-bottom: 1.5rem;">
                        Dementia represents one of the most significant healthcare challenges of our time, with an annual global economic burden of $1.3 trillion. Currently, 55 million people worldwide are living with dementia, and this number continues to grow with 10 million new cases diagnosed annually.
                    </p>
                    <p style="color: var(--text-gray); line-height: 1.8; margin-bottom: 1.5rem;">
                        The devastating impact extends far beyond individual patients, affecting families, caregivers, and healthcare systems globally. Traditional diagnostic methods often detect dementia only after significant cognitive decline has already occurred.
                    </p>
                    <p style="color: var(--text-gray); line-height: 1.8;">
                        This late detection limits treatment options and planning opportunities, making early intervention strategies crucial for improving patient outcomes and reducing the overall burden on society.
                    </p>
                </div>
                <div>
                    <h3 style="color: var(--text-dark); font-size: 1.8rem; margin-bottom: 1.5rem;">Our Journey</h3>
                    <p style="color: var(--text-gray); line-height: 1.8; margin-bottom: 1.5rem;">
                        Since our founding in 2020, HiSage Health has achieved remarkable milestones in transforming dementia screening. We've grown from a small university research project to a comprehensive platform dedicated to advancing early cognitive health assessment.
                    </p>
                    <p style="color: var(--text-gray); line-height: 1.8; margin-bottom: 1.5rem;">
                        Our breakthrough achievements include pioneering AI speech analysis technology, completing extensive clinical validation studies, and establishing partnerships with leading medical institutions across multiple continents.
                    </p>
                    <p style="color: var(--text-gray); line-height: 1.8;">
                        Through rigorous research and development, we are helping families gain precious time for planning and intervention strategies.
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- Global Impact & Recognition Section -->
    <section class="section" style="background: white;">
        <div class="container">
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 4rem; align-items: flex-start; margin-top: 1.5rem;">
                <div>
                    <h3 style="color: var(--text-dark); font-size: 1.8rem; margin-bottom: 1.5rem;">What's Next</h3>
                    <p style="color: var(--text-gray); line-height: 1.8; margin-bottom: 1.5rem;">
                        We're preparing to launch HiSage 2.0 with enhanced multilingual support, real-time analysis, and integration with electronic health records. This next generation platform will expand our capabilities and reach.
                    </p>
                    <p style="color: var(--text-gray); line-height: 1.8; margin-bottom: 1.5rem;">
                        Our Series B funding round will accelerate global adoption and expand our research into other conditions, including Parkinson's disease and depression detection.
                    </p>
                    <p style="color: var(--text-gray); line-height: 1.8;">
                        With continued innovation and strategic partnerships, we're committed to making early cognitive health screening accessible to everyone, everywhere, transforming how we approach brain health worldwide.
                    </p>
                </div>
                <div>
                    <h3 style="color: var(--text-dark); font-size: 1.8rem; margin-bottom: 1.5rem;">Our Recognition</h3>
                    <p style="color: var(--text-gray); line-height: 1.8; margin-bottom: 1.5rem;">
                        HiSage Health's innovative approach enables detection earlier than traditional methods, providing unprecedented opportunities for early intervention and care planning. This breakthrough capability has garnered recognition from leading medical institutions worldwide.
                    </p>
                    <p style="color: var(--text-gray); line-height: 1.8; margin-bottom: 1.5rem;">
                        Our platform has been validated through extensive clinical trials and partnerships with top-tier universities, medical centers, and global health organizations, establishing us as a leader in AI-powered cognitive health screening.
                    </p>
                    <p style="color: var(--text-gray); line-height: 1.8;">
                        By transforming how cognitive decline is detected and monitored, we're not just changing individual lives – we're contributing to a global shift toward proactive, preventive healthcare that can significantly reduce the burden of dementia worldwide.
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- Hero Section -->
    <section class="hero">
        <div class="container">
            <div class="hero-content">
                <h1>Join Us in Our Mission </h1>
                <p>Whether you're a healthcare provider, researcher, or someone concerned about cognitive health, we invite you to be part of the solution.</p>
                <div style="display: flex; gap: 1rem; align-items: center; flex-wrap: wrap; justify-content: center;">
                    <button type="button" onclick="startScreening(); return false;" class="cta-button">
                        <i class="fas fa-microphone"></i>
                        Try Our Screening
                    </button>

                    <button type="button" onclick="checkLoginAndGoToHistory()"
                       style="display: inline-flex; align-items: center; gap: 0.5rem; padding: 0.75rem 1.5rem;
                              background: rgba(255,255,255,0.1); color: white; text-decoration: none; border-radius: 50px;
                              font-weight: 600; transition: all 0.3s ease; backdrop-filter: blur(10px);
                              border: 1px solid rgba(255,255,255,0.2); border: none; cursor: pointer;">
                        <i class="fas fa-history"></i>
                        View History
                    </button>
                </div>
            </div>
        </div>
    </section>

<script>
    // Function to check if user is authenticated
    function isUserAuthenticated() {
        const token = localStorage.getItem('access_token');
        if (!token) {
            console.log('❌ No access token found');
            return false;
        }

        try {
            const payload = JSON.parse(atob(token.split('.')[1]));
            const currentTime = Date.now() / 1000;

            if (payload.exp < currentTime) {
                console.log('❌ Token expired');
                localStorage.removeItem('access_token');
                localStorage.removeItem('refresh_token');
                return false;
            }

            console.log('✅ User is authenticated');
            return true;
        } catch (error) {
            console.error('❌ Error checking token:', error);
            localStorage.removeItem('access_token');
            localStorage.removeItem('refresh_token');
            return false;
        }
    }

    // Function to start screening
    function startScreening() {
        console.log('🎯 Starting screening process...');

        const isAuth = isUserAuthenticated();
        console.log('🔍 Authentication result:', isAuth);

        if (isAuth) {
            console.log('✅ User is authenticated, redirecting to audio upload');
            window.location.href = '/audio_upload/';
        } else {
            console.log('⚠️ User not authenticated, redirecting to login');
            // Store the intended destination
            sessionStorage.setItem('redirectAfterLogin', '/audio_upload/');
            console.log('💾 Stored redirect URL in sessionStorage');
            window.location.href = '/login/';
        }
    }

    // Function to check login and go to history
    function checkLoginAndGoToHistory() {
        console.log('🔍 Checking login status before going to history...');

        const isAuth = isUserAuthenticated();
        console.log('🔍 Authentication result:', isAuth);

        if (isAuth) {
            console.log('✅ User is authenticated, redirecting to audio history');
            window.location.href = '/audio_upload/history/';
        } else {
            console.log('⚠️ User not authenticated, redirecting to login');
            // Store the intended destination
            sessionStorage.setItem('redirectAfterLogin', '/audio_upload/history/');
            console.log('💾 Stored redirect URL in sessionStorage');
            window.location.href = '/login/';
        }
    }

    // Make functions available globally
    window.startScreening = startScreening;
    window.checkLoginAndGoToHistory = checkLoginAndGoToHistory;

    // Ensure functions are available when page loads
    document.addEventListener('DOMContentLoaded', () => {
        window.startScreening = startScreening;
        window.checkLoginAndGoToHistory = checkLoginAndGoToHistory;
        console.log('✅ About page: functions loaded');
    });
</script>

</body>
</html>
{% endblock %}
