<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Password Change Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 0 auto;
            padding: 2rem;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 2rem;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 1rem;
        }
        .status {
            padding: 1rem;
            border-radius: 4px;
            margin: 1rem 0;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        pre { background: #f8f9fa; padding: 1rem; border-radius: 4px; overflow-x: auto; font-size: 0.9em; }
        .form-group {
            margin-bottom: 1rem;
        }
        label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: bold;
        }
        input[type="password"] {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 1rem;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 4px;
            cursor: pointer;
            margin: 0.5rem;
            font-size: 1rem;
        }
        button:hover { background: #0056b3; }
        button:disabled { background: #ccc; cursor: not-allowed; }
        .debug-btn { background: #17a2b8; }
        .debug-btn:hover { background: #138496; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Password Change Test</h1>
        
        <h2>Authentication Status</h2>
        <div id="auth-status" class="status warning">Checking authentication...</div>
        <pre id="auth-details"></pre>

        <h2>Password Change Form</h2>
        <form id="password-form">
            <div class="form-group">
                <label for="current-password">Current Password</label>
                <input type="password" id="current-password" placeholder="Enter current password">
            </div>
            
            <div class="form-group">
                <label for="new-password">New Password</label>
                <input type="password" id="new-password" placeholder="Enter new password">
            </div>
            
            <div class="form-group">
                <label for="confirm-password">Confirm New Password</label>
                <input type="password" id="confirm-password" placeholder="Confirm new password">
            </div>
            
            <button type="button" onclick="debugForm()" class="debug-btn">🔍 Debug Form</button>
            <button type="button" onclick="testPasswordChange()">🔑 Test Password Change</button>
            <button type="button" onclick="document.getElementById('password-form').reset()">🔄 Reset Form</button>
        </form>

        <h2>Test Results</h2>
        <div id="test-status" class="status warning">Ready to test</div>
        <pre id="test-response"></pre>

        <div style="margin-top: 2rem;">
            <a href="/user/settings/" style="
                display: inline-block;
                background: #28a745;
                color: white;
                padding: 0.75rem 1.5rem;
                text-decoration: none;
                border-radius: 4px;
            ">Go to Settings</a>
        </div>
    </div>

    <script>
        // 使用Django模板变量传递配置
        window.API_BASE_URL = "{{ API_BASE_URL }}";
        window.LOCAL_BASE_URL = "{{ LOCAL_BASE_URL }}";
        window.CSRF_TOKEN = "{{ csrf_token }}";
        
        // 备用配置
        if (!window.API_BASE_URL || window.API_BASE_URL === '') {
            window.API_BASE_URL = "http://**************:8001";
        }

        document.addEventListener('DOMContentLoaded', function() {
            checkAuth();
        });

        async function checkAuth() {
            const authStatus = document.getElementById('auth-status');
            const authDetails = document.getElementById('auth-details');
            
            const token = localStorage.getItem('access_token');
            
            if (!token) {
                authStatus.className = 'status error';
                authStatus.textContent = '❌ No authentication token found';
                authDetails.textContent = 'Please log in first.';
                return;
            }

            try {
                const response = await fetch(`${window.API_BASE_URL}/api/user/profile/`, {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    authStatus.className = 'status success';
                    authStatus.textContent = '✅ Authentication successful';
                    authDetails.textContent = JSON.stringify({
                        user: data.user.email,
                        token_present: true
                    }, null, 2);
                } else {
                    authStatus.className = 'status error';
                    authStatus.textContent = `❌ Authentication failed: ${response.status}`;
                    authDetails.textContent = await response.text();
                }
            } catch (error) {
                authStatus.className = 'status error';
                authStatus.textContent = '❌ Authentication error';
                authDetails.textContent = error.message;
            }
        }

        function debugForm() {
            const currentPassword = document.getElementById('current-password').value;
            const newPassword = document.getElementById('new-password').value;
            const confirmPassword = document.getElementById('confirm-password').value;
            
            const debugInfo = {
                form_element: !!document.getElementById('password-form'),
                current_password: {
                    element_exists: !!document.getElementById('current-password'),
                    value_length: currentPassword.length,
                    is_empty: !currentPassword || !currentPassword.trim(),
                    value_preview: currentPassword ? '***' : 'empty'
                },
                new_password: {
                    element_exists: !!document.getElementById('new-password'),
                    value_length: newPassword.length,
                    is_empty: !newPassword || !newPassword.trim(),
                    value_preview: newPassword ? '***' : 'empty'
                },
                confirm_password: {
                    element_exists: !!document.getElementById('confirm-password'),
                    value_length: confirmPassword.length,
                    is_empty: !confirmPassword || !confirmPassword.trim(),
                    value_preview: confirmPassword ? '***' : 'empty'
                },
                passwords_match: newPassword === confirmPassword,
                validation: {
                    current_password_valid: !!(currentPassword && currentPassword.trim()),
                    new_password_valid: !!(newPassword && newPassword.trim() && newPassword.length >= 8),
                    passwords_match: newPassword === confirmPassword
                }
            };
            
            console.log('🔍 Form debug info:', debugInfo);
            document.getElementById('test-response').textContent = JSON.stringify(debugInfo, null, 2);
            document.getElementById('test-status').className = 'status warning';
            document.getElementById('test-status').textContent = '🔍 Form debug info logged';
        }

        async function testPasswordChange() {
            const testStatus = document.getElementById('test-status');
            const testResponse = document.getElementById('test-response');
            
            testStatus.className = 'status warning';
            testStatus.textContent = '🔄 Testing password change...';
            
            const currentPassword = document.getElementById('current-password').value;
            const newPassword = document.getElementById('new-password').value;
            const confirmPassword = document.getElementById('confirm-password').value;
            
            // Validate form
            if (!currentPassword || !currentPassword.trim()) {
                testStatus.className = 'status error';
                testStatus.textContent = '❌ Current password is required';
                return;
            }
            
            if (!newPassword || !newPassword.trim()) {
                testStatus.className = 'status error';
                testStatus.textContent = '❌ New password is required';
                return;
            }
            
            if (newPassword !== confirmPassword) {
                testStatus.className = 'status error';
                testStatus.textContent = '❌ Passwords do not match';
                return;
            }
            
            if (newPassword.length < 8) {
                testStatus.className = 'status error';
                testStatus.textContent = '❌ Password must be at least 8 characters';
                return;
            }

            const token = localStorage.getItem('access_token');
            if (!token) {
                testStatus.className = 'status error';
                testStatus.textContent = '❌ No authentication token';
                return;
            }

            try {
                const requestData = {
                    current_password: currentPassword,
                    new_password: newPassword
                };
                
                console.log('📤 Sending password change request');
                
                const response = await fetch(`${window.API_BASE_URL}/api/user/change-password/`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(requestData)
                });
                
                const responseText = await response.text();
                console.log('📡 Response:', response.status, responseText);
                
                if (response.ok) {
                    const data = JSON.parse(responseText);
                    testStatus.className = 'status success';
                    testStatus.textContent = '✅ Password changed successfully';
                    testResponse.textContent = JSON.stringify(data, null, 2);
                    document.getElementById('password-form').reset();
                } else {
                    testStatus.className = 'status error';
                    testStatus.textContent = `❌ Password change failed: ${response.status}`;
                    testResponse.textContent = responseText;
                }
            } catch (error) {
                testStatus.className = 'status error';
                testStatus.textContent = '❌ Network error';
                testResponse.textContent = error.message;
            }
        }
    </script>
</body>
</html>
