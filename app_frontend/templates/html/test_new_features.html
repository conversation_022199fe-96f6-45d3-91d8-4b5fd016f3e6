{% extends 'html/base.html' %}
{% load static %}

{% block title %}New Features Test - Cognitive Health{% endblock %}

{% block content %}
<div class="tw-min-h-screen tw-bg-gray-50 tw-py-8">
    <div class="tw-max-w-4xl tw-mx-auto tw-px-4 sm:tw-px-6 lg:tw-px-8">
        <div class="tw-mb-8">
            <h1 class="tw-text-3xl tw-font-bold tw-text-gray-900">New Features Test Page</h1>
            <p class="tw-mt-2 tw-text-gray-600">Testing redesigned user authentication and profile system</p>
        </div>

        <!-- 功能测试区域 -->
        <div class="tw-space-y-8">
            <!-- 认证状态 -->
            <div class="tw-bg-white tw-shadow tw-rounded-lg tw-p-6">
                <h2 class="tw-text-xl tw-font-semibold tw-text-gray-900 tw-mb-4">认证状态</h2>
                <div class="tw-space-y-3">
                    <div class="tw-flex tw-justify-between">
                        <span>登录状态:</span>
                        <span id="auth-status" class="tw-font-medium">检查中...</span>
                    </div>
                    <div class="tw-flex tw-justify-between">
                        <span>用户邮箱:</span>
                        <span id="user-email" class="tw-font-medium">-</span>
                    </div>
                    <div class="tw-flex tw-justify-between">
                        <span>显示名称:</span>
                        <span id="display-name" class="tw-font-medium">-</span>
                    </div>
                    <div class="tw-flex tw-justify-between">
                        <span>Token:</span>
                        <span id="token-status" class="tw-font-medium">-</span>
                    </div>
                </div>
            </div>

            <!-- API测试 -->
            <div class="tw-bg-white tw-shadow tw-rounded-lg tw-p-6">
                <h2 class="tw-text-xl tw-font-semibold tw-text-gray-900 tw-mb-4">API测试</h2>
                <div class="tw-grid tw-grid-cols-1 md:tw-grid-cols-2 tw-gap-4">
                    <button id="test-profile" class="tw-bg-blue-600 tw-text-white tw-px-4 tw-py-2 tw-rounded tw-hover:tw-bg-blue-700">
                        测试获取Profile
                    </button>
                    <button id="test-history" class="tw-bg-green-600 tw-text-white tw-px-4 tw-py-2 tw-rounded tw-hover:tw-bg-green-700">
                        测试音频历史
                    </button>
                    <button id="test-update-profile" class="tw-bg-yellow-600 tw-text-white tw-px-4 tw-py-2 tw-rounded tw-hover:tw-bg-yellow-700">
                        测试更新Profile
                    </button>
                    <button id="test-logout" class="tw-bg-red-600 tw-text-white tw-px-4 tw-py-2 tw-rounded tw-hover:tw-bg-red-700">
                        测试登出
                    </button>
                </div>
            </div>

            <!-- 测试结果 -->
            <div class="tw-bg-white tw-shadow tw-rounded-lg tw-p-6">
                <h2 class="tw-text-xl tw-font-semibold tw-text-gray-900 tw-mb-4">测试结果</h2>
                <div id="test-results" class="tw-space-y-2 tw-text-sm tw-font-mono tw-bg-gray-100 tw-p-4 tw-rounded tw-max-h-96 tw-overflow-y-auto">
                    <div class="tw-text-gray-500">等待测试...</div>
                </div>
            </div>

            <!-- 页面链接测试 -->
            <div class="tw-bg-white tw-shadow tw-rounded-lg tw-p-6">
                <h2 class="tw-text-xl tw-font-semibold tw-text-gray-900 tw-mb-4">页面导航测试</h2>
                <div class="tw-grid tw-grid-cols-1 md:tw-grid-cols-3 tw-gap-4">
                    <a href="/profile/" class="tw-bg-blue-600 tw-text-white tw-px-4 tw-py-2 tw-rounded tw-hover:tw-bg-blue-700 tw-text-center tw-block">
                        Profile页面
                    </a>
                    <a href="/settings/" class="tw-bg-green-600 tw-text-white tw-px-4 tw-py-2 tw-rounded tw-hover:tw-bg-green-700 tw-text-center tw-block">
                        Settings页面
                    </a>
                    <a href="/audio-history/" class="tw-bg-purple-600 tw-text-white tw-px-4 tw-py-2 tw-rounded tw-hover:tw-bg-purple-700 tw-text-center tw-block">
                        音频历史页面
                    </a>
                </div>
            </div>

            <!-- 注册登录测试 -->
            <div class="tw-bg-white tw-shadow tw-rounded-lg tw-p-6">
                <h2 class="tw-text-xl tw-font-semibold tw-text-gray-900 tw-mb-4">认证页面</h2>
                <div class="tw-grid tw-grid-cols-1 md:tw-grid-cols-2 tw-gap-4">
                    <a href="/register/" class="tw-bg-indigo-600 tw-text-white tw-px-4 tw-py-2 tw-rounded tw-hover:tw-bg-indigo-700 tw-text-center tw-block">
                        注册页面
                    </a>
                    <a href="/login/" class="tw-bg-gray-600 tw-text-white tw-px-4 tw-py-2 tw-rounded tw-hover:tw-bg-gray-700 tw-text-center tw-block">
                        登录页面
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// 从Django传递API配置到前端
window.API_CONFIG = {
    API_BASE_URL: '{{ API_BASE_URL }}/api',
    LOCAL_BASE_URL: '{{ LOCAL_BASE_URL }}'
};
</script>
<script src="{% static 'js/auth_api.js' %}?v=2.0"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const authAPI = new AuthAPI();
    const resultsContainer = document.getElementById('test-results');
    
    // 初始化检查认证状态
    checkAuthStatus();
    
    // 绑定测试按钮事件
    document.getElementById('test-profile').addEventListener('click', testGetProfile);
    document.getElementById('test-history').addEventListener('click', testGetHistory);
    document.getElementById('test-update-profile').addEventListener('click', testUpdateProfile);
    document.getElementById('test-logout').addEventListener('click', testLogout);

    function checkAuthStatus() {
        const isLoggedIn = authAPI.isLoggedIn();
        const currentUser = authAPI.getCurrentUser();
        const token = localStorage.getItem('access_token');
        
        document.getElementById('auth-status').textContent = isLoggedIn ? '已登录' : '未登录';
        document.getElementById('auth-status').className = `tw-font-medium ${isLoggedIn ? 'tw-text-green-600' : 'tw-text-red-600'}`;
        
        if (currentUser) {
            document.getElementById('user-email').textContent = currentUser.email || '-';
            document.getElementById('display-name').textContent = currentUser.display_name || '-';
        }
        
        document.getElementById('token-status').textContent = token ? '存在' : '不存在';
        
        logResult('认证状态检查完成', {
            isLoggedIn,
            currentUser,
            hasToken: !!token
        });
    }

    async function testGetProfile() {
        logResult('开始测试获取Profile...');
        try {
            const response = await authAPI.getUserProfile();
            logResult('获取Profile成功', response);
        } catch (error) {
            logResult('获取Profile失败', error.message);
        }
    }

    async function testGetHistory() {
        logResult('开始测试获取音频历史...');
        try {
            const response = await authAPI.getAudioHistory();
            logResult('获取音频历史成功', response);
        } catch (error) {
            logResult('获取音频历史失败', error.message);
        }
    }

    async function testUpdateProfile() {
        logResult('开始测试更新Profile...');
        try {
            const response = await authAPI.updateUserProfile({
                first_name: '测试名字',
                last_name: '测试姓氏'
            });
            logResult('更新Profile成功', response);
            checkAuthStatus(); // 重新检查状态
        } catch (error) {
            logResult('更新Profile失败', error.message);
        }
    }

    async function testLogout() {
        logResult('开始测试登出...');
        try {
            const response = await authAPI.logout();
            logResult('登出成功', response);
            checkAuthStatus(); // 重新检查状态
        } catch (error) {
            logResult('登出失败', error.message);
        }
    }

    function logResult(message, data = null) {
        const timestamp = new Date().toLocaleTimeString();
        const logEntry = document.createElement('div');
        logEntry.className = 'tw-mb-2';
        
        let content = `[${timestamp}] ${message}`;
        if (data) {
            content += `\n${JSON.stringify(data, null, 2)}`;
        }
        
        logEntry.textContent = content;
        resultsContainer.appendChild(logEntry);
        resultsContainer.scrollTop = resultsContainer.scrollHeight;
    }
});
</script>
{% endblock %}
