{% extends 'html/base.html' %}
{% load static %}

{% block title %}Audio History API Test - HiSage Health{% endblock %}

{% block content %}
<div class="tw-container tw-mx-auto tw-px-4 tw-py-8">
    <div class="tw-max-w-4xl tw-mx-auto">
        <h1 class="tw-text-3xl tw-font-bold tw-text-gray-900 tw-mb-8">音频历史API测试</h1>
        
        <!-- 测试控制面板 -->
        <div class="tw-bg-white tw-rounded-lg tw-shadow-md tw-p-6 tw-mb-8">
            <h2 class="tw-text-xl tw-font-semibold tw-mb-4">测试控制</h2>
            
            <div class="tw-flex tw-gap-4 tw-flex-wrap tw-mb-4">
                <button id="test-login" class="tw-bg-green-600 tw-text-white tw-py-2 tw-px-4 tw-rounded-lg hover:tw-bg-green-700">
                    🔑 模拟登录
                </button>
                
                <button id="test-api" class="tw-bg-blue-600 tw-text-white tw-py-2 tw-px-4 tw-rounded-lg hover:tw-bg-blue-700">
                    📡 测试API
                </button>
                
                <button id="create-test-data" class="tw-bg-purple-600 tw-text-white tw-py-2 tw-px-4 tw-rounded-lg hover:tw-bg-purple-700">
                    🧪 创建测试数据
                </button>
                
                <button id="clear-logs" class="tw-bg-gray-600 tw-text-white tw-py-2 tw-px-4 tw-rounded-lg hover:tw-bg-gray-700">
                    🗑️ 清除日志
                </button>
            </div>
            
            <div class="tw-text-sm tw-text-gray-600">
                <p><strong>当前Token状态：</strong> <span id="token-status">检查中...</span></p>
                <p><strong>API端点：</strong> <code>{{ API_BASE_URL }}/api/audio_history/</code></p>
            </div>
        </div>
        
        <!-- API响应显示 -->
        <div class="tw-bg-white tw-rounded-lg tw-shadow-md tw-p-6 tw-mb-8">
            <h3 class="tw-text-lg tw-font-semibold tw-mb-4">API响应</h3>
            <div id="api-response" class="tw-bg-gray-50 tw-p-4 tw-rounded tw-font-mono tw-text-sm tw-min-h-32">
                点击"测试API"按钮查看响应...
            </div>
        </div>
        
        <!-- 数据解析结果 -->
        <div class="tw-bg-white tw-rounded-lg tw-shadow-md tw-p-6 tw-mb-8">
            <h3 class="tw-text-lg tw-font-semibold tw-mb-4">数据解析结果</h3>
            <div id="parsed-data" class="tw-space-y-4">
                等待API响应...
            </div>
        </div>
        
        <!-- 实时日志 -->
        <div class="tw-bg-black tw-text-green-400 tw-rounded-lg tw-p-4 tw-font-mono tw-text-sm">
            <h3 class="tw-text-white tw-mb-2">实时日志：</h3>
            <div id="debug-log" class="tw-h-64 tw-overflow-y-auto tw-whitespace-pre-wrap"></div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const tokenStatus = document.getElementById('token-status');
    const apiResponse = document.getElementById('api-response');
    const parsedData = document.getElementById('parsed-data');
    const debugLog = document.getElementById('debug-log');
    
    const apiBaseUrl = '{{ API_BASE_URL }}';
    
    // 日志函数
    function log(message) {
        const timestamp = new Date().toLocaleTimeString();
        debugLog.textContent += `[${timestamp}] ${message}\n`;
        debugLog.scrollTop = debugLog.scrollHeight;
        console.log(message);
    }
    
    // 检查Token状态
    function checkTokenStatus() {
        const token = localStorage.getItem('access_token');
        
        if (token) {
            try {
                const payload = JSON.parse(atob(token.split('.')[1]));
                const isExpired = payload.exp * 1000 < Date.now();
                tokenStatus.textContent = isExpired ? '已过期' : '有效';
                tokenStatus.className = isExpired ? 'tw-text-red-600' : 'tw-text-green-600';
                log(`Token状态: ${isExpired ? '已过期' : '有效'}`);
                return !isExpired;
            } catch (e) {
                tokenStatus.textContent = '格式错误';
                tokenStatus.className = 'tw-text-yellow-600';
                log('Token格式错误');
                return false;
            }
        } else {
            tokenStatus.textContent = '不存在';
            tokenStatus.className = 'tw-text-red-600';
            log('Token不存在');
            return false;
        }
    }
    
    // 模拟登录
    document.getElementById('test-login').addEventListener('click', function() {
        log('开始模拟登录...');
        
        const payload = {
            email: '<EMAIL>',
            exp: Math.floor(Date.now() / 1000) + 3600
        };
        
        const mockToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.' + btoa(JSON.stringify(payload)) + '.signature';
        
        localStorage.setItem('access_token', mockToken);
        log('模拟token已设置');
        checkTokenStatus();
    });
    
    // 测试API
    document.getElementById('test-api').addEventListener('click', async function() {
        log('开始测试音频历史API...');
        
        const token = localStorage.getItem('access_token');
        if (!token) {
            log('错误: 没有token，请先模拟登录');
            return;
        }
        
        try {
            const url = `${apiBaseUrl}/api/audio_history/`;
            log(`请求URL: ${url}`);
            
            const response = await fetch(url, {
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });
            
            log(`响应状态: ${response.status} ${response.statusText}`);
            
            if (response.ok) {
                const data = await response.json();
                log('API调用成功');
                
                // 显示原始响应
                apiResponse.textContent = JSON.stringify(data, null, 2);
                
                // 解析数据
                parseApiResponse(data);
                
            } else {
                const errorText = await response.text();
                log(`API错误: ${response.status} - ${errorText}`);
                apiResponse.textContent = `错误 ${response.status}: ${errorText}`;
            }
            
        } catch (error) {
            log(`请求异常: ${error.message}`);
            apiResponse.textContent = `异常: ${error.message}`;
        }
    });
    
    // 解析API响应
    function parseApiResponse(data) {
        let html = '';
        
        html += `<div class="tw-mb-4">`;
        html += `<h4 class="tw-font-semibold">响应结构分析:</h4>`;
        html += `<ul class="tw-list-disc tw-list-inside tw-mt-2">`;
        html += `<li>success: ${data.success || 'undefined'}</li>`;
        html += `<li>count: ${data.count || 'undefined'}</li>`;
        html += `<li>data数组长度: ${data.data ? data.data.length : 'undefined'}</li>`;
        html += `<li>results数组长度: ${data.results ? data.results.length : 'undefined'}</li>`;
        html += `</ul>`;
        html += `</div>`;
        
        // 分析数据数组
        const items = data.data || data.results || [];
        
        if (items.length > 0) {
            html += `<div class="tw-mb-4">`;
            html += `<h4 class="tw-font-semibold">第一条记录字段:</h4>`;
            html += `<ul class="tw-list-disc tw-list-inside tw-mt-2">`;
            
            const firstItem = items[0];
            Object.keys(firstItem).forEach(key => {
                const value = firstItem[key];
                const valueStr = typeof value === 'string' && value.length > 50 
                    ? value.substring(0, 50) + '...' 
                    : String(value);
                html += `<li><strong>${key}:</strong> ${valueStr}</li>`;
            });
            
            html += `</ul>`;
            html += `</div>`;
            
            // 显示所有记录的基本信息
            html += `<div class="tw-mb-4">`;
            html += `<h4 class="tw-font-semibold">所有记录概览:</h4>`;
            html += `<div class="tw-space-y-2 tw-mt-2">`;
            
            items.forEach((item, index) => {
                html += `<div class="tw-bg-gray-100 tw-p-2 tw-rounded">`;
                html += `<strong>记录 ${index + 1}:</strong> `;
                html += `${item.filename || 'N/A'} - `;
                html += `${item.status || 'N/A'} - `;
                html += `${item.upload_time || 'N/A'}`;
                html += `</div>`;
            });
            
            html += `</div>`;
            html += `</div>`;
        } else {
            html += `<div class="tw-text-red-600">没有找到音频分析记录</div>`;
        }
        
        parsedData.innerHTML = html;
        log(`解析完成，找到 ${items.length} 条记录`);
    }
    
    // 创建测试数据
    document.getElementById('create-test-data').addEventListener('click', async function() {
        log('开始创建测试数据...');
        
        const token = localStorage.getItem('access_token');
        if (!token) {
            log('错误: 没有token，请先模拟登录');
            return;
        }
        
        try {
            // 这里应该调用后端的测试数据创建API
            log('注意: 需要后端支持创建测试数据的API');
            log('或者手动在数据库中创建测试数据');
            
        } catch (error) {
            log(`创建测试数据失败: ${error.message}`);
        }
    });
    
    // 清除日志
    document.getElementById('clear-logs').addEventListener('click', function() {
        debugLog.textContent = '';
        log('日志已清除');
    });
    
    // 初始化
    log('音频历史API测试页面已加载');
    checkTokenStatus();
});
</script>
{% endblock %}
