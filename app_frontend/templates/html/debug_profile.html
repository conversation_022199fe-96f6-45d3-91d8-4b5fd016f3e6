{% extends 'html/base.html' %}
{% load static %}

{% block title %}Profile Debug - HiSage Health{% endblock %}

{% block content %}
<div class="tw-container tw-mx-auto tw-px-4 tw-py-8">
    <div class="tw-max-w-4xl tw-mx-auto">
        <h1 class="tw-text-3xl tw-font-bold tw-text-gray-900 tw-mb-8">Profile调试页面</h1>
        
        <!-- 调试信息 -->
        <div class="tw-bg-gray-50 tw-border tw-border-gray-200 tw-rounded-lg tw-p-6 tw-mb-8">
            <h2 class="tw-text-xl tw-font-semibold tw-text-gray-900 tw-mb-4">调试信息</h2>
            
            <div class="tw-space-y-4">
                <div>
                    <strong>当前Token状态：</strong>
                    <span id="token-status" class="tw-font-mono tw-text-sm">检查中...</span>
                </div>
                
                <div>
                    <strong>Profile元素状态：</strong>
                    <div id="element-status" class="tw-font-mono tw-text-sm tw-mt-2"></div>
                </div>
                
                <div>
                    <strong>API配置：</strong>
                    <div class="tw-font-mono tw-text-sm tw-mt-2">
                        <div>API_BASE_URL: {{ API_BASE_URL }}</div>
                        <div>LOCAL_BASE_URL: {{ LOCAL_BASE_URL }}</div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 手动测试按钮 -->
        <div class="tw-bg-white tw-rounded-lg tw-shadow-md tw-p-6 tw-mb-8">
            <h3 class="tw-text-lg tw-font-semibold tw-mb-4">手动测试</h3>
            
            <div class="tw-space-y-4">
                <button id="test-login" class="tw-bg-blue-600 tw-text-white tw-py-2 tw-px-4 tw-rounded-lg hover:tw-bg-blue-700">
                    模拟登录状态
                </button>
                
                <button id="test-logout" class="tw-bg-red-600 tw-text-white tw-py-2 tw-px-4 tw-rounded-lg hover:tw-bg-red-700">
                    清除登录状态
                </button>
                
                <button id="test-api" class="tw-bg-green-600 tw-text-white tw-py-2 tw-px-4 tw-rounded-lg hover:tw-bg-green-700">
                    测试Profile API
                </button>
                
                <button id="refresh-profile" class="tw-bg-purple-600 tw-text-white tw-py-2 tw-px-4 tw-rounded-lg hover:tw-bg-purple-700">
                    刷新Profile状态
                </button>
            </div>
        </div>
        
        <!-- 实时日志 -->
        <div class="tw-bg-black tw-text-green-400 tw-rounded-lg tw-p-4 tw-font-mono tw-text-sm">
            <h3 class="tw-text-white tw-mb-2">实时日志：</h3>
            <div id="debug-log" class="tw-h-64 tw-overflow-y-auto tw-whitespace-pre-wrap"></div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const tokenStatus = document.getElementById('token-status');
    const elementStatus = document.getElementById('element-status');
    const debugLog = document.getElementById('debug-log');
    
    // 日志函数
    function log(message) {
        const timestamp = new Date().toLocaleTimeString();
        debugLog.textContent += `[${timestamp}] ${message}\n`;
        debugLog.scrollTop = debugLog.scrollHeight;
        console.log(message);
    }
    
    // 检查元素状态
    function checkElementStatus() {
        const authButtons = document.getElementById('auth-buttons');
        const userProfile = document.getElementById('user-profile');
        const profileAvatar = document.getElementById('profile-avatar');
        
        let status = '';
        status += `auth-buttons: ${authButtons ? '存在' : '不存在'} (display: ${authButtons?.style.display || 'default'})\n`;
        status += `user-profile: ${userProfile ? '存在' : '不存在'} (display: ${userProfile?.style.display || 'default'})\n`;
        status += `profile-avatar: ${profileAvatar ? '存在' : '不存在'}\n`;
        
        elementStatus.textContent = status;
        log('元素状态检查完成');
    }
    
    // 检查Token状态
    function checkTokenStatus() {
        const token = localStorage.getItem('access_token');
        const refreshToken = localStorage.getItem('refresh_token');
        
        if (token) {
            try {
                const payload = JSON.parse(atob(token.split('.')[1]));
                const isExpired = payload.exp * 1000 < Date.now();
                tokenStatus.textContent = `存在 (${isExpired ? '已过期' : '有效'})`;
                tokenStatus.className = isExpired ? 'tw-text-red-600' : 'tw-text-green-600';
                log(`Token状态: ${isExpired ? '已过期' : '有效'}`);
            } catch (e) {
                tokenStatus.textContent = '存在但格式错误';
                tokenStatus.className = 'tw-text-yellow-600';
                log('Token格式错误');
            }
        } else {
            tokenStatus.textContent = '不存在';
            tokenStatus.className = 'tw-text-red-600';
            log('Token不存在');
        }
    }
    
    // 模拟登录
    document.getElementById('test-login').addEventListener('click', function() {
        log('开始模拟登录...');
        
        // 创建模拟token
        const payload = {
            user_id: 1,
            email: '<EMAIL>',
            exp: Math.floor(Date.now() / 1000) + 3600 // 1小时后过期
        };
        
        const mockToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.' + btoa(JSON.stringify(payload)) + '.signature';
        
        localStorage.setItem('access_token', mockToken);
        localStorage.setItem('refresh_token', 'mock_refresh_token');
        
        log('模拟token已设置');
        checkTokenStatus();
        
        // 触发profile检查
        if (window.userProfile) {
            window.userProfile.checkAuthStatus();
            log('已触发profile状态检查');
        }
    });
    
    // 清除登录状态
    document.getElementById('test-logout').addEventListener('click', function() {
        log('清除登录状态...');
        localStorage.removeItem('access_token');
        localStorage.removeItem('refresh_token');
        
        checkTokenStatus();
        
        if (window.userProfile) {
            window.userProfile.checkAuthStatus();
            log('已触发profile状态检查');
        }
    });
    
    // 测试API
    document.getElementById('test-api').addEventListener('click', async function() {
        log('测试Profile API...');
        
        const token = localStorage.getItem('access_token');
        if (!token) {
            log('错误: 没有token');
            return;
        }
        
        try {
            const apiUrl = '{{ API_BASE_URL }}/api/profile/';
            log(`请求URL: ${apiUrl}`);
            
            const response = await fetch(apiUrl, {
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });
            
            log(`API响应状态: ${response.status}`);
            
            if (response.ok) {
                const data = await response.json();
                log(`API响应数据: ${JSON.stringify(data, null, 2)}`);
            } else {
                const errorText = await response.text();
                log(`API错误响应: ${errorText}`);
            }
        } catch (error) {
            log(`API请求异常: ${error.message}`);
        }
    });
    
    // 刷新Profile状态
    document.getElementById('refresh-profile').addEventListener('click', function() {
        log('刷新Profile状态...');
        checkTokenStatus();
        checkElementStatus();
        
        if (window.userProfile) {
            window.userProfile.checkAuthStatus();
            log('已触发profile状态检查');
        }
    });
    
    // 初始化
    log('调试页面已加载');
    checkTokenStatus();
    checkElementStatus();
    
    // 定期检查状态
    setInterval(() => {
        checkElementStatus();
    }, 2000);
});
</script>
{% endblock %}
