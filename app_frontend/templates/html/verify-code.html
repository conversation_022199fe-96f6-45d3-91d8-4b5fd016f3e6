{% load static %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Email Verification - Cognitive Health</title>
    <link rel="stylesheet" href="{% static 'css/auth.css' %}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .verify-code-container {
            max-width: 500px;
            margin: 50px auto;
            padding: 40px;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            text-align: center;
        }

        .verify-code-header {
            margin-bottom: 30px;
        }

        .verify-code-header h1 {
            color: #333;
            margin-bottom: 10px;
            font-size: 28px;
        }

        .verify-code-header p {
            color: #666;
            font-size: 16px;
            line-height: 1.5;
        }

        .email-display {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            font-weight: bold;
            color: #495057;
        }

        .code-input-container {
            margin: 30px 0;
        }

        .code-input {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin: 20px 0;
        }

        .code-digit {
            width: 50px;
            height: 60px;
            border: 2px solid #ddd;
            border-radius: 8px;
            text-align: center;
            font-size: 24px;
            font-weight: bold;
            outline: none;
            transition: all 0.3s ease;
        }

        .code-digit:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .code-digit.filled {
            background: #f0f8ff;
            border-color: #667eea;
        }

        .verify-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 40px;
            border-radius: 25px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 20px 0;
            width: 100%;
        }

        .verify-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }

        .verify-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .resend-section {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #eee;
        }

        .resend-btn {
            background: none;
            border: 1px solid #667eea;
            color: #667eea;
            padding: 10px 20px;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .resend-btn:hover {
            background: #667eea;
            color: white;
        }

        .resend-btn:disabled {
            border-color: #ccc;
            color: #ccc;
            cursor: not-allowed;
        }

        .countdown {
            color: #666;
            font-size: 14px;
            margin-top: 10px;
        }

        .alert {
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            font-size: 14px;
        }

        .alert-success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }

        .alert-error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }

        .alert-info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }

        .back-to-login {
            margin-top: 20px;
        }

        .back-to-login a {
            color: #667eea;
            text-decoration: none;
            font-size: 14px;
        }

        .back-to-login a:hover {
            text-decoration: underline;
        }

        .loading {
            display: none;
            margin: 10px 0;
        }

        .spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            animation: spin 1s linear infinite;
            display: inline-block;
            margin-right: 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="auth-container">
        <div class="verify-code-container">
            <div class="verify-code-header">
                <h1><i class="fas fa-shield-alt"></i> Email Verification</h1>
                <p>We have sent a 6-digit verification code to your email</p>
                <div class="email-display" id="emailDisplay">
                    <i class="fas fa-envelope"></i>
                    <span id="userEmail"></span>
                </div>
            </div>

            <div id="messageDiv"></div>

            <form id="verifyCodeForm">
                <div class="code-input-container">
                    <label for="code-input">Please enter verification code:</label>
                    <div class="code-input">
                        <input type="text" class="code-digit" maxlength="1" data-index="0">
                        <input type="text" class="code-digit" maxlength="1" data-index="1">
                        <input type="text" class="code-digit" maxlength="1" data-index="2">
                        <input type="text" class="code-digit" maxlength="1" data-index="3">
                        <input type="text" class="code-digit" maxlength="1" data-index="4">
                        <input type="text" class="code-digit" maxlength="1" data-index="5">
                    </div>
                </div>

                <button type="submit" class="verify-btn" id="verifyBtn">
                    <i class="fas fa-check"></i> Verify and Activate Account
                </button>

                <div class="loading" id="loadingDiv">
                    <div class="spinner"></div>
                    Verifying...
                </div>
            </form>

            <div class="resend-section">
                <p>Didn't receive the verification code?</p>
                <button type="button" class="resend-btn" id="resendBtn">
                    <i class="fas fa-paper-plane"></i> Resend Verification Code
                </button>
                <div class="countdown" id="countdownDiv"></div>
            </div>

            <div class="back-to-login">
                <a href="/login"><i class="fas fa-arrow-left"></i> Back to Login Page</a>
            </div>
        </div>
    </div>

    <script>
        // Pass API configuration from Django to frontend
        window.API_BASE_URL = "{{ API_BASE_URL }}";
        window.LOCAL_BASE_URL = "{{ LOCAL_BASE_URL }}";
        window.API_CONFIG = {
            API_BASE_URL: '{{ API_BASE_URL }}/api',
            LOCAL_BASE_URL: '{{ LOCAL_BASE_URL }}'
        };
        // Mark this page has custom event handlers to prevent auth_api.js from duplicate binding
        window.customAuthHandlers = true;

        // Debug information
        console.log('🔧 Verify code page - API_BASE_URL:', window.API_BASE_URL);
        console.log('🔧 Verify code page - API_CONFIG:', window.API_CONFIG);
    </script>
    <script src="{% static 'js/auth_api.js' %}?v=2.0"></script>
    <script>
        // Get email from URL parameters or localStorage
        const urlParams = new URLSearchParams(window.location.search);
        const email = urlParams.get('email') || localStorage.getItem('pendingActivationEmail') || '';

        if (email) {
            document.getElementById('userEmail').textContent = email;
        } else {
            // If no email information, redirect to registration page
            window.location.href = '/register';
        }

        // Verification code input handling
        const codeInputs = document.querySelectorAll('.code-digit');
        const verifyBtn = document.getElementById('verifyBtn');
        const resendBtn = document.getElementById('resendBtn');
        const messageDiv = document.getElementById('messageDiv');
        const loadingDiv = document.getElementById('loadingDiv');
        const countdownDiv = document.getElementById('countdownDiv');

        // Verification code input logic
        codeInputs.forEach((input, index) => {
            input.addEventListener('input', (e) => {
                const value = e.target.value;

                // Only allow numbers
                if (!/^\d$/.test(value)) {
                    e.target.value = '';
                    return;
                }

                e.target.classList.add('filled');

                // Auto jump to next input box
                if (value && index < codeInputs.length - 1) {
                    codeInputs[index + 1].focus();
                }

                // Check if all input boxes are filled
                updateVerifyButton();
            });

            input.addEventListener('keydown', (e) => {
                // Backspace key handling
                if (e.key === 'Backspace' && !e.target.value && index > 0) {
                    codeInputs[index - 1].focus();
                    codeInputs[index - 1].classList.remove('filled');
                }
            });

            input.addEventListener('paste', (e) => {
                e.preventDefault();
                const pastedData = e.clipboardData.getData('text');
                const digits = pastedData.replace(/\D/g, '').slice(0, 6);
                
                digits.split('').forEach((digit, i) => {
                    if (codeInputs[i]) {
                        codeInputs[i].value = digit;
                        codeInputs[i].classList.add('filled');
                    }
                });
                
                updateVerifyButton();
            });
        });

        function updateVerifyButton() {
            const allFilled = Array.from(codeInputs).every(input => input.value);
            verifyBtn.disabled = !allFilled;
        }

        function getVerificationCode() {
            return Array.from(codeInputs).map(input => input.value).join('');
        }

        function clearCode() {
            codeInputs.forEach(input => {
                input.value = '';
                input.classList.remove('filled');
            });
            updateVerifyButton();
        }

        function showMessage(message, type = 'info') {
            messageDiv.innerHTML = `
                <div class="alert alert-${type}">
                    <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
                    ${message}
                </div>
            `;
        }

        // Verification code submission
        document.getElementById('verifyCodeForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const verificationCode = getVerificationCode();
            if (verificationCode.length !== 6) {
                showMessage('Please enter the complete 6-digit verification code', 'error');
                return;
            }

            verifyBtn.disabled = true;
            loadingDiv.style.display = 'block';
            messageDiv.innerHTML = '';

            try {
                // Create AuthAPI instance
                const authAPI = new AuthAPI();
                const result = await authAPI.verifyCode(email, verificationCode);

                if (result.success) {
                    if (result.auto_login && result.tokens) {
                        // Store JWT tokens for automatic login
                        localStorage.setItem('access_token', result.tokens.access);
                        localStorage.setItem('refresh_token', result.tokens.refresh);
                        localStorage.setItem('user_data', JSON.stringify(result.user));

                        showMessage('Account activated successfully! You are now logged in. Redirecting to home page...', 'success');
                        localStorage.removeItem('pendingActivationEmail');

                        // Redirect to home page after successful activation and login
                        setTimeout(() => {
                            window.location.href = '/';
                        }, 2000);
                    } else {
                        // Fallback to login page if auto_login is not available
                        showMessage('Account activated successfully! Redirecting to login page...', 'success');
                        localStorage.removeItem('pendingActivationEmail');
                        setTimeout(() => {
                            window.location.href = '/login?message=Account activated successfully, please log in';
                        }, 2000);
                    }
                } else {
                    showMessage(result.message || 'Verification failed, please check the verification code', 'error');
                    clearCode();
                    codeInputs[0].focus();
                }
            } catch (error) {
                showMessage('Network error, please try again later', 'error');
                clearCode();
                codeInputs[0].focus();
            } finally {
                verifyBtn.disabled = false;
                loadingDiv.style.display = 'none';
                updateVerifyButton();
            }
        });

        // Resend verification code
        let resendCountdown = 0;

        function startResendCountdown() {
            resendCountdown = 60;
            resendBtn.disabled = true;

            const timer = setInterval(() => {
                countdownDiv.textContent = `Can resend in ${resendCountdown} seconds`;
                resendCountdown--;

                if (resendCountdown < 0) {
                    clearInterval(timer);
                    resendBtn.disabled = false;
                    countdownDiv.textContent = '';
                }
            }, 1000);
        }

        resendBtn.addEventListener('click', async () => {
            resendBtn.disabled = true;
            messageDiv.innerHTML = '';

            try {
                // Create AuthAPI instance
                const authAPI = new AuthAPI();
                const result = await authAPI.resendVerification(email);

                if (result.success) {
                    showMessage('Verification code has been resent, please check your email', 'success');
                    startResendCountdown();
                    clearCode();
                    codeInputs[0].focus();
                } else {
                    showMessage(result.message || 'Failed to send, please try again later', 'error');
                    resendBtn.disabled = false;
                }
            } catch (error) {
                showMessage('Network error, please try again later', 'error');
                resendBtn.disabled = false;
            }
        });

        // Auto focus on the first input box when page loads
        codeInputs[0].focus();

        // Handle messages from URL parameters
        const message = urlParams.get('message');
        if (message) {
            showMessage(message, 'info');
        }
    </script>
</body>
</html>
