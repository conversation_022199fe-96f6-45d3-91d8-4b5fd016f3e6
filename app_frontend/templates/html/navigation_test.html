{% extends 'base.html' %}
{% load static %}

{% block content %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Navigation Test - HiSage Health</title>
    <script src="{% static 'js/global_navigation.js' %}?v=1.0"></script>
    <style>
        body {
            font-family: 'Inter', sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8fafc;
            padding: 80px 20px 20px;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 2rem;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .test-section {
            margin: 2rem 0;
            padding: 1.5rem;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            background: #f8fafc;
        }
        
        .btn {
            display: inline-block;
            padding: 0.75rem 1.5rem;
            margin: 0.5rem;
            background: #3b82f6;
            color: white;
            text-decoration: none;
            border-radius: 6px;
            border: none;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        
        .btn:hover {
            background: #2563eb;
        }
        
        .btn-secondary {
            background: #6b7280;
        }
        
        .btn-secondary:hover {
            background: #4b5563;
        }
        
        .btn-success {
            background: #10b981;
        }
        
        .btn-success:hover {
            background: #059669;
        }
        
        .auth-status {
            padding: 1rem;
            border-radius: 6px;
            margin: 1rem 0;
        }
        
        .authenticated {
            background: #d1fae5;
            color: #065f46;
            border: 1px solid #a7f3d0;
        }
        
        .not-authenticated {
            background: #fee2e2;
            color: #991b1b;
            border: 1px solid #fecaca;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 Authentication Navigation Test</h1>
        <p>This page demonstrates the global authentication navigation system.</p>
        
        <div id="auth-status" class="auth-status">
            <strong>Authentication Status:</strong> <span id="auth-text">Checking...</span>
        </div>
        
        <div class="test-section">
            <h3>🔗 Login Links (Return to Current Page)</h3>
            <p>These links will take you to login and return you to this page after successful authentication.</p>
            
            <a href="/login/" class="btn" data-login-link="true">Login Link (Data Attribute)</a>
            <button class="btn" onclick="goToLogin()">Login Button (JavaScript)</button>
        </div>
        
        <div class="test-section">
            <h3>🚪 Navigation Links (No Authentication Required)</h3>
            <p>These links navigate directly without authentication checks.</p>
            
            <a href="/about/" class="btn btn-secondary" data-nav-link="true">About Us</a>
            <a href="/contact/" class="btn btn-secondary" data-nav-link="true">Contact Us</a>
            <a href="/message_board/" class="btn btn-secondary" data-nav-link="true">Message Board</a>
        </div>
        
        <div class="test-section">
            <h3>🔒 Protected Links (Authentication Required)</h3>
            <p>These links will check authentication first. If not logged in, you'll go to login then to the target page.</p>
            
            <a href="/user/profile/" class="btn btn-success" data-auth-required="true">User Profile</a>
            <a href="/audio_upload/history/" class="btn btn-success" data-auth-required="true">Audio History</a>
            <a href="/dashboard/" class="btn btn-success" data-auth-required="true">Dashboard</a>
        </div>
        
        <div class="test-section">
            <h3>⚡ JavaScript Functions</h3>
            <p>Test the JavaScript functions directly.</p>
            
            <button class="btn" onclick="navigateTo('/about/', false)">Navigate to About (No Auth)</button>
            <button class="btn btn-success" onclick="navigateTo('/user/profile/', true)">Navigate to Profile (Auth Required)</button>
            <button class="btn btn-secondary" onclick="checkAuthStatus()">Check Auth Status</button>
        </div>
        
        <div class="test-section">
            <h3>📋 Test Results</h3>
            <div id="test-results" style="background: white; padding: 1rem; border-radius: 4px; min-height: 100px;">
                <em>Test results will appear here...</em>
            </div>
        </div>
    </div>

    <script>
        // Update authentication status display
        function updateAuthStatus() {
            const authStatus = document.getElementById('auth-status');
            const authText = document.getElementById('auth-text');
            
            if (globalNav.isAuthenticated()) {
                authStatus.className = 'auth-status authenticated';
                authText.textContent = 'Authenticated ✅';
            } else {
                authStatus.className = 'auth-status not-authenticated';
                authText.textContent = 'Not Authenticated ❌';
            }
        }
        
        // Check authentication status and log results
        function checkAuthStatus() {
            const results = document.getElementById('test-results');
            const isAuth = globalNav.isAuthenticated();
            const token = localStorage.getItem('access_token');

            results.innerHTML = `
                <h4>Authentication Check Results:</h4>
                <p><strong>Is Authenticated:</strong> ${isAuth}</p>
                <p><strong>Has Token:</strong> ${!!token}</p>
                <p><strong>Token Preview:</strong> ${token ? token.substring(0, 50) + '...' : 'None'}</p>
                <p><strong>Current URL:</strong> ${window.location.href}</p>
                <p><strong>Saved Return URL:</strong> ${sessionStorage.getItem('navigation_return_url') || 'None'}</p>
            `;
        }
        
        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🔐 Navigation test page loaded');
            updateAuthStatus();
            
            // Update status every 5 seconds
            setInterval(updateAuthStatus, 5000);
        });
    </script>
</body>
</html>
{% endblock %}
