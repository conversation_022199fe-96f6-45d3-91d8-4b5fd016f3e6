<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Configuration Debug</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 2rem;
            background: #f5f5f5;
        }
        .debug-container {
            background: white;
            padding: 2rem;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 1rem;
        }
        .status {
            padding: 1rem;
            border-radius: 4px;
            margin: 1rem 0;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        pre { background: #f8f9fa; padding: 1rem; border-radius: 4px; overflow-x: auto; }
    </style>
</head>
<body>
    <div class="debug-container">
        <h1>Configuration Debug</h1>
        
        <h2>Django Template Variables</h2>
        <pre>API_BASE_URL: {{ API_BASE_URL }}
LOCAL_BASE_URL: {{ LOCAL_BASE_URL }}
CSRF_TOKEN: {{ csrf_token }}</pre>

        <h2>JavaScript Configuration</h2>
        <div id="js-status" class="status warning">Testing JavaScript configuration...</div>
        <pre id="js-config"></pre>

        <div style="margin-top: 2rem;">
            <a href="/user/settings/" style="
                display: inline-block;
                background: #007bff;
                color: white;
                padding: 0.75rem 1.5rem;
                text-decoration: none;
                border-radius: 4px;
                margin-right: 0.5rem;
            ">Go to Settings</a>
            <a href="/user/settings-test/" style="
                display: inline-block;
                background: #28a745;
                color: white;
                padding: 0.75rem 1.5rem;
                text-decoration: none;
                border-radius: 4px;
            ">Go to Settings Test</a>
        </div>
    </div>

    <script>
        // Use Django template variables to pass configuration
        window.API_BASE_URL = "{{ API_BASE_URL }}";
        window.LOCAL_BASE_URL = "{{ LOCAL_BASE_URL }}";
        window.CSRF_TOKEN = "{{ csrf_token }}";

        // Fallback configuration (if template variables are empty)
        if (!window.API_BASE_URL || window.API_BASE_URL === '') {
            window.API_BASE_URL = "http://**************:8001";
        }
        if (!window.LOCAL_BASE_URL || window.LOCAL_BASE_URL === '') {
            window.LOCAL_BASE_URL = "http://**************:8000";
        }

        document.addEventListener('DOMContentLoaded', function() {
            const jsStatus = document.getElementById('js-status');
            const jsConfig = document.getElementById('js-config');
            
            const config = {
                API_BASE_URL: window.API_BASE_URL,
                LOCAL_BASE_URL: window.LOCAL_BASE_URL,
                CSRF_TOKEN: window.CSRF_TOKEN ? 'Present' : 'Missing',
                access_token: localStorage.getItem('access_token') ? 'Present' : 'Missing'
            };
            
            jsConfig.textContent = JSON.stringify(config, null, 2);
            
            if (window.API_BASE_URL && window.API_BASE_URL !== '' && 
                window.LOCAL_BASE_URL && window.LOCAL_BASE_URL !== '') {
                jsStatus.className = 'status success';
                jsStatus.textContent = '✅ JavaScript configuration loaded successfully';
            } else {
                jsStatus.className = 'status error';
                jsStatus.textContent = '❌ JavaScript configuration failed';
            }
            
            console.log('🔧 Configuration debug:', config);
        });
    </script>
</body>
</html>
