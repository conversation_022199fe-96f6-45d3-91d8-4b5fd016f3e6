<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Message Test - HiSage Health</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <style>
        body {
            background-color: #f8f9fa;
            padding: 2rem;
        }
        .debug-box {
            background: #fff;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
        }
        .message-item {
            background: #e9ecef;
            border-radius: 5px;
            padding: 1rem;
            margin-bottom: 0.5rem;
        }
        .console-log {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 1rem;
            border-radius: 5px;
            font-family: monospace;
            font-size: 0.9rem;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Simple Message Management Test</h1>
        <p>This is a simplified test page to debug message loading issues.</p>
        
        <!-- Status Display -->
        <div class="debug-box">
            <h3>System Status</h3>
            <p><strong>API Base URL:</strong> <span id="api-url">{{ API_BASE_URL }}</span></p>
            <p><strong>Local Base URL:</strong> <span id="local-url">{{ LOCAL_BASE_URL }}</span></p>
            <p><strong>Page Loaded:</strong> <span id="page-status">Loading...</span></p>
            <p><strong>JavaScript Working:</strong> <span id="js-status">Testing...</span></p>
            <p><strong>Authentication:</strong> <span id="auth-status">Checking...</span></p>
        </div>
        
        <!-- Action Buttons -->
        <div class="debug-box">
            <h3>Actions</h3>
            <button class="btn btn-primary me-2" onclick="testBasicAuth()">Test Authentication</button>
            <button class="btn btn-success me-2" onclick="testMessagesAPI()">Test Messages API</button>
            <button class="btn btn-info me-2" onclick="loadAndDisplayMessages()">Load & Display Messages</button>
            <button class="btn btn-secondary" onclick="clearLogs()">Clear Logs</button>
        </div>
        
        <!-- Messages Display -->
        <div class="debug-box">
            <h3>Messages</h3>
            <div id="messages-display">
                <p class="text-muted">Messages will appear here...</p>
            </div>
        </div>
        
        <!-- Console Logs -->
        <div class="debug-box">
            <h3>Console Logs</h3>
            <div id="console-logs" class="console-log">
                Console logs will appear here...
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Immediate execution test
        console.log('🚀 Simple test page script started');
        
        // Configuration
        const API_BASE_URL = '{{ API_BASE_URL }}';
        const LOCAL_BASE_URL = '{{ LOCAL_BASE_URL }}';
        
        console.log('🔧 Configuration:', { API_BASE_URL, LOCAL_BASE_URL });
        
        // Console log capture
        const logBuffer = [];
        const originalLog = console.log;
        const originalError = console.error;
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            const message = `[${new Date().toLocaleTimeString()}] ${args.join(' ')}`;
            logBuffer.push(message);
            updateConsoleLogs();
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            const message = `[ERROR ${new Date().toLocaleTimeString()}] ${args.join(' ')}`;
            logBuffer.push(message);
            updateConsoleLogs();
        };
        
        function updateConsoleLogs() {
            const logsDiv = document.getElementById('console-logs');
            if (logsDiv) {
                logsDiv.textContent = logBuffer.slice(-20).join('\n');
                logsDiv.scrollTop = logsDiv.scrollHeight;
            }
        }
        
        function clearLogs() {
            logBuffer.length = 0;
            updateConsoleLogs();
        }
        
        // Page initialization
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🔧 DOMContentLoaded fired');
            
            // Update status displays
            document.getElementById('page-status').textContent = 'Loaded';
            document.getElementById('js-status').textContent = 'Working';
            
            // Test authentication
            testBasicAuth();
        });
        
        function testBasicAuth() {
            console.log('🔐 Testing authentication...');
            
            const token = localStorage.getItem('access_token');
            const userInfo = localStorage.getItem('user_info');
            
            console.log('Token exists:', !!token);
            console.log('User info exists:', !!userInfo);
            
            if (token) {
                try {
                    // Try to parse user info
                    if (userInfo) {
                        const user = JSON.parse(userInfo);
                        console.log('User data:', user);
                        document.getElementById('auth-status').innerHTML = 
                            `✅ Authenticated as ${user.email} (Staff: ${user.is_staff})`;
                    } else {
                        document.getElementById('auth-status').textContent = '⚠️ Token exists but no user info';
                    }
                } catch (e) {
                    console.error('Error parsing user info:', e);
                    document.getElementById('auth-status').textContent = '❌ Error parsing user data';
                }
            } else {
                document.getElementById('auth-status').textContent = '❌ Not authenticated';
            }
        }
        
        async function testMessagesAPI() {
            console.log('📡 Testing Messages API...');
            
            const token = localStorage.getItem('access_token');
            if (!token) {
                console.error('No access token found');
                return;
            }
            
            try {
                const response = await fetch(`${API_BASE_URL}/api/contact/messages/?page=1&page_size=10`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                console.log('API Response status:', response.status);
                
                if (response.ok) {
                    const data = await response.json();
                    console.log('API Response data:', data);
                    
                    if (data.success && data.data && data.data.messages) {
                        console.log(`✅ API Success: Found ${data.data.messages.length} messages`);
                        return data.data.messages;
                    } else {
                        console.error('❌ API returned invalid structure:', data);
                    }
                } else {
                    const errorText = await response.text();
                    console.error('❌ API Error:', response.status, errorText);
                }
            } catch (error) {
                console.error('❌ Network Error:', error);
            }
            
            return null;
        }
        
        async function loadAndDisplayMessages() {
            console.log('📋 Loading and displaying messages...');
            
            const messages = await testMessagesAPI();
            const displayDiv = document.getElementById('messages-display');
            
            if (!messages || messages.length === 0) {
                displayDiv.innerHTML = '<p class="text-warning">No messages found or API error</p>';
                return;
            }
            
            console.log(`🎨 Rendering ${messages.length} messages`);
            
            const messagesHtml = messages.map((message, index) => {
                const date = message.created_at ? new Date(message.created_at).toLocaleDateString() : 'N/A';
                return `
                    <div class="message-item">
                        <strong>${message.name || 'Anonymous'}</strong> (${message.email || 'No email'})
                        <br><small>${message.inquiry_type_display || 'General'} - ${message.status_display || 'New'} - ${date}</small>
                        <br>${message.description || 'No description'}
                    </div>
                `;
            }).join('');
            
            displayDiv.innerHTML = messagesHtml;
            console.log('✅ Messages rendered successfully');
        }
        
        // Test JavaScript execution
        console.log('🔧 All functions defined, page ready');
    </script>
</body>
</html>
