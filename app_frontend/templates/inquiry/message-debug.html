<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Message Management Debug - HiSage Health</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Inter', sans-serif;
        }
        .debug-header {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
        }
        .debug-card {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 1rem;
            padding: 1.5rem;
        }
        .status-good { color: #27ae60; }
        .status-bad { color: #e74c3c; }
        .status-warning { color: #f39c12; }
        .code-block {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 1rem;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            margin: 1rem 0;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <!-- Debug Header -->
    <div class="debug-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1><i class="fas fa-bug me-3"></i>Message Management Debug</h1>
                    <p class="mb-0">Diagnostic tool for troubleshooting message management issues</p>
                </div>
                <div class="col-md-4 text-end">
                    <a href="/message/" class="btn btn-outline-light me-2">
                        <i class="fas fa-envelope me-1"></i>Messages
                    </a>
                    <a href="/dashboard/" class="btn btn-outline-light">
                        <i class="fas fa-tachometer-alt me-1"></i>Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- System Status -->
        <div class="debug-card">
            <h4><i class="fas fa-heartbeat me-2"></i>System Status</h4>
            <div class="row">
                <div class="col-md-6">
                    <p><strong>Frontend URL:</strong> <span id="frontend-url">{{ LOCAL_BASE_URL }}</span></p>
                    <p><strong>Backend URL:</strong> <span id="backend-url">{{ API_BASE_URL }}</span></p>
                    <p><strong>Current Page:</strong> <span id="current-page">Loading...</span></p>
                </div>
                <div class="col-md-6">
                    <p><strong>User Agent:</strong> <span id="user-agent">Loading...</span></p>
                    <p><strong>Timestamp:</strong> <span id="timestamp">Loading...</span></p>
                </div>
            </div>
        </div>

        <!-- Authentication Status -->
        <div class="debug-card">
            <h4><i class="fas fa-key me-2"></i>Authentication Status</h4>
            <div class="row">
                <div class="col-md-6">
                    <p><strong>Access Token:</strong> <span id="access-token-status">Checking...</span></p>
                    <p><strong>Refresh Token:</strong> <span id="refresh-token-status">Checking...</span></p>
                    <p><strong>User Info:</strong> <span id="user-info-status">Checking...</span></p>
                </div>
                <div class="col-md-6">
                    <p><strong>Is Authenticated:</strong> <span id="is-authenticated">Checking...</span></p>
                    <p><strong>Token Length:</strong> <span id="token-length">Checking...</span></p>
                    <button class="btn btn-sm btn-primary" onclick="checkAuth()">
                        <i class="fas fa-sync me-1"></i>Refresh Auth Status
                    </button>
                </div>
            </div>
        </div>

        <!-- API Tests -->
        <div class="debug-card">
            <h4><i class="fas fa-plug me-2"></i>API Connection Tests</h4>
            <div class="row">
                <div class="col-md-12">
                    <button class="btn btn-primary me-2" onclick="testBackendHealth()">
                        <i class="fas fa-heartbeat me-1"></i>Test Backend Health
                    </button>
                    <button class="btn btn-warning me-2" onclick="testMessagesAPI()">
                        <i class="fas fa-envelope me-1"></i>Test Messages API
                    </button>
                    <button class="btn btn-info me-2" onclick="testContactAPI()">
                        <i class="fas fa-paper-plane me-1"></i>Test Contact API
                    </button>
                    <button class="btn btn-secondary" onclick="clearResults()">
                        <i class="fas fa-trash me-1"></i>Clear Results
                    </button>
                </div>
            </div>
            <div id="api-results" class="mt-3">
                <!-- API test results will appear here -->
            </div>
        </div>

        <!-- Database Check -->
        <div class="debug-card">
            <h4><i class="fas fa-database me-2"></i>Database Information</h4>
            <p>To check the database directly, run these commands in the backend directory:</p>
            <div class="code-block">
python manage.py shell

from api.models import Inquiry
print(f"Total inquiries: {Inquiry.objects.count()}")

for inquiry in Inquiry.objects.all()[:5]:
    print(f"  {inquiry.email}: {inquiry.description[:50]}...")
            </div>
        </div>

        <!-- Console Logs -->
        <div class="debug-card">
            <h4><i class="fas fa-terminal me-2"></i>Console Logs</h4>
            <p>Check the browser console (F12) for detailed logs. Recent logs will appear here:</p>
            <div id="console-logs" class="code-block" style="max-height: 200px;">
                Console logs will appear here...
            </div>
            <button class="btn btn-sm btn-secondary" onclick="clearConsoleLogs()">
                <i class="fas fa-trash me-1"></i>Clear Logs
            </button>
        </div>

        <!-- Quick Actions -->
        <div class="debug-card">
            <h4><i class="fas fa-tools me-2"></i>Quick Actions</h4>
            <div class="row">
                <div class="col-md-6">
                    <button class="btn btn-success me-2 mb-2" onclick="createTestMessage()">
                        <i class="fas fa-plus me-1"></i>Create Test Message
                    </button>
                    <button class="btn btn-info me-2 mb-2" onclick="showStorageInfo()">
                        <i class="fas fa-info me-1"></i>Show Storage Info
                    </button>
                </div>
                <div class="col-md-6">
                    <button class="btn btn-warning me-2 mb-2" onclick="clearStorage()">
                        <i class="fas fa-trash me-1"></i>Clear Storage
                    </button>
                    <button class="btn btn-primary me-2 mb-2" onclick="exportDebugInfo()">
                        <i class="fas fa-download me-1"></i>Export Debug Info
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Configuration
        const API_BASE_URL = '{{ API_BASE_URL }}';
        const LOCAL_BASE_URL = '{{ LOCAL_BASE_URL }}';
        
        // Console log capture
        const originalLog = console.log;
        const originalError = console.error;
        const logBuffer = [];
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            logBuffer.push(`[LOG] ${new Date().toLocaleTimeString()}: ${args.join(' ')}`);
            updateConsoleLogs();
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            logBuffer.push(`[ERROR] ${new Date().toLocaleTimeString()}: ${args.join(' ')}`);
            updateConsoleLogs();
        };
        
        function updateConsoleLogs() {
            const logsDiv = document.getElementById('console-logs');
            logsDiv.innerHTML = logBuffer.slice(-20).join('\n');
            logsDiv.scrollTop = logsDiv.scrollHeight;
        }
        
        function clearConsoleLogs() {
            logBuffer.length = 0;
            updateConsoleLogs();
        }
        
        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🔧 Debug page initialized');
            updateSystemInfo();
            checkAuth();
        });
        
        function updateSystemInfo() {
            document.getElementById('frontend-url').textContent = LOCAL_BASE_URL;
            document.getElementById('backend-url').textContent = API_BASE_URL;
            document.getElementById('current-page').textContent = window.location.href;
            document.getElementById('user-agent').textContent = navigator.userAgent.substring(0, 100) + '...';
            document.getElementById('timestamp').textContent = new Date().toLocaleString();
        }
        
        function checkAuth() {
            const accessToken = localStorage.getItem('access_token');
            const refreshToken = localStorage.getItem('refresh_token');
            const userInfo = localStorage.getItem('user_info');
            
            document.getElementById('access-token-status').innerHTML = accessToken ? 
                '<span class="status-good">✅ Present</span>' : '<span class="status-bad">❌ Missing</span>';
            
            document.getElementById('refresh-token-status').innerHTML = refreshToken ? 
                '<span class="status-good">✅ Present</span>' : '<span class="status-bad">❌ Missing</span>';
            
            document.getElementById('user-info-status').innerHTML = userInfo ? 
                '<span class="status-good">✅ Present</span>' : '<span class="status-bad">❌ Missing</span>';
            
            const isAuth = accessToken && accessToken !== 'null' && accessToken !== '';
            document.getElementById('is-authenticated').innerHTML = isAuth ? 
                '<span class="status-good">✅ Yes</span>' : '<span class="status-bad">❌ No</span>';
            
            document.getElementById('token-length').textContent = accessToken ? 
                `${accessToken.length} characters` : 'N/A';
            
            console.log('🔍 Authentication check completed', {
                hasAccessToken: !!accessToken,
                hasRefreshToken: !!refreshToken,
                hasUserInfo: !!userInfo,
                isAuthenticated: isAuth
            });
        }
        
        async function testBackendHealth() {
            addResult('Testing backend health...', 'info');
            
            try {
                const response = await fetch(`${API_BASE_URL}/api/`, { timeout: 5000 });
                if (response.ok) {
                    addResult('✅ Backend is responding', 'success');
                } else {
                    addResult(`❌ Backend returned ${response.status}`, 'error');
                }
            } catch (error) {
                addResult(`❌ Backend connection failed: ${error.message}`, 'error');
            }
        }
        
        async function testMessagesAPI() {
            addResult('Testing messages API...', 'info');
            
            const token = localStorage.getItem('access_token');
            if (!token) {
                addResult('❌ No access token found', 'error');
                return;
            }
            
            try {
                const response = await fetch(`${API_BASE_URL}/api/contact/messages/?page=1&page_size=1`, {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                const data = await response.json();
                
                if (response.ok && data.success) {
                    const total = data.data?.pagination?.total || 0;
                    addResult(`✅ Messages API working! Found ${total} messages`, 'success');
                } else {
                    addResult(`❌ Messages API failed: ${data.message || response.statusText}`, 'error');
                }
            } catch (error) {
                addResult(`❌ Messages API error: ${error.message}`, 'error');
            }
        }
        
        async function testContactAPI() {
            addResult('Testing contact submission API...', 'info');
            
            const testData = {
                name: 'Debug Test User',
                email: '<EMAIL>',
                inquiry_type: '0',
                description: 'This is a test message from the debug page.'
            };
            
            try {
                const response = await fetch(`${API_BASE_URL}/api/contact/inquiry/`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                    body: new URLSearchParams(testData)
                });
                
                if (response.ok) {
                    addResult('✅ Contact API working! Test message created', 'success');
                } else {
                    const errorText = await response.text();
                    addResult(`❌ Contact API failed: ${response.status} - ${errorText}`, 'error');
                }
            } catch (error) {
                addResult(`❌ Contact API error: ${error.message}`, 'error');
            }
        }
        
        function addResult(message, type) {
            const resultsDiv = document.getElementById('api-results');
            const alertClass = type === 'success' ? 'alert-success' : 
                             type === 'error' ? 'alert-danger' : 'alert-info';
            
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert ${alertClass} alert-dismissible fade show`;
            alertDiv.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            
            resultsDiv.appendChild(alertDiv);
            console.log(message);
        }
        
        function clearResults() {
            document.getElementById('api-results').innerHTML = '';
        }
        
        function createTestMessage() {
            window.open('/contact-us/', '_blank');
        }
        
        function showStorageInfo() {
            const info = {
                'localStorage keys': Object.keys(localStorage),
                'sessionStorage keys': Object.keys(sessionStorage),
                'localStorage size': JSON.stringify(localStorage).length,
                'sessionStorage size': JSON.stringify(sessionStorage).length
            };
            
            alert(JSON.stringify(info, null, 2));
        }
        
        function clearStorage() {
            if (confirm('Clear all localStorage and sessionStorage? This will log you out.')) {
                localStorage.clear();
                sessionStorage.clear();
                location.reload();
            }
        }
        
        function exportDebugInfo() {
            const debugInfo = {
                timestamp: new Date().toISOString(),
                urls: { frontend: LOCAL_BASE_URL, backend: API_BASE_URL },
                authentication: {
                    hasAccessToken: !!localStorage.getItem('access_token'),
                    hasRefreshToken: !!localStorage.getItem('refresh_token'),
                    hasUserInfo: !!localStorage.getItem('user_info')
                },
                browser: {
                    userAgent: navigator.userAgent,
                    url: window.location.href
                },
                logs: logBuffer.slice(-50)
            };
            
            const blob = new Blob([JSON.stringify(debugInfo, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `message-debug-${Date.now()}.json`;
            a.click();
            URL.revokeObjectURL(url);
        }
    </script>
</body>
</html>
