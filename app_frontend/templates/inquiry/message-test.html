<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Message API Test - HiSage Health</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Inter', sans-serif;
        }
        .test-container {
            max-width: 1200px;
            margin: 2rem auto;
            padding: 2rem;
        }
        .api-response {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 1rem;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            max-height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        .message-card {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="row">
            <div class="col-12">
                <h1>Message API Test</h1>
                <p>This page tests the message management API directly and shows the raw response.</p>
                
                <div class="mb-3">
                    <button class="btn btn-primary me-2" onclick="testAPI()">Test Messages API</button>
                    <button class="btn btn-secondary me-2" onclick="clearResults()">Clear Results</button>
                    <button class="btn btn-info" onclick="showAuthInfo()">Show Auth Info</button>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <h3>API Response</h3>
                        <div id="api-response" class="api-response">
                            Click "Test Messages API" to see the response...
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <h3>Rendered Messages</h3>
                        <div id="rendered-messages">
                            Messages will appear here after API test...
                        </div>
                    </div>
                </div>
                
                <div class="mt-4">
                    <h3>Debug Information</h3>
                    <div id="debug-info" class="api-response">
                        Debug information will appear here...
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Configuration
        const API_BASE_URL = '{{ API_BASE_URL }}';
        const LOCAL_BASE_URL = '{{ LOCAL_BASE_URL }}';
        
        function log(message) {
            console.log(message);
            const debugDiv = document.getElementById('debug-info');
            debugDiv.innerHTML += `[${new Date().toLocaleTimeString()}] ${message}\n`;
        }
        
        function getAuthHeaders() {
            const token = localStorage.getItem('access_token');
            return {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            };
        }
        
        function showAuthInfo() {
            const token = localStorage.getItem('access_token');
            const userInfo = localStorage.getItem('user_info');
            
            log('=== AUTH INFO ===');
            log(`API_BASE_URL: ${API_BASE_URL}`);
            log(`Token exists: ${!!token}`);
            log(`Token length: ${token ? token.length : 0}`);
            log(`User info exists: ${!!userInfo}`);
            
            if (userInfo) {
                try {
                    const user = JSON.parse(userInfo);
                    log(`User email: ${user.email}`);
                    log(`User is_staff: ${user.is_staff}`);
                } catch (e) {
                    log(`Error parsing user info: ${e.message}`);
                }
            }
        }
        
        async function testAPI() {
            log('=== TESTING API ===');
            clearResults();
            
            const apiUrl = `${API_BASE_URL}/api/contact/messages/?page=1&page_size=10`;
            log(`Calling: ${apiUrl}`);
            
            const headers = getAuthHeaders();
            log(`Headers: ${JSON.stringify(headers, null, 2)}`);
            
            try {
                const response = await fetch(apiUrl, {
                    method: 'GET',
                    headers: headers
                });
                
                log(`Response status: ${response.status}`);
                log(`Response headers: ${JSON.stringify(Object.fromEntries(response.headers.entries()), null, 2)}`);
                
                if (response.ok) {
                    const data = await response.json();
                    
                    // Show raw API response
                    document.getElementById('api-response').textContent = JSON.stringify(data, null, 2);
                    
                    log(`API Success: ${data.success}`);
                    
                    if (data.success && data.data && data.data.messages) {
                        const messages = data.data.messages;
                        log(`Messages count: ${messages.length}`);
                        
                        if (messages.length > 0) {
                            log(`First message: ${JSON.stringify(messages[0], null, 2)}`);
                            renderMessages(messages);
                        } else {
                            log('No messages in response');
                            document.getElementById('rendered-messages').innerHTML = 
                                '<div class="alert alert-info">No messages found in API response</div>';
                        }
                    } else {
                        log('Invalid response structure');
                        document.getElementById('rendered-messages').innerHTML = 
                            '<div class="alert alert-warning">Invalid API response structure</div>';
                    }
                } else {
                    const errorText = await response.text();
                    log(`API Error: ${response.status} - ${errorText}`);
                    
                    document.getElementById('api-response').textContent = 
                        `Error ${response.status}: ${errorText}`;
                    
                    document.getElementById('rendered-messages').innerHTML = 
                        `<div class="alert alert-danger">API Error: ${response.status}</div>`;
                }
                
            } catch (error) {
                log(`Network Error: ${error.message}`);
                
                document.getElementById('api-response').textContent = 
                    `Network Error: ${error.message}`;
                
                document.getElementById('rendered-messages').innerHTML = 
                    `<div class="alert alert-danger">Network Error: ${error.message}</div>`;
            }
        }
        
        function renderMessages(messages) {
            const container = document.getElementById('rendered-messages');
            
            if (!messages || messages.length === 0) {
                container.innerHTML = '<div class="alert alert-info">No messages to render</div>';
                return;
            }
            
            const messagesHtml = messages.map((message, index) => {
                const date = message.created_at ? new Date(message.created_at).toLocaleDateString() : 'N/A';
                const time = message.created_at ? new Date(message.created_at).toLocaleTimeString() : 'N/A';
                
                return `
                    <div class="message-card">
                        <h6>${message.name || 'Anonymous User'}</h6>
                        <p><strong>Email:</strong> ${message.email || 'No email'}</p>
                        <p><strong>Type:</strong> ${message.inquiry_type_display || 'General'}</p>
                        <p><strong>Status:</strong> ${message.status_display || 'New'}</p>
                        <p><strong>Date:</strong> ${date} ${time}</p>
                        <p><strong>Message:</strong> ${message.description || 'No description'}</p>
                        <small class="text-muted">ID: ${message.id}</small>
                    </div>
                `;
            }).join('');
            
            container.innerHTML = messagesHtml;
            log(`Rendered ${messages.length} messages successfully`);
        }
        
        function clearResults() {
            document.getElementById('api-response').textContent = 'Results cleared...';
            document.getElementById('rendered-messages').innerHTML = 'Messages cleared...';
        }
        
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            log('=== PAGE LOADED ===');
            showAuthInfo();
        });
    </script>
</body>
</html>
