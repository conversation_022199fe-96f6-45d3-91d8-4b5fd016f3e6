<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Message Management - HiSage Health</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Inter', sans-serif;
        }
        .admin-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
        }
        .message-card {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 1rem;
            padding: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .message-card:hover {
            box-shadow: 0 5px 20px rgba(0,0,0,0.15);
            transform: translateY(-2px);
        }
        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
        }
        .status-new { background: #ffeaa7; color: #2d3436; }
        .status-in_progress { background: #74b9ff; color: white; }
        .status-replied { background: #00b894; color: white; }
        .status-closed { background: #636e72; color: white; }
        .user-badge {
            padding: 0.25rem 0.5rem;
            border-radius: 15px;
            font-size: 0.75rem;
            font-weight: 600;
        }
        .user-registered { background: #d4edda; color: #155724; }
        .user-guest { background: #f8d7da; color: #721c24; }
        .reply-section {
            background: #f8f9fa;
            border-top: 1px solid #dee2e6;
            padding: 1rem;
            margin-top: 1rem;
        }
        .loading-spinner {
            text-align: center;
            padding: 2rem;
        }
        .empty-state {
            text-align: center;
            padding: 3rem;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <!-- Admin Header -->
    <div class="admin-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1><i class="fas fa-envelope me-3"></i>Message Management</h1>
                    <p class="mb-0">Manage user inquiries and messages</p>
                </div>
                <div class="col-md-4 text-end">
                    <button type="button" class="btn btn-outline-light me-2" onclick="loadMessages()">
                        <i class="fas fa-sync me-1"></i>Reload Messages
                    </button>
                    <a href="/dashboard/" class="btn btn-outline-light">
                        <i class="fas fa-tachometer-alt me-1"></i>Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- Status Display -->
        <div class="alert alert-info">
            <strong>Status:</strong> <span id="status-display">Initializing...</span>
        </div>

        <!-- Search and Filters -->
        <div class="card mb-3">
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <label for="search" class="form-label">Search Messages</label>
                        <input type="text" class="form-control" id="search" placeholder="Search by name, email, or message...">
                    </div>
                    <div class="col-md-3">
                        <label for="status-filter" class="form-label">Status</label>
                        <select class="form-select" id="status-filter">
                            <option value="">All Status</option>
                            <option value="new">New</option>
                            <option value="in_progress">In Progress</option>
                            <option value="replied">Replied</option>
                            <option value="closed">Closed</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <button type="button" class="btn btn-primary" onclick="loadMessages()">
                                <i class="fas fa-search me-1"></i>Search
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Loading Spinner -->
        <div class="loading-spinner" id="loading-spinner" style="display: none;">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-2">Loading messages...</p>
        </div>

        <!-- Messages Container -->
        <div id="messages-container">
            <!-- Messages will be loaded here -->
        </div>
    </div>

    <!-- Message Details Modal -->
    <div class="modal fade" id="messageDetailsModal" tabindex="-1" aria-labelledby="messageDetailsModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="messageDetailsModalLabel">
                        <i class="fas fa-envelope me-2"></i>Message Details
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body" id="messageDetailsContent">
                    <!-- Message details will be loaded here -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Immediate debug output
        console.log('🚀 MINIMAL: Script started');
        
        // Configuration
        const API_BASE_URL = '{{ API_BASE_URL }}';
        const LOCAL_BASE_URL = '{{ LOCAL_BASE_URL }}';
        
        console.log('🔧 MINIMAL: Config loaded', { API_BASE_URL, LOCAL_BASE_URL });
        
        // Update status display
        function updateStatus(message) {
            console.log('📊 MINIMAL: Status -', message);
            const statusEl = document.getElementById('status-display');
            if (statusEl) {
                statusEl.textContent = message;
            }
        }
        
        // Check authentication
        function isAuthenticated() {
            const token = localStorage.getItem('access_token');
            const isAuth = token && token !== 'null' && token !== '';
            console.log('🔐 MINIMAL: Auth check -', isAuth);
            return isAuth;
        }
        
        // Get auth headers
        function getAuthHeaders() {
            const token = localStorage.getItem('access_token');
            return {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            };
        }
        
        // Show loading
        function showLoading(show) {
            const spinner = document.getElementById('loading-spinner');
            const container = document.getElementById('messages-container');
            
            if (show) {
                spinner.style.display = 'block';
                container.style.display = 'none';
            } else {
                spinner.style.display = 'none';
                container.style.display = 'block';
            }
        }
        
        // Load messages
        async function loadMessages() {
            console.log('📥 MINIMAL: Loading messages...');
            updateStatus('Loading messages...');
            showLoading(true);

            try {
                if (!isAuthenticated()) {
                    throw new Error('Not authenticated');
                }

                // Get filter values
                const search = document.getElementById('search')?.value?.trim() || '';
                const status = document.getElementById('status-filter')?.value || '';

                // Build query parameters
                const params = new URLSearchParams({
                    page: 1,
                    page_size: 20
                });

                if (search) params.append('search', search);
                if (status) params.append('status', status);

                const apiUrl = `${API_BASE_URL}/api/contact/messages/?${params}`;
                console.log('📡 MINIMAL: Calling API -', apiUrl);
                
                const response = await fetch(apiUrl, {
                    method: 'GET',
                    headers: getAuthHeaders()
                });
                
                console.log('📡 MINIMAL: Response status -', response.status);
                
                if (response.ok) {
                    const data = await response.json();
                    console.log('📡 MINIMAL: Response data -', data);
                    
                    if (data.success && data.data && data.data.messages) {
                        const messages = data.data.messages;
                        console.log('📊 MINIMAL: Messages count -', messages.length);
                        
                        displayMessages(messages);
                        updateStatus(`Loaded ${messages.length} messages successfully`);
                    } else {
                        throw new Error(data.message || 'Invalid response structure');
                    }
                } else {
                    const errorText = await response.text();
                    throw new Error(`API Error: ${response.status} - ${errorText}`);
                }
                
            } catch (error) {
                console.error('❌ MINIMAL: Error -', error);
                updateStatus(`Error: ${error.message}`);
                
                const container = document.getElementById('messages-container');
                container.innerHTML = `
                    <div class="alert alert-danger">
                        <h5>Error Loading Messages</h5>
                        <p>${error.message}</p>
                        <button class="btn btn-primary" onclick="loadMessages()">Try Again</button>
                    </div>
                `;
            } finally {
                showLoading(false);
            }
        }
        
        // Display messages
        function displayMessages(messages) {
            console.log('🎨 MINIMAL: Displaying messages -', messages.length);
            
            const container = document.getElementById('messages-container');
            
            if (!messages || messages.length === 0) {
                container.innerHTML = `
                    <div class="empty-state">
                        <i class="fas fa-inbox fa-3x mb-3"></i>
                        <h4>No Messages Found</h4>
                        <p>There are no messages to display.</p>
                    </div>
                `;
                return;
            }
            
            const messagesHtml = messages.map(message => {
                const statusClass = `status-${message.status || 'new'}`;
                const date = message.created_at ? new Date(message.created_at).toLocaleDateString() : 'N/A';
                const time = message.created_at ? new Date(message.created_at).toLocaleTimeString() : 'N/A';

                // User registration status
                const userBadge = message.is_registered_user
                    ? '<span class="user-badge user-registered"><i class="fas fa-user-check me-1"></i>Registered User</span>'
                    : '<span class="user-badge user-guest"><i class="fas fa-user me-1"></i>Guest User</span>';

                // User info display
                const userInfo = message.user_info
                    ? `${message.user_info.first_name} ${message.user_info.last_name}`.trim() || message.name
                    : message.name || 'Anonymous User';

                // Reply count display
                const replyBadge = (message.reply_count || 0) > 0
                    ? `<span class="badge bg-info ms-2">${message.reply_count} replies</span>`
                    : '';

                return `
                    <div class="message-card" id="message-${message.id}">
                        <div class="row align-items-start">
                            <div class="col-md-7">
                                <div class="d-flex align-items-center mb-2">
                                    <h6 class="mb-0 me-2">${userInfo}</h6>
                                    ${userBadge}
                                    ${replyBadge}
                                </div>
                                <p class="text-muted mb-1">
                                    <i class="fas fa-envelope me-1"></i>${message.email || 'No email'}
                                </p>
                                <p class="mb-2">${message.description || 'No description'}</p>
                                <small class="text-muted">
                                    <i class="fas fa-clock me-1"></i>${date} ${time}
                                </small>
                            </div>
                            <div class="col-md-2">
                                <span class="badge bg-secondary">${message.inquiry_type_display || 'General'}</span>
                            </div>
                            <div class="col-md-2">
                                <span class="status-badge ${statusClass}">${message.status_display || 'New'}</span>
                            </div>
                            <div class="col-md-1 text-end">
                                <button class="btn btn-sm btn-primary mb-1" onclick="showReplyForm('${message.id}')">
                                    <i class="fas fa-reply"></i>
                                </button>
                                <button class="btn btn-sm btn-outline-secondary" onclick="viewMessageDetails('${message.id}')">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                        </div>

                        <!-- Reply Form (initially hidden) -->
                        <div class="reply-section" id="reply-form-${message.id}" style="display: none;">
                            <h6><i class="fas fa-reply me-2"></i>Reply to ${userInfo}</h6>
                            <div class="mb-3">
                                <textarea class="form-control" id="reply-text-${message.id}" rows="3" placeholder="Type your reply here..."></textarea>
                            </div>
                            <div class="d-flex justify-content-between">
                                <div>
                                    <button class="btn btn-success btn-sm" onclick="sendReply('${message.id}')">
                                        <i class="fas fa-paper-plane me-1"></i>Send Reply
                                    </button>
                                    <button class="btn btn-secondary btn-sm ms-2" onclick="hideReplyForm('${message.id}')">
                                        Cancel
                                    </button>
                                </div>
                                <div>
                                    <select class="form-select form-select-sm" id="status-update-${message.id}" style="width: auto;">
                                        <option value="">Keep Status</option>
                                        <option value="in_progress">Mark In Progress</option>
                                        <option value="replied">Mark Replied</option>
                                        <option value="closed">Mark Closed</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            }).join('');
            
            container.innerHTML = messagesHtml;
            console.log('✅ MINIMAL: Messages rendered successfully');
        }
        
        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🔧 MINIMAL: DOMContentLoaded fired');
            updateStatus('Page loaded, checking authentication...');

            // Add search box enter key support
            const searchBox = document.getElementById('search');
            if (searchBox) {
                searchBox.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        loadMessages();
                    }
                });
            }

            if (isAuthenticated()) {
                updateStatus('Authenticated, loading messages...');
                loadMessages();
            } else {
                updateStatus('Not authenticated - please log in');
                const container = document.getElementById('messages-container');
                container.innerHTML = `
                    <div class="alert alert-warning">
                        <h5>Authentication Required</h5>
                        <p>Please log in as an administrator to view messages.</p>
                        <a href="/login/" class="btn btn-primary">Login</a>
                    </div>
                `;
            }
        });
        
        // Reply functionality
        function showReplyForm(messageId) {
            console.log('💬 MINIMAL: Showing reply form for message', messageId);
            const replyForm = document.getElementById(`reply-form-${messageId}`);
            if (replyForm) {
                replyForm.style.display = 'block';
                // Focus on textarea
                const textarea = document.getElementById(`reply-text-${messageId}`);
                if (textarea) {
                    textarea.focus();
                }
            }
        }

        function hideReplyForm(messageId) {
            console.log('💬 MINIMAL: Hiding reply form for message', messageId);
            const replyForm = document.getElementById(`reply-form-${messageId}`);
            if (replyForm) {
                replyForm.style.display = 'none';
                // Clear textarea
                const textarea = document.getElementById(`reply-text-${messageId}`);
                if (textarea) {
                    textarea.value = '';
                }
                // Reset status selector
                const statusSelect = document.getElementById(`status-update-${messageId}`);
                if (statusSelect) {
                    statusSelect.value = '';
                }
            }
        }

        async function sendReply(messageId) {
            console.log('📤 MINIMAL: Sending reply for message', messageId);

            const textarea = document.getElementById(`reply-text-${messageId}`);
            const statusSelect = document.getElementById(`status-update-${messageId}`);

            if (!textarea) {
                console.error('Reply textarea not found');
                return;
            }

            const replyText = textarea.value.trim();
            if (!replyText) {
                alert('Please enter a reply message');
                return;
            }

            const newStatus = statusSelect ? statusSelect.value : '';

            try {
                updateStatus('Sending reply...');

                // Send reply
                const replyResponse = await fetch(`${API_BASE_URL}/api/contact/messages/${messageId}/reply/`, {
                    method: 'POST',
                    headers: getAuthHeaders(),
                    body: JSON.stringify({
                        message: replyText
                    })
                });

                if (!replyResponse.ok) {
                    const errorText = await replyResponse.text();
                    throw new Error(`Reply failed: ${replyResponse.status} - ${errorText}`);
                }

                console.log('✅ MINIMAL: Reply sent successfully');

                // Update status if selected
                if (newStatus) {
                    console.log('📝 MINIMAL: Updating message status to', newStatus);
                    const statusResponse = await fetch(`${API_BASE_URL}/api/contact/messages/${messageId}/`, {
                        method: 'PUT',
                        headers: getAuthHeaders(),
                        body: JSON.stringify({
                            status: newStatus
                        })
                    });

                    if (!statusResponse.ok) {
                        console.warn('Status update failed, but reply was sent');
                    } else {
                        console.log('✅ MINIMAL: Status updated successfully');
                    }
                }

                // Hide reply form
                hideReplyForm(messageId);

                // Reload messages to show updated data
                updateStatus('Reply sent successfully! Reloading messages...');
                await loadMessages();

            } catch (error) {
                console.error('❌ MINIMAL: Reply error -', error);
                updateStatus(`Reply failed: ${error.message}`);
                alert(`Failed to send reply: ${error.message}`);
            }
        }

        async function viewMessageDetails(messageId) {
            console.log('👁️ MINIMAL: Loading details for message', messageId);

            try {
                updateStatus('Loading message details...');

                const response = await fetch(`${API_BASE_URL}/api/contact/messages/${messageId}/`, {
                    method: 'GET',
                    headers: getAuthHeaders()
                });

                if (!response.ok) {
                    const errorText = await response.text();
                    throw new Error(`Failed to load message details: ${response.status} - ${errorText}`);
                }

                const data = await response.json();
                console.log('📋 MINIMAL: Message details loaded', data);

                if (data.success && data.data) {
                    displayMessageDetails(data.data);

                    // Show modal
                    const modal = new bootstrap.Modal(document.getElementById('messageDetailsModal'));
                    modal.show();

                    updateStatus('Message details loaded');
                } else {
                    throw new Error(data.message || 'Invalid response structure');
                }

            } catch (error) {
                console.error('❌ MINIMAL: Error loading message details -', error);
                updateStatus(`Error: ${error.message}`);
                alert(`Failed to load message details: ${error.message}`);
            }
        }

        function displayMessageDetails(message) {
            const content = document.getElementById('messageDetailsContent');
            if (!content) return;

            const userBadge = message.is_registered_user
                ? '<span class="user-badge user-registered"><i class="fas fa-user-check me-1"></i>Registered User</span>'
                : '<span class="user-badge user-guest"><i class="fas fa-user me-1"></i>Guest User</span>';

            const userInfo = message.user_info
                ? `${message.user_info.first_name} ${message.user_info.last_name}`.trim() || message.name
                : message.name || 'Anonymous User';

            const date = message.created_at ? new Date(message.created_at).toLocaleString() : 'N/A';
            const statusClass = `status-${message.status || 'new'}`;

            // Build replies HTML
            let repliesHtml = '';
            if (message.replies && message.replies.length > 0) {
                repliesHtml = `
                    <h6 class="mt-4 mb-3"><i class="fas fa-comments me-2"></i>Replies (${message.replies.length})</h6>
                    <div class="replies-container">
                        ${message.replies.map(reply => {
                            const replyDate = reply.created_at ? new Date(reply.created_at).toLocaleString() : 'N/A';
                            return `
                                <div class="reply-item mb-3 p-3" style="background: #f8f9fa; border-left: 4px solid #007bff; border-radius: 5px;">
                                    <div class="d-flex justify-content-between align-items-start mb-2">
                                        <strong><i class="fas fa-user-tie me-1"></i>${reply.admin_name || 'Admin'}</strong>
                                        <small class="text-muted">${replyDate}</small>
                                    </div>
                                    <p class="mb-0">${reply.message}</p>
                                </div>
                            `;
                        }).join('')}
                    </div>
                `;
            } else {
                repliesHtml = '<p class="text-muted mt-4"><i class="fas fa-info-circle me-2"></i>No replies yet</p>';
            }

            content.innerHTML = `
                <div class="message-details">
                    <div class="row mb-3">
                        <div class="col-md-8">
                            <h5 class="mb-2">
                                ${userInfo}
                                ${userBadge}
                            </h5>
                            <p class="text-muted mb-1">
                                <i class="fas fa-envelope me-1"></i>${message.email || 'No email'}
                            </p>
                            <p class="text-muted mb-0">
                                <i class="fas fa-clock me-1"></i>${date}
                            </p>
                        </div>
                        <div class="col-md-4 text-end">
                            <span class="badge bg-secondary mb-2">${message.inquiry_type_display || 'General'}</span><br>
                            <span class="status-badge ${statusClass}">${message.status_display || 'New'}</span>
                        </div>
                    </div>

                    ${message.user_info ? `
                        <div class="alert alert-info">
                            <h6><i class="fas fa-user-check me-2"></i>Registered User Information</h6>
                            <p class="mb-1"><strong>Full Name:</strong> ${message.user_info.first_name} ${message.user_info.last_name}</p>
                            <p class="mb-1"><strong>Member Since:</strong> ${message.user_info.date_joined ? new Date(message.user_info.date_joined).toLocaleDateString() : 'N/A'}</p>
                            <p class="mb-0"><strong>Account Status:</strong> ${message.user_info.is_active ? 'Active' : 'Inactive'}</p>
                        </div>
                    ` : ''}

                    <div class="message-content mb-4">
                        <h6><i class="fas fa-comment me-2"></i>Message</h6>
                        <div class="p-3" style="background: #f8f9fa; border-radius: 5px;">
                            ${message.description || 'No message content'}
                        </div>
                    </div>

                    ${repliesHtml}
                </div>
            `;
        }

        console.log('🔧 MINIMAL: Script fully loaded with reply functionality');
    </script>
</body>
</html>
