<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Message Management - HiSage Health Admin</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Inter', sans-serif;
        }
        .admin-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
        }
        .message-card {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 1rem;
            transition: all 0.3s ease;
        }
        .message-card:hover {
            box-shadow: 0 5px 20px rgba(0,0,0,0.15);
            transform: translateY(-2px);
        }
        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
        }
        .status-new { background: #ffeaa7; color: #2d3436; }
        .status-in_progress { background: #74b9ff; color: white; }
        .status-replied { background: #00b894; color: white; }
        .status-closed { background: #636e72; color: white; }
        .type-badge {
            padding: 0.25rem 0.5rem;
            border-radius: 15px;
            font-size: 0.75rem;
            background: #e9ecef;
            color: #495057;
        }
        .search-filters {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 1.5rem;
            margin-bottom: 2rem;
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 8px;
        }
        .btn-outline-primary {
            border-color: #667eea;
            color: #667eea;
            border-radius: 8px;
        }
        .btn-outline-primary:hover {
            background: #667eea;
            border-color: #667eea;
        }
        .loading-spinner {
            display: none;
            text-align: center;
            padding: 2rem;
        }
        .empty-state {
            text-align: center;
            padding: 3rem;
            color: #6c757d;
        }
        .message-preview {
            max-height: 60px;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
        }
        .reply-count {
            background: #667eea;
            color: white;
            border-radius: 50%;
            padding: 0.2rem 0.5rem;
            font-size: 0.7rem;
            min-width: 20px;
            text-align: center;
        }
    </style>
</head>
<body>
    <!-- Admin Header -->
    <div class="admin-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h1><i class="fas fa-envelope me-3"></i>Message Management</h1>
                    <p class="mb-0">Manage contact inquiries from users</p>
                </div>
                <div class="col-md-6 text-end">
                    <button type="button" class="btn btn-outline-light me-2" onclick="toggleDebugPanel()">
                        <i class="fas fa-bug me-1"></i>Debug
                    </button>
                    <a href="/dashboard/" class="btn btn-outline-light me-2">
                        <i class="fas fa-tachometer-alt me-1"></i>Dashboard
                    </a>
                    <a href="/" class="btn btn-outline-light">
                        <i class="fas fa-home me-1"></i>Home
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- Debug Panel (only show if debug=true in URL) -->
        <div id="debug-panel" class="search-filters" style="display: none; background: #fff3cd; border: 1px solid #ffeaa7;">
            <h6><i class="fas fa-bug me-2"></i>Debug Information</h6>
            <div class="row">
                <div class="col-md-6">
                    <small>
                        <strong>API Base URL:</strong> <span id="debug-api-url">{{ API_BASE_URL }}</span><br>
                        <strong>Auth Token:</strong> <span id="debug-auth-token">Checking...</span><br>
                        <strong>User Authenticated:</strong> <span id="debug-user-auth">Checking...</span>
                    </small>
                </div>
                <div class="col-md-6">
                    <button type="button" class="btn btn-sm btn-warning" onclick="testApiConnection()">
                        <i class="fas fa-plug me-1"></i>Test API Connection
                    </button>
                    <button type="button" class="btn btn-sm btn-info" onclick="showDebugInfo()">
                        <i class="fas fa-info me-1"></i>Show Debug Info
                    </button>
                </div>
            </div>
        </div>

        <!-- Search and Filters -->
        <div class="search-filters">
            <div class="row">
                <div class="col-md-4">
                    <label for="search" class="form-label">Search Messages</label>
                    <input type="text" class="form-control" id="search" placeholder="Search by name, email, or message...">
                </div>
                <div class="col-md-3">
                    <label for="status-filter" class="form-label">Status</label>
                    <select class="form-select" id="status-filter">
                        {% for status in status_choices %}
                        <option value="{{ status.value }}">{{ status.label }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="type-filter" class="form-label">Type</label>
                    <select class="form-select" id="type-filter">
                        {% for type in inquiry_choices %}
                        <option value="{{ type.value }}">{{ type.label }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-grid">
                        <button type="button" class="btn btn-primary" onclick="loadMessages()">
                            <i class="fas fa-search me-1"></i>Search
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics -->
        <div class="row mb-4" id="stats-row" style="display: none;">
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h5 class="card-title text-primary" id="total-messages">0</h5>
                        <p class="card-text">Total Messages</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h5 class="card-title text-warning" id="new-messages">0</h5>
                        <p class="card-text">New Messages</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h5 class="card-title text-info" id="in-progress-messages">0</h5>
                        <p class="card-text">In Progress</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h5 class="card-title text-success" id="replied-messages">0</h5>
                        <p class="card-text">Replied</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Loading Spinner -->
        <div class="loading-spinner" id="loading-spinner">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-2">Loading messages...</p>
        </div>

        <!-- Messages List -->
        <div id="messages-container">
            <!-- Messages will be loaded here -->
        </div>

        <!-- Pagination -->
        <nav aria-label="Messages pagination" id="pagination-nav" style="display: none;">
            <ul class="pagination justify-content-center" id="pagination-container">
                <!-- Pagination will be loaded here -->
            </ul>
        </nav>
    </div>

    <!-- Message Detail Modal -->
    <div class="modal fade" id="messageModal" tabindex="-1" aria-labelledby="messageModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="messageModalLabel">Message Details</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body" id="messageModalBody">
                    <!-- Message details will be loaded here -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="button" class="btn btn-primary" id="replyButton" onclick="showReplyForm()">
                        <i class="fas fa-reply me-1"></i>Reply
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Reply Modal -->
    <div class="modal fade" id="replyModal" tabindex="-1" aria-labelledby="replyModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="replyModalLabel">Send Reply</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="replyForm">
                        <div class="mb-3">
                            <label for="replyMessage" class="form-label">Reply Message</label>
                            <textarea class="form-control" id="replyMessage" rows="5" placeholder="Type your reply here..." required></textarea>
                            <div class="form-text">Maximum 2000 characters</div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" id="sendReplyButton" onclick="sendReply()">
                        <i class="fas fa-paper-plane me-1"></i>Send Reply
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Immediate debug output
        console.log('🚀 Script tag started executing');

        // Configuration
        const API_BASE_URL = '{{ API_BASE_URL }}';
        const LOCAL_BASE_URL = '{{ LOCAL_BASE_URL }}';

        console.log('🔧 Configuration loaded:', { API_BASE_URL, LOCAL_BASE_URL });

        // Global variables
        let currentPage = 1;
        let currentMessageId = null;
        let messagesData = [];

        console.log('🔧 Global variables initialized');

        // Test basic JavaScript execution
        console.log('🚀 JavaScript file loaded successfully');
        console.log('🔍 Current URL:', window.location.href);
        console.log('🔍 API_BASE_URL:', API_BASE_URL);
        console.log('🔍 LOCAL_BASE_URL:', LOCAL_BASE_URL);

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🔧 DOMContentLoaded event fired');
            console.log('🔧 Initializing message management page...');

            // Show debug panel if debug=true in URL
            const urlParams = new URLSearchParams(window.location.search);
            const debugMode = urlParams.get('debug') === 'true';
            console.log('🔍 Debug mode check:', debugMode);
            console.log('🔍 URL search params:', window.location.search);

            if (debugMode) {
                console.log('🐛 Enabling debug panel...');
                const debugPanel = document.getElementById('debug-panel');
                if (debugPanel) {
                    debugPanel.style.display = 'block';
                    updateDebugInfo();
                    console.log('✅ Debug panel enabled');
                } else {
                    console.error('❌ Debug panel element not found');
                }
            } else {
                console.log('ℹ️ Debug mode not enabled. Add ?debug=true to URL to enable.');
            }

            // Check authentication
            if (!isAuthenticated()) {
                showError('Authentication required. Please log in as an administrator.');
                console.log('❌ Authentication failed - no valid token found');
                console.log('🔍 Available tokens:', {
                    access_token: localStorage.getItem('access_token') ? 'Present' : 'Missing',
                    refresh_token: localStorage.getItem('refresh_token') ? 'Present' : 'Missing'
                });
                return;
            }

            console.log('✅ Authentication successful, loading messages...');

            // Load initial messages
            loadMessages();
        });

        // Toggle debug panel visibility
        function toggleDebugPanel() {
            const debugPanel = document.getElementById('debug-panel');
            if (debugPanel.style.display === 'none' || debugPanel.style.display === '') {
                debugPanel.style.display = 'block';
                updateDebugInfo();
                console.log('🐛 Debug panel enabled');
            } else {
                debugPanel.style.display = 'none';
                console.log('🐛 Debug panel disabled');
            }
        }

        // Update debug information
        function updateDebugInfo() {
            document.getElementById('debug-api-url').textContent = API_BASE_URL;

            const token = localStorage.getItem('access_token');
            document.getElementById('debug-auth-token').textContent = token ?
                `${token.substring(0, 20)}...` : 'No token found';

            document.getElementById('debug-user-auth').textContent = isAuthenticated() ?
                'Yes' : 'No';
        }

        // Test API connection
        async function testApiConnection() {
            console.log('🧪 Testing API connection...');

            try {
                const response = await fetch(`${API_BASE_URL}/api/contact/messages/?page=1&page_size=1`, {
                    method: 'GET',
                    headers: getAuthHeaders()
                });

                console.log('🔍 Test API response status:', response.status);

                if (response.ok) {
                    const data = await response.json();
                    showSuccess(`API connection successful! Found ${data.data?.pagination?.total || 0} messages.`);
                } else {
                    const errorText = await response.text();
                    showError(`API connection failed: ${response.status} - ${errorText}`);
                }

            } catch (error) {
                console.error('❌ API test error:', error);
                showError(`API connection error: ${error.message}`);
            }
        }

        // Show debug information
        function showDebugInfo() {
            const debugInfo = {
                'API Base URL': API_BASE_URL,
                'Local Base URL': LOCAL_BASE_URL,
                'Access Token': localStorage.getItem('access_token') ? 'Present' : 'Missing',
                'Refresh Token': localStorage.getItem('refresh_token') ? 'Present' : 'Missing',
                'User Info': localStorage.getItem('user_info') ? 'Present' : 'Missing',
                'Is Authenticated': isAuthenticated(),
                'Current Page': window.location.href,
                'User Agent': navigator.userAgent
            };

            console.log('🔍 Debug Information:', debugInfo);

            let debugText = 'Debug Information:\n\n';
            for (const [key, value] of Object.entries(debugInfo)) {
                debugText += `${key}: ${value}\n`;
            }

            alert(debugText);
        }

        // Check if user is authenticated
        function isAuthenticated() {
            const token = localStorage.getItem('access_token');
            return token && token !== 'null' && token !== '';
        }

        // Get authentication headers
        function getAuthHeaders() {
            const token = localStorage.getItem('access_token');
            return {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            };
        }

        // Load messages from API
        async function loadMessages(page = 1) {
            console.log('📥 Loading messages, page:', page);
            console.log('🔍 API_BASE_URL:', API_BASE_URL);
            console.log('🔍 Auth token exists:', !!localStorage.getItem('access_token'));

            showLoading(true);

            try {
                // Get filter values
                const search = document.getElementById('search').value.trim();
                const status = document.getElementById('status-filter').value;
                const type = document.getElementById('type-filter').value;

                // Build query parameters
                const params = new URLSearchParams({
                    page: page,
                    page_size: 20
                });

                if (search) params.append('search', search);
                if (status) params.append('status', status);
                if (type) params.append('type', type);

                const apiUrl = `${API_BASE_URL}/api/contact/messages/?${params}`;
                console.log('🔍 Calling API:', apiUrl);

                const headers = getAuthHeaders();
                console.log('🔍 Request headers:', headers);

                const response = await fetch(apiUrl, {
                    method: 'GET',
                    headers: headers
                });

                console.log('🔍 Response status:', response.status);
                console.log('🔍 Response headers:', Object.fromEntries(response.headers.entries()));

                if (response.ok) {
                    const data = await response.json();
                    console.log('✅ Messages loaded:', data);

                    if (data.success) {
                        messagesData = data.data.messages;
                        console.log('📊 Messages data:', messagesData);
                        console.log('📊 Messages count:', messagesData ? messagesData.length : 'undefined');
                        console.log('📊 Pagination data:', data.data.pagination);

                        // Add detailed logging for each message
                        if (messagesData && messagesData.length > 0) {
                            console.log('📊 First message sample:', messagesData[0]);
                        }

                        displayMessages(data.data.messages);
                        displayPagination(data.data.pagination);
                        updateStats(data.data.messages);
                        currentPage = page;
                    } else {
                        console.error('❌ API returned success=false:', data);
                        showError(data.message || 'Failed to load messages');
                    }
                } else {
                    const errorText = await response.text();
                    console.error('❌ API error response:', errorText);

                    if (response.status === 403) {
                        showError('Access denied. Administrator privileges required.');
                    } else if (response.status === 401) {
                        showError('Authentication expired. Please log in again.');
                        // Redirect to login
                        setTimeout(() => {
                            window.location.href = '/login/';
                        }, 2000);
                    } else {
                        showError(`Failed to load messages. Server returned ${response.status}: ${errorText}`);
                    }
                }

            } catch (error) {
                console.error('❌ Error loading messages:', error);
                showError('Network error. Please check your connection and try again.');
            } finally {
                showLoading(false);
            }
        }

        // Display messages in the UI
        function displayMessages(messages) {
            console.log('🎨 displayMessages called with:', messages);
            const container = document.getElementById('messages-container');

            if (!container) {
                console.error('❌ messages-container element not found!');
                return;
            }

            if (!messages || messages.length === 0) {
                console.log('📭 No messages to display');
                container.innerHTML = `
                    <div class="empty-state">
                        <i class="fas fa-inbox fa-3x mb-3"></i>
                        <h4>No Messages Found</h4>
                        <p>There are no messages matching your search criteria.</p>
                        <div class="mt-3">
                            <small class="text-muted">Debug: messages = ${JSON.stringify(messages)}</small>
                        </div>
                    </div>
                `;
                return;
            }

            console.log(`🎨 Rendering ${messages.length} messages`);

            try {
                const messagesHtml = messages.map((message, index) => {
                    console.log(`🎨 Rendering message ${index + 1}:`, message);

                    const statusClass = `status-${message.status || 'new'}`;
                    const date = message.created_at ? new Date(message.created_at).toLocaleDateString() : 'N/A';
                    const time = message.created_at ? new Date(message.created_at).toLocaleTimeString() : 'N/A';

                    return `
                        <div class="message-card" onclick="viewMessage('${message.id}')">
                            <div class="card-body">
                                <div class="row align-items-center">
                                    <div class="col-md-6">
                                        <h6 class="card-title mb-1">
                                            ${message.name || 'Anonymous User'}
                                            ${(message.reply_count || 0) > 0 ? `<span class="reply-count ms-2">${message.reply_count}</span>` : ''}
                                        </h6>
                                        <p class="text-muted mb-1">
                                            <i class="fas fa-envelope me-1"></i>${message.email || 'No email'}
                                        </p>
                                        <div class="message-preview">
                                            ${message.description || 'No description'}
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <span class="type-badge">${message.inquiry_type_display || 'General'}</span>
                                    </div>
                                    <div class="col-md-2">
                                        <span class="status-badge ${statusClass}">${message.status_display || 'New'}</span>
                                    </div>
                                    <div class="col-md-1 text-end">
                                        <small class="text-muted">
                                            ${date}<br>${time}
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;
                }).join('');

                console.log('🎨 Generated HTML length:', messagesHtml.length);
                container.innerHTML = messagesHtml;
                console.log('✅ Messages rendered successfully');

            } catch (error) {
                console.error('❌ Error rendering messages:', error);
                container.innerHTML = `
                    <div class="alert alert-danger">
                        <h5>Error Rendering Messages</h5>
                        <p>There was an error displaying the messages: ${error.message}</p>
                        <details>
                            <summary>Debug Information</summary>
                            <pre>${JSON.stringify(messages, null, 2)}</pre>
                        </details>
                    </div>
                `;
            }
        }

        // Display pagination
        function displayPagination(pagination) {
            const nav = document.getElementById('pagination-nav');
            const container = document.getElementById('pagination-container');

            if (pagination.total_pages <= 1) {
                nav.style.display = 'none';
                return;
            }

            nav.style.display = 'block';

            let paginationHtml = '';

            // Previous button
            if (pagination.page > 1) {
                paginationHtml += `
                    <li class="page-item">
                        <a class="page-link" href="#" onclick="loadMessages(${pagination.page - 1})">Previous</a>
                    </li>
                `;
            }

            // Page numbers
            const startPage = Math.max(1, pagination.page - 2);
            const endPage = Math.min(pagination.total_pages, pagination.page + 2);

            for (let i = startPage; i <= endPage; i++) {
                const activeClass = i === pagination.page ? 'active' : '';
                paginationHtml += `
                    <li class="page-item ${activeClass}">
                        <a class="page-link" href="#" onclick="loadMessages(${i})">${i}</a>
                    </li>
                `;
            }

            // Next button
            if (pagination.page < pagination.total_pages) {
                paginationHtml += `
                    <li class="page-item">
                        <a class="page-link" href="#" onclick="loadMessages(${pagination.page + 1})">Next</a>
                    </li>
                `;
            }

            container.innerHTML = paginationHtml;
        }

        // Update statistics
        function updateStats(messages) {
            const statsRow = document.getElementById('stats-row');
            statsRow.style.display = 'flex';

            const statusCounts = {
                total: messages.length,
                new: 0,
                in_progress: 0,
                replied: 0
            };

            messages.forEach(message => {
                if (message.status === 'new') statusCounts.new++;
                else if (message.status === 'in_progress') statusCounts.in_progress++;
                else if (message.status === 'replied') statusCounts.replied++;
            });

            document.getElementById('total-messages').textContent = statusCounts.total;
            document.getElementById('new-messages').textContent = statusCounts.new;
            document.getElementById('in-progress-messages').textContent = statusCounts.in_progress;
            document.getElementById('replied-messages').textContent = statusCounts.replied;
        }

        // View message details
        async function viewMessage(messageId) {
            console.log('👁️ Viewing message:', messageId);
            currentMessageId = messageId;

            try {
                const response = await fetch(`${API_BASE_URL}/api/contact/messages/${messageId}/`, {
                    method: 'GET',
                    headers: getAuthHeaders()
                });

                if (response.ok) {
                    const data = await response.json();
                    console.log('✅ Message details loaded:', data);

                    if (data.success) {
                        displayMessageDetails(data.data);
                        const modal = new bootstrap.Modal(document.getElementById('messageModal'));
                        modal.show();
                    } else {
                        showError(data.message || 'Failed to load message details');
                    }
                } else {
                    showError('Failed to load message details');
                }

            } catch (error) {
                console.error('❌ Error loading message details:', error);
                showError('Network error. Please try again.');
            }
        }

        // Display message details in modal
        function displayMessageDetails(message) {
            const modalBody = document.getElementById('messageModalBody');
            const statusClass = `status-${message.status}`;
            const date = new Date(message.created_at).toLocaleDateString();
            const time = new Date(message.created_at).toLocaleTimeString();

            let repliesHtml = '';
            if (message.replies && message.replies.length > 0) {
                repliesHtml = `
                    <div class="mt-4">
                        <h6><i class="fas fa-reply me-2"></i>Replies (${message.replies.length})</h6>
                        <div class="replies-container">
                            ${message.replies.map(reply => {
                                const replyDate = new Date(reply.created_at).toLocaleDateString();
                                const replyTime = new Date(reply.created_at).toLocaleTimeString();
                                return `
                                    <div class="reply-item p-3 mb-2" style="background: #f8f9fa; border-left: 4px solid #667eea; border-radius: 5px;">
                                        <div class="d-flex justify-content-between align-items-start mb-2">
                                            <strong>${reply.admin_name || reply.admin_email}</strong>
                                            <small class="text-muted">${replyDate} ${replyTime}</small>
                                        </div>
                                        <p class="mb-0">${reply.message}</p>
                                    </div>
                                `;
                            }).join('')}
                        </div>
                    </div>
                `;
            }

            modalBody.innerHTML = `
                <div class="message-details">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <strong>Name:</strong> ${message.name || 'Anonymous User'}
                        </div>
                        <div class="col-md-6">
                            <strong>Email:</strong> ${message.email}
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <strong>Type:</strong> <span class="type-badge">${message.inquiry_type_display}</span>
                        </div>
                        <div class="col-md-6">
                            <strong>Status:</strong> <span class="status-badge ${statusClass}">${message.status_display}</span>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <strong>Created:</strong> ${date} ${time}
                        </div>
                        <div class="col-md-6">
                            <strong>Updated:</strong> ${new Date(message.updated_at).toLocaleDateString()} ${new Date(message.updated_at).toLocaleTimeString()}
                        </div>
                    </div>
                    <div class="mb-3">
                        <strong>Message:</strong>
                        <div class="p-3 mt-2" style="background: #f8f9fa; border-radius: 5px;">
                            ${message.description}
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="statusSelect" class="form-label"><strong>Update Status:</strong></label>
                        <select class="form-select" id="statusSelect" onchange="updateMessageStatus('${message.id}', this.value)">
                            <option value="new" ${message.status === 'new' ? 'selected' : ''}>New</option>
                            <option value="in_progress" ${message.status === 'in_progress' ? 'selected' : ''}>In Progress</option>
                            <option value="replied" ${message.status === 'replied' ? 'selected' : ''}>Replied</option>
                            <option value="closed" ${message.status === 'closed' ? 'selected' : ''}>Closed</option>
                        </select>
                    </div>
                    ${repliesHtml}
                </div>
            `;
        }

        // Update message status
        async function updateMessageStatus(messageId, newStatus) {
            console.log('🔄 Updating message status:', messageId, newStatus);

            try {
                const response = await fetch(`${API_BASE_URL}/api/contact/messages/${messageId}/`, {
                    method: 'PUT',
                    headers: getAuthHeaders(),
                    body: JSON.stringify({ status: newStatus })
                });

                if (response.ok) {
                    const data = await response.json();
                    console.log('✅ Status updated:', data);

                    if (data.success) {
                        showSuccess('Message status updated successfully');
                        // Refresh the message list
                        loadMessages(currentPage);
                    } else {
                        showError(data.message || 'Failed to update status');
                    }
                } else {
                    showError('Failed to update message status');
                }

            } catch (error) {
                console.error('❌ Error updating status:', error);
                showError('Network error. Please try again.');
            }
        }

        // Show reply form
        function showReplyForm() {
            const modal = new bootstrap.Modal(document.getElementById('replyModal'));
            modal.show();
        }

        // Send reply
        async function sendReply() {
            const replyMessage = document.getElementById('replyMessage').value.trim();

            if (!replyMessage) {
                showError('Please enter a reply message');
                return;
            }

            if (replyMessage.length > 2000) {
                showError('Reply message is too long (maximum 2000 characters)');
                return;
            }

            console.log('📤 Sending reply to message:', currentMessageId);

            const sendButton = document.getElementById('sendReplyButton');
            sendButton.disabled = true;
            sendButton.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Sending...';

            try {
                const response = await fetch(`${API_BASE_URL}/api/contact/messages/${currentMessageId}/reply/`, {
                    method: 'POST',
                    headers: getAuthHeaders(),
                    body: JSON.stringify({ message: replyMessage })
                });

                if (response.ok) {
                    const data = await response.json();
                    console.log('✅ Reply sent:', data);

                    if (data.success) {
                        showSuccess('Reply sent successfully');

                        // Close reply modal
                        const replyModal = bootstrap.Modal.getInstance(document.getElementById('replyModal'));
                        replyModal.hide();

                        // Clear reply form
                        document.getElementById('replyMessage').value = '';

                        // Refresh message details
                        viewMessage(currentMessageId);

                        // Refresh message list
                        loadMessages(currentPage);
                    } else {
                        showError(data.message || 'Failed to send reply');
                    }
                } else {
                    showError('Failed to send reply');
                }

            } catch (error) {
                console.error('❌ Error sending reply:', error);
                showError('Network error. Please try again.');
            } finally {
                sendButton.disabled = false;
                sendButton.innerHTML = '<i class="fas fa-paper-plane me-1"></i>Send Reply';
            }
        }

        // Utility functions
        function showLoading(show) {
            const spinner = document.getElementById('loading-spinner');
            const container = document.getElementById('messages-container');

            if (show) {
                spinner.style.display = 'block';
                container.style.display = 'none';
            } else {
                spinner.style.display = 'none';
                container.style.display = 'block';
            }
        }

        function showError(message) {
            console.error('❌ Error:', message);

            // Create toast notification
            const toastHtml = `
                <div class="toast align-items-center text-white bg-danger border-0" role="alert" aria-live="assertive" aria-atomic="true">
                    <div class="d-flex">
                        <div class="toast-body">
                            <i class="fas fa-exclamation-circle me-2"></i>${message}
                        </div>
                        <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
                    </div>
                </div>
            `;

            showToast(toastHtml);
        }

        function showSuccess(message) {
            console.log('✅ Success:', message);

            // Create toast notification
            const toastHtml = `
                <div class="toast align-items-center text-white bg-success border-0" role="alert" aria-live="assertive" aria-atomic="true">
                    <div class="d-flex">
                        <div class="toast-body">
                            <i class="fas fa-check-circle me-2"></i>${message}
                        </div>
                        <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
                    </div>
                </div>
            `;

            showToast(toastHtml);
        }

        function showToast(toastHtml) {
            // Create toast container if it doesn't exist
            let toastContainer = document.getElementById('toast-container');
            if (!toastContainer) {
                toastContainer = document.createElement('div');
                toastContainer.id = 'toast-container';
                toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';
                toastContainer.style.zIndex = '9999';
                document.body.appendChild(toastContainer);
            }

            // Add toast to container
            toastContainer.insertAdjacentHTML('beforeend', toastHtml);

            // Show toast
            const toastElement = toastContainer.lastElementChild;
            const toast = new bootstrap.Toast(toastElement, { delay: 5000 });
            toast.show();

            // Remove toast element after it's hidden
            toastElement.addEventListener('hidden.bs.toast', () => {
                toastElement.remove();
            });
        }

        // Event listeners
        document.getElementById('search').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                loadMessages(1);
            }
        });

        // Auto-refresh every 30 seconds
        setInterval(() => {
            if (document.visibilityState === 'visible') {
                loadMessages(currentPage);
            }
        }, 30000);
