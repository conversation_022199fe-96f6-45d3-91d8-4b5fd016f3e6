@import url('https://fonts.googleapis.com/css2?family=Khula:wght@300;400;600;700;800&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&display=swap');

:root{

    --btn-color: #fdfdfd;/* button color*/
    --btn-bg: #101010;/* button bg color*/

    --primary-text-color: #1d1b1b;
}


html {
    scroll-behavior: smooth;
    font-family: "Roboto", sans-serif;
}

header{
    background-color: #fff;
    color: #000;
}


header > .collapsable-header{
    display: flex;
    gap: 1rem;
    width: 100%;
    background-color: inherit;
    place-content: center;
    overflow: hidden;
    transition: width 0.3s ease;
}

.animated-collapse{
    transition: width 0.3s ease;
}


.header-links {
    display: flex;
    align-items: center;
    min-width: fit-content;
    border-radius: 25px;
    padding: 5px 10px;
    transition: background-color 0.5s, color 0.5s;
}


.header-links:hover {
    color: #fff;
    background-color: #000;
}


.black-text{
    color: #000;
}

.white-text{
    color: #fff;
}

.primary-text-color{
    color: var(--primary-text-color);
}

.opacity-0{
    opacity: 0 !important;
}

.opacity-100{
    opacity: 100 !important;
}

.btn{
    padding: 10px 15px;
    width: max-content;
    border-radius: 10px;
    color: var(--btn-color);
    background-color: var(--btn-bg);
    justify-content: center;
    align-items: center;
    display: flex;
    cursor: pointer;
}

.btn:hover{

}

.btn:disabled{
    cursor: default;
}

.input{
    padding: 10px;
    background-color: transparent;
    /* border-radius: 10px; */
    outline: none;
    min-width: 100px;
    border-bottom: 2px solid #090909;
    transition: border 0.3s;
}

.input:active, .input:focus, .input:focus-within{
    border-bottom: 2px solid #BE3345 !important;
}


.input-error{
    border-bottom: 3px solid #ff1e1e;
}

.input-error:focus-within{
    border-bottom: 3px solid #fd0101;
}

.error-container{
    /* container used to display errors */
    border: 3px solid #F5C6CB;
    background-color: #F8D7DA;
    color: #932504;
    width: 100%;
    max-width: 450px;
    border-radius: 5px;
    min-height: 50px;
    padding: 5px 10px;
}

.error-container ul{
    list-style: decimal;
}

.error-container li{
    margin-left: 10px;
}

.error-container li > ul  {
    list-style: circle;
}


/* Slideshow container */
.slideshow-container {
    width: 100%;
    /* height: 100%; */
    position: relative;
    margin: auto;
    overflow: hidden;
    display: block;
    height: 100%;
}


/* Next & previous buttons for slideshow */
.prev,
.next {
    cursor: pointer;
    position: absolute;
    top: 50%;
    width: auto;
    padding: 16px;
    margin-top: -22px;
    color: white;
    font-weight: bold;
    font-size: 18px;
    transition: 0.6s ease;
    border-radius: 0 3px 3px 0;
    user-select: none;
    z-index: 10;
}

/* Position the "next button" to the right */
.next {
    right: 0;
    border-radius: 3px 0 0 3px;
}

/* On hover, add a black background color with a little bit see-through */
.prev:hover,
.next:hover {
    background-color: rgba(0, 0, 0, 0.8);
}

/* Navigation dots styling */
.dots-container {
    text-align: center;
    margin-top: 20px;
}

.dot {
    height: 10px;
    width: 10px;
    background-color: #bbb;
    border-radius: 50%;
    display: inline-block;
    margin: 0 5px;
    cursor: pointer;
}

.dots-container .active,
.dot:hover {
    background-color: #717171;
}

.footer-link{
    color: #0d0d0d;
    transition: color 0.3s;
}

.footer-link:hover{
    color: #483cf4;
}


.review-container {
    position: relative;
    max-width: 600px;
    margin: auto;
}

.review-slide {
    display: none;
    padding: 20px;
    text-align: center;
}

/* Fading animation */

.fade {
    animation: fadeEffect 1s ease-in-out;
}

@keyframes fadeEffect {
    from { opacity: 0; }
    to { opacity: 1; }
}


.stars {
    display: inline-block;
    font-size: 40px;
    cursor: pointer;
}
.stars .star {
    color: #ccc;
    transition: color 0.2s;
}
.stars .star:hover,
.stars .star.active {
    color: gold;
}

/* --------- collapsible div ---------- */
.collapsible {
    background-color: #f3f0f0;
    color: #2b2929;
    /* cursor: pointer; */
    padding: 5px;
    width: 100%;
    border: none;
    text-align: left;
    outline: none;
    font-size: 16px;
    transition: 0.4s;
}

/* Style for the collapsible content */
.content {
    padding: 0 18px;
    /* display: none; */
    height: 0px;
    overflow: hidden;
    background-color: transparent;
    transition: height 0.5s;
    text-align: justify;
    margin-top: 10px;
}

.collapsible .active,
.collapsible:hover {
    /* background-color: #dedddd; */
}


/* --------------------- pagination -------------------- */

.pagination{
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
    margin: 5% 5px;
}

.pagination .page-item{
    display: flex;
    font-size: larger;
    padding: 5px 15px;
    border: 1px solid #1e85ec;
    /* background-color: #1e85ec; */
    color: #000;
    cursor: pointer;
    border-radius: 5px;
    transition: background-color 0.3s, color 0.3s;
}

.pagination .page-item:hover{
    color: #fff;
    background-color: #2085e9;
}

.pagination .page-item:disabled{
    background-color: #ebf5ff;
}

.pagination .active{
    color: #fff;
    background-color: #2085e9;
}

/* .pagination .active:hover{
    background-color: #fd7171;
} */


.pagination .disabled{
    color: #babcbd;
    background-color: #e2e2e2;
    border: 1px solid #cccdcf;

}

.pagination .disabled:hover{
    color: #babcbd;
    background-color: #e2e2e2;
    border: 1px solid #cccdcf;
}

/* On smaller screens, decrease text size */
@media only screen and (max-width: 300px) {
    .prev,
    .next,
    .text {
        font-size: 11px;
    }
}


@media not all and (min-width: 1024px) {
    header .collapsable-header {
        position: fixed;
        right: 0px;
        flex-direction: column;
        opacity: 0;
        height: 100vh;
        height: 100dvh;
        width: 0vw;
        justify-content: space-between;
        padding: 5px;
        padding-top: 5%;
        padding-bottom: 5%;
        place-items: end;
        background-color: #ffffff;
        color: #000000;
        overflow-y: auto;
        box-shadow: 2px 0px 3px #000;
    }

    .header-links{
        color: black;
    }
    
}