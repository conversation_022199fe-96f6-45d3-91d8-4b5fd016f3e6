<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Notifications - HiSage Health</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .notification-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
        }
        
        .notification-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            margin-bottom: 1rem;
            overflow: hidden;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }
        
        .notification-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
        }

        .notification-card.clickable {
            cursor: pointer;
        }

        .notification-card.clickable:hover {
            background-color: #f8fafc;
        }
        
        .notification-card.unread {
            border-left: 4px solid #3b82f6;
        }
        
        .notification-card.unread::before {
            content: '';
            position: absolute;
            top: 1rem;
            right: 1rem;
            width: 8px;
            height: 8px;
            background: #3b82f6;
            border-radius: 50%;
        }
        
        .notification-type-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
        }
        
        .type-message {
            background: #dbeafe;
            color: #1e40af;
        }
        
        .type-audio {
            background: #dcfce7;
            color: #166534;
        }
        
        .type-system {
            background: #fef3c7;
            color: #92400e;
        }
        
        .notification-time {
            color: #6b7280;
            font-size: 0.875rem;
        }
        
        .notification-actions {
            padding: 1rem;
            background: #f9fafb;
            border-top: 1px solid #e5e7eb;
        }
        
        .empty-state {
            text-align: center;
            padding: 4rem 2rem;
            color: #6b7280;
        }
        
        .empty-state i {
            font-size: 4rem;
            margin-bottom: 1rem;
            color: #d1d5db;
        }
        
        .loading-spinner {
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 3rem;
        }
        
        .filter-tabs {
            background: white;
            border-radius: 12px;
            padding: 0.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        
        .filter-tab {
            border: none;
            background: transparent;
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            color: #6b7280;
            font-weight: 500;
            transition: all 0.2s ease;
        }
        
        .filter-tab.active {
            background: #3b82f6;
            color: white;
        }
        

    </style>
</head>
<body>
    <div class="notification-header">
        <div class="container">
            <h1 class="mb-0">
                <i class="bi bi-bell me-2"></i>
                Notifications
            </h1>
            <p class="mb-0 mt-2">Stay updated with your messages and audio analysis results</p>
        </div>
    </div>

    <div class="container">
        <!-- Filter Tabs -->
        <div class="filter-tabs">
            <button class="filter-tab active" data-filter="all">All Notifications</button>
            <button class="filter-tab" data-filter="message_reply">Message Replies</button>
            <button class="filter-tab" data-filter="audio_complete">Audio Analysis</button>
            <button class="filter-tab" data-filter="system">System</button>
        </div>

        <!-- Actions -->
        <div class="d-flex justify-content-between align-items-center mb-3">
            <div>
                <span id="notification-count" class="text-muted">Loading notifications...</span>
            </div>
            <div>
                <button id="mark-all-read" class="btn btn-outline-primary me-2">
                    <i class="bi bi-check-all me-1"></i>
                    Mark All as Read
                </button>
                <button id="test-modal" class="btn btn-outline-secondary">
                    <i class="bi bi-gear me-1"></i>
                    Test Modal
                </button>
            </div>
        </div>

        <!-- Authentication Check -->
        <div id="auth-check" class="text-center p-4" style="display: none;">
            <div class="alert alert-info">
                <i class="bi bi-info-circle me-2"></i>
                Checking authentication status...
            </div>
        </div>

        <!-- Loading State -->
        <div id="loading-state" class="loading-spinner">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
        </div>

        <!-- Notifications Container -->
        <div id="notifications-container"></div>

        <!-- Empty State -->
        <div id="empty-state" class="empty-state" style="display: none;">
            <i class="bi bi-bell-slash"></i>
            <h3>No Notifications</h3>
            <p>You don't have any notifications yet. When you receive messages or complete audio analysis, they'll appear here.</p>
        </div>
    </div>

    <!-- Message Detail Modal -->
    <div class="modal fade" id="messageDetailModal" tabindex="-1" aria-labelledby="messageDetailModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="messageDetailModalLabel">
                        <i class="bi bi-chat-dots me-2"></i>
                        Message Details
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div id="messageDetailContent">
                        <div class="text-center p-4">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Get API base URL from Django settings
        const API_BASE_URL = '{{ API_BASE_URL }}';
        
        class NotificationManager {
            constructor() {
                this.currentFilter = 'all';
                this.notifications = [];
                this.init();
            }

            init() {
                this.bindEvents();
                this.loadNotifications();
            }

            bindEvents() {
                // Filter tabs
                document.querySelectorAll('.filter-tab').forEach(tab => {
                    tab.addEventListener('click', (e) => {
                        this.setActiveFilter(e.target.dataset.filter);
                    });
                });

                // Mark all as read
                document.getElementById('mark-all-read').addEventListener('click', () => {
                    this.markAllAsRead();
                });

                // Test modal button
                document.getElementById('test-modal').addEventListener('click', () => {
                    console.log('🧪 Test modal button clicked');
                    this.testModal();
                });
            }

            setActiveFilter(filter) {
                this.currentFilter = filter;
                
                // Update active tab
                document.querySelectorAll('.filter-tab').forEach(tab => {
                    tab.classList.remove('active');
                });
                document.querySelector(`[data-filter="${filter}"]`).classList.add('active');
                
                // Filter notifications
                this.renderNotifications();
            }

            async loadNotifications() {
                try {
                    const token = localStorage.getItem('access_token');
                    if (!token) {
                        console.log('No access token found, redirecting to login');
                        window.location.href = '/login/';
                        return;
                    }

                    console.log('Loading notifications with token:', token.substring(0, 20) + '...');

                    const response = await fetch(`${API_BASE_URL}/api/user/notifications/`, {
                        headers: {
                            'Authorization': `Bearer ${token}`,
                            'Content-Type': 'application/json'
                        }
                    });

                    console.log('Notifications API response status:', response.status);

                    if (response.status === 401) {
                        console.log('Unauthorized, redirecting to login');
                        localStorage.removeItem('access_token');
                        window.location.href = '/login/';
                        return;
                    }

                    const data = await response.json();
                    console.log('Notifications API response data:', data);

                    if (data.success) {
                        this.notifications = data.data.notifications;
                        console.log('🔔 Loaded notifications:', this.notifications);

                        // Debug: Check if notifications have related_object_id
                        this.notifications.forEach(n => {
                            console.log('🔔 Notification found:', {
                                id: n.id,
                                type: n.notification_type,
                                related_object_id: n.related_object_id,
                                title: n.title,
                                message: n.message,
                                is_read: n.is_read,
                                created_at: n.created_at
                            });

                            if (n.notification_type === 'message_reply') {
                                console.log('🔔 Message reply notification found');
                            } else if (n.notification_type === 'audio_complete') {
                                console.log('🔔 Audio complete notification found');
                            } else if (n.notification_type === 'audio_failed') {
                                console.log('🔔 Audio failed notification found');
                            }
                        });

                        this.renderNotifications();
                        this.updateNotificationCount(data.data.unread_count);
                    } else {
                        this.showError('Failed to load notifications: ' + (data.error || 'Unknown error'));
                    }
                } catch (error) {
                    console.error('Error loading notifications:', error);
                    this.showError('Failed to load notifications: ' + error.message);
                } finally {
                    document.getElementById('loading-state').style.display = 'none';
                }
            }

            renderNotifications() {
                const container = document.getElementById('notifications-container');
                const emptyState = document.getElementById('empty-state');
                
                let filteredNotifications = this.notifications;
                if (this.currentFilter !== 'all') {
                    filteredNotifications = this.notifications.filter(n => n.notification_type === this.currentFilter);
                }

                if (filteredNotifications.length === 0) {
                    container.innerHTML = '';
                    emptyState.style.display = 'block';
                    return;
                }

                emptyState.style.display = 'none';
                
                container.innerHTML = filteredNotifications.map(notification =>
                    this.renderNotificationCard(notification)
                ).join('');

                // Bind click events for mark as read
                container.querySelectorAll('.mark-read-btn').forEach(btn => {
                    btn.addEventListener('click', (e) => {
                        e.stopPropagation();
                        this.markAsRead(btn.dataset.notificationId);
                    });
                });

                // Bind click events for notification cards
                container.querySelectorAll('.notification-card').forEach(card => {
                    card.addEventListener('click', (e) => {
                        console.log('Notification card clicked:', e.target);

                        // Don't trigger if clicking on buttons
                        if (e.target.closest('.mark-read-btn')) {
                            console.log('Clicked on mark-read button, ignoring');
                            return;
                        }

                        const notificationId = card.dataset.notificationId;
                        const notificationType = card.dataset.notificationType;

                        console.log('Handling notification click:', {
                            notificationId,
                            notificationType
                        });

                        this.handleNotificationClick(notificationId, notificationType);
                    });
                });
            }

            renderNotificationCard(notification) {
                const typeConfig = this.getTypeConfig(notification.notification_type);
                const isUnread = !notification.is_read;
                const isClickable = notification.notification_type === 'message_reply';

                console.log('🔔 Rendering notification card:', {
                    id: notification.id,
                    type: notification.notification_type,
                    isClickable,
                    related_object_id: notification.related_object_id
                });

                return `
                    <div class="notification-card ${isUnread ? 'unread' : ''} ${isClickable ? 'clickable' : ''} position-relative"
                         data-notification-id="${notification.id}"
                         data-notification-type="${notification.notification_type}"
                         ${isClickable ? 'style="cursor: pointer;"' : ''}>
                        <div class="p-3">
                            <div class="d-flex align-items-start gap-3">
                                <div class="notification-type-icon ${typeConfig.class}">
                                    <i class="bi ${typeConfig.icon}"></i>
                                </div>
                                <div class="flex-grow-1">
                                    <div class="d-flex justify-content-between align-items-start mb-2">
                                        <h6 class="mb-0 fw-semibold">${notification.title}</h6>
                                        <span class="notification-time">${notification.time_ago}</span>
                                    </div>
                                    <p class="mb-0 text-muted">${notification.message}</p>
                                    ${isClickable ? '<small class="text-primary"><i class="bi bi-cursor-fill me-1"></i>Click to view details</small>' : ''}
                                </div>
                            </div>
                        </div>
                        ${isUnread ? `
                            <div class="notification-actions">
                                <button class="btn btn-sm btn-outline-primary mark-read-btn" data-notification-id="${notification.id}">
                                    <i class="bi bi-check me-1"></i>
                                    Mark as Read
                                </button>
                            </div>
                        ` : ''}
                    </div>
                `;
            }

            getTypeConfig(type) {
                const configs = {
                    'message_reply': {
                        icon: 'bi-chat-dots',
                        class: 'type-message'
                    },
                    'audio_complete': {
                        icon: 'bi-music-note-beamed',
                        class: 'type-audio'
                    },
                    'audio_failed': {
                        icon: 'bi-exclamation-triangle',
                        class: 'type-system'
                    },
                    'system': {
                        icon: 'bi-info-circle',
                        class: 'type-system'
                    }
                };
                return configs[type] || configs['system'];
            }

            async markAsRead(notificationId) {
                try {
                    const token = localStorage.getItem('access_token');
                    if (!token) {
                        window.location.href = '/login/';
                        return;
                    }

                    const response = await fetch(`${API_BASE_URL}/api/user/notifications/${notificationId}/read/`, {
                        method: 'POST',
                        headers: {
                            'Authorization': `Bearer ${token}`,
                            'Content-Type': 'application/json'
                        }
                    });

                    if (response.status === 401) {
                        localStorage.removeItem('access_token');
                        window.location.href = '/login/';
                        return;
                    }

                    if (response.ok) {
                        // Update local notification state
                        const notification = this.notifications.find(n => n.id === notificationId);
                        if (notification) {
                            notification.is_read = true;
                        }
                        this.renderNotifications();
                        this.updateNotificationCount();
                    }
                } catch (error) {
                    console.error('Error marking notification as read:', error);
                }
            }

            async markAllAsRead() {
                try {
                    const token = localStorage.getItem('access_token');
                    if (!token) {
                        window.location.href = '/login/';
                        return;
                    }

                    const response = await fetch(`${API_BASE_URL}/api/user/notifications/mark-all-read/`, {
                        method: 'POST',
                        headers: {
                            'Authorization': `Bearer ${token}`,
                            'Content-Type': 'application/json'
                        }
                    });

                    if (response.status === 401) {
                        localStorage.removeItem('access_token');
                        window.location.href = '/login/';
                        return;
                    }

                    if (response.ok) {
                        // Update all notifications to read
                        this.notifications.forEach(n => n.is_read = true);
                        this.renderNotifications();
                        this.updateNotificationCount();
                    }
                } catch (error) {
                    console.error('Error marking all notifications as read:', error);
                }
            }

            updateNotificationCount(unreadCount = null) {
                if (unreadCount === null) {
                    unreadCount = this.notifications.filter(n => !n.is_read).length;
                }
                
                const countElement = document.getElementById('notification-count');
                const total = this.notifications.length;
                
                if (total === 0) {
                    countElement.textContent = 'No notifications';
                } else if (unreadCount === 0) {
                    countElement.textContent = `${total} notification${total !== 1 ? 's' : ''} (all read)`;
                } else {
                    countElement.textContent = `${total} notification${total !== 1 ? 's' : ''} (${unreadCount} unread)`;
                }
            }

            showError(message) {
                const container = document.getElementById('notifications-container');
                container.innerHTML = `
                    <div class="alert alert-danger" role="alert">
                        <i class="bi bi-exclamation-triangle me-2"></i>
                        ${message}
                    </div>
                `;
            }

            async handleNotificationClick(notificationId, notificationType) {
                console.log('🔔 handleNotificationClick called:', {
                    notificationId,
                    notificationType
                });

                // Only handle message reply notifications for now
                if (notificationType === 'message_reply') {
                    console.log('🔔 Processing message_reply notification');
                    await this.showMessageDetail(notificationId);

                    // Mark as read when clicked
                    if (!this.isNotificationRead(notificationId)) {
                        console.log('🔔 Marking notification as read');
                        await this.markAsRead(notificationId);
                    } else {
                        console.log('🔔 Notification already read');
                    }
                } else {
                    console.log('🔔 Notification type not clickable:', notificationType);
                }
            }

            isNotificationRead(notificationId) {
                const notification = this.notifications.find(n => n.id === notificationId);
                return notification ? notification.is_read : true;
            }

            async showMessageDetail(notificationId) {
                try {
                    console.log('🔔 showMessageDetail called for notification:', notificationId);

                    // Find the notification to get message ID
                    const notification = this.notifications.find(n => n.id === notificationId);
                    console.log('🔔 Found notification:', notification);

                    if (!notification) {
                        console.error('🔔 Notification not found:', notificationId);
                        this.showModalError('Notification not found');
                        return;
                    }

                    if (!notification.related_object_id) {
                        console.error('🔔 No related_object_id in notification:', notification);
                        this.showModalError('Message details not available - no related object ID');
                        return;
                    }

                    const messageId = notification.related_object_id;
                    console.log('🔔 Using message ID:', messageId);

                    // Show modal with loading state
                    const modal = new bootstrap.Modal(document.getElementById('messageDetailModal'));
                    document.getElementById('messageDetailContent').innerHTML = `
                        <div class="text-center p-4">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading message details...</span>
                            </div>
                            <p class="mt-2 text-muted">Loading message details...</p>
                        </div>
                    `;
                    modal.show();

                    // Fetch message details
                    const token = localStorage.getItem('access_token');
                    if (!token) {
                        window.location.href = '/login/';
                        return;
                    }

                    console.log('🔔 Making API request to:', `${API_BASE_URL}/api/user/messages/${messageId}/`);

                    const response = await fetch(`${API_BASE_URL}/api/user/messages/${messageId}/`, {
                        headers: {
                            'Authorization': `Bearer ${token}`,
                            'Content-Type': 'application/json'
                        }
                    });

                    console.log('🔔 API response status:', response.status);
                    console.log('🔔 API response headers:', response.headers);

                    if (response.status === 401) {
                        localStorage.removeItem('access_token');
                        window.location.href = '/login/';
                        return;
                    }

                    if (!response.ok) {
                        // Try to get error details from response
                        let errorText = `HTTP ${response.status}: ${response.statusText}`;
                        try {
                            const errorData = await response.json();
                            if (errorData.error) {
                                errorText += ` - ${errorData.error}`;
                            }
                            console.log('🔔 Error response data:', errorData);
                        } catch (e) {
                            console.log('🔔 Could not parse error response as JSON');
                        }
                        throw new Error(errorText);
                    }

                    const data = await response.json();

                    if (data.success) {
                        this.renderMessageDetail(data.data);
                    } else {
                        this.showModalError(data.error || 'Failed to load message details');
                    }

                } catch (error) {
                    console.error('Error loading message details:', error);
                    this.showModalError('Failed to load message details: ' + error.message);
                }
            }

            renderMessageDetail(messageData) {
                const message = messageData.message;
                const replies = messageData.replies || [];

                const repliesHtml = replies.length > 0 ? `
                    <div class="mt-4">
                        <h6 class="fw-semibold mb-3">
                            <i class="bi bi-reply me-2"></i>
                            Admin Replies (${replies.length})
                        </h6>
                        ${replies.map(reply => `
                            <div class="card mb-3">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-start mb-2">
                                        <strong class="text-primary">
                                            <i class="bi bi-person-badge me-1"></i>
                                            ${reply.admin_name || 'Admin'}
                                        </strong>
                                        <small class="text-muted">${this.formatDateTime(reply.created_at)}</small>
                                    </div>
                                    <p class="mb-0">${reply.message}</p>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                ` : `
                    <div class="mt-4">
                        <div class="alert alert-info">
                            <i class="bi bi-info-circle me-2"></i>
                            No admin replies yet.
                        </div>
                    </div>
                `;

                document.getElementById('messageDetailContent').innerHTML = `
                    <div class="message-detail">
                        <!-- Original Message -->
                        <div class="card">
                            <div class="card-header bg-light">
                                <h6 class="mb-0">
                                    <i class="bi bi-envelope me-2"></i>
                                    Original Message
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="row mb-3">
                                    <div class="col-sm-3"><strong>From:</strong></div>
                                    <div class="col-sm-9">${message.name} (${message.email})</div>
                                </div>
                                <div class="row mb-3">
                                    <div class="col-sm-3"><strong>Subject:</strong></div>
                                    <div class="col-sm-9">${message.subject}</div>
                                </div>
                                <div class="row mb-3">
                                    <div class="col-sm-3"><strong>Date:</strong></div>
                                    <div class="col-sm-9">${this.formatDateTime(message.created_at)}</div>
                                </div>
                                <div class="row mb-3">
                                    <div class="col-sm-3"><strong>Status:</strong></div>
                                    <div class="col-sm-9">
                                        <span class="badge ${this.getStatusBadgeClass(message.status)}">
                                            ${message.status}
                                        </span>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-sm-3"><strong>Message:</strong></div>
                                    <div class="col-sm-9">
                                        <div class="border rounded p-3 bg-light">
                                            ${message.message.replace(/\n/g, '<br>')}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        ${repliesHtml}
                    </div>
                `;
            }

            showModalError(message) {
                document.getElementById('messageDetailContent').innerHTML = `
                    <div class="alert alert-danger" role="alert">
                        <i class="bi bi-exclamation-triangle me-2"></i>
                        ${message}
                    </div>
                `;
            }

            formatDateTime(dateString) {
                // Use timezone manager if available, otherwise fallback to default formatting
                if (window.parent && window.parent.timezoneManager) {
                    return window.parent.timezoneManager.formatTime(dateString, {
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric',
                        hour: '2-digit',
                        minute: '2-digit'
                    });
                }

                // Fallback formatting with US Eastern timezone
                const date = new Date(dateString);
                return date.toLocaleString('en-US', {
                    timeZone: 'America/New_York',
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit'
                });
            }

            getStatusBadgeClass(status) {
                const statusClasses = {
                    'pending': 'bg-warning text-dark',
                    'in_progress': 'bg-info text-white',
                    'resolved': 'bg-success text-white',
                    'closed': 'bg-secondary text-white'
                };
                return statusClasses[status] || 'bg-secondary text-white';
            }

            testModal() {
                console.log('🧪 Testing modal functionality');

                // Show modal with test content
                const modal = new bootstrap.Modal(document.getElementById('messageDetailModal'));
                document.getElementById('messageDetailContent').innerHTML = `
                    <div class="alert alert-info">
                        <h5><i class="bi bi-info-circle me-2"></i>Modal Test</h5>
                        <p>This is a test to verify the modal is working correctly.</p>
                        <p><strong>Bootstrap version:</strong> ${bootstrap.Modal.VERSION || 'Unknown'}</p>
                        <p><strong>Current time:</strong> ${new Date().toLocaleString()}</p>
                    </div>
                `;

                try {
                    modal.show();
                    console.log('🧪 Modal shown successfully');
                } catch (error) {
                    console.error('🧪 Error showing modal:', error);
                }
            }
        }

        // Check authentication and initialize
        document.addEventListener('DOMContentLoaded', () => {
            console.log('Notifications page loaded');

            // Show auth check message
            document.getElementById('auth-check').style.display = 'block';
            document.getElementById('loading-state').style.display = 'none';

            // Check if user is authenticated
            const token = localStorage.getItem('access_token');
            const userAuthenticated = {{ user_authenticated|yesno:"true,false" }};

            console.log('Token exists:', !!token);
            console.log('User authenticated (Django):', userAuthenticated);

            if (!token && !userAuthenticated) {
                console.log('No authentication found, redirecting to login');
                // Show message before redirect
                document.getElementById('auth-check').innerHTML = `
                    <div class="alert alert-warning">
                        <i class="bi bi-exclamation-triangle me-2"></i>
                        Authentication required. Redirecting to login...
                    </div>
                `;

                setTimeout(() => {
                    window.location.href = '/login/';
                }, 2000);
                return;
            }

            // Hide auth check and show loading
            document.getElementById('auth-check').style.display = 'none';
            document.getElementById('loading-state').style.display = 'flex';

            // Initialize notification manager
            new NotificationManager();
        });
    </script>
</body>
</html>
