from django.shortcuts import render, get_object_or_404, redirect
from django.contrib import messages
from django.contrib.auth.decorators import login_required
from django.core.paginator import Paginator
from django.http import JsonResponse
from django.views.decorators.http import require_http_methods
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
from django.db.models import Q
from .models import Message, Reply
from .forms import MessageForm, ReplyForm


def message_board_view(request):
    """Display the message board with all messages and posting form"""
    
    # Get search query
    search_query = request.GET.get('search', '')
    
    # Get all active messages
    messages_queryset = Message.objects.filter(is_active=True)
    
    # Apply search filter if provided
    if search_query:
        messages_queryset = messages_queryset.filter(
            Q(title__icontains=search_query) |
            Q(content__icontains=search_query) |
            Q(anonymous_name__icontains=search_query)
        )
    
    # Pagination
    paginator = Paginator(messages_queryset, 10)  # Show 10 messages per page
    page_number = request.GET.get('page')
    messages_page = paginator.get_page(page_number)
    
    # Handle message posting
    message_form = MessageForm(user=request.user)
    
    if request.method == 'POST' and 'post_message' in request.POST:
        message_form = MessageForm(request.POST, request.FILES, user=request.user)
        if message_form.is_valid():
            message = message_form.save(commit=False)
            
            # Set user information based on form data
            if not message.is_anonymous and request.user.is_authenticated:
                message.user = request.user
                message.is_anonymous = False
                message.anonymous_name = ''
                message.anonymous_email = ''
            else:
                message.user = None
                message.is_anonymous = True
            
            message.save()
            messages.success(request, 'Your message has been posted successfully!')
            return redirect('message_board:message_board')
    
    context = {
        'messages': messages_page,
        'message_form': message_form,
        'search_query': search_query,
        'total_messages': messages_queryset.count(),
    }
    
    return render(request, 'message_board/message_board.html', context)


def message_detail_view(request, message_id):
    """Display a single message with its replies and reply form"""
    
    message = get_object_or_404(Message, id=message_id, is_active=True)
    replies = Reply.objects.filter(message=message, is_active=True)
    
    # Handle reply posting (only for authenticated users)
    reply_form = None
    if request.user.is_authenticated:
        reply_form = ReplyForm(user=request.user)
        
        if request.method == 'POST' and 'post_reply' in request.POST:
            reply_form = ReplyForm(request.POST, request.FILES, user=request.user)
            if reply_form.is_valid():
                reply = reply_form.save(commit=False)
                reply.message = message
                reply.user = request.user
                reply.save()
                messages.success(request, 'Your reply has been posted successfully!')
                return redirect('message_board:message_detail', message_id=message.id)
    
    context = {
        'message': message,
        'replies': replies,
        'reply_form': reply_form,
        'can_reply': request.user.is_authenticated,
    }
    
    return render(request, 'message_board/message_detail.html', context)


@require_http_methods(["POST"])
def ajax_post_message(request):
    """AJAX endpoint for posting messages"""
    
    if request.content_type == 'application/json':
        import json
        data = json.loads(request.body)
        form = MessageForm(data, user=request.user)
    else:
        form = MessageForm(request.POST, request.FILES, user=request.user)
    
    if form.is_valid():
        message = form.save(commit=False)
        
        # Set user information based on form data
        if not message.is_anonymous and request.user.is_authenticated:
            message.user = request.user
            message.is_anonymous = False
            message.anonymous_name = ''
            message.anonymous_email = ''
        else:
            message.user = None
            message.is_anonymous = True
        
        message.save()
        
        return JsonResponse({
            'success': True,
            'message': 'Message posted successfully!',
            'message_id': str(message.id)
        })
    else:
        return JsonResponse({
            'success': False,
            'errors': form.errors
        }, status=400)


@login_required
@require_http_methods(["POST"])
def ajax_post_reply(request, message_id):
    """AJAX endpoint for posting replies"""
    
    message = get_object_or_404(Message, id=message_id, is_active=True)
    
    if request.content_type == 'application/json':
        import json
        data = json.loads(request.body)
        form = ReplyForm(data, user=request.user)
    else:
        form = ReplyForm(request.POST, request.FILES, user=request.user)
    
    if form.is_valid():
        reply = form.save(commit=False)
        reply.message = message
        reply.user = request.user
        reply.save()
        
        return JsonResponse({
            'success': True,
            'message': 'Reply posted successfully!',
            'reply_id': str(reply.id)
        })
    else:
        return JsonResponse({
            'success': False,
            'errors': form.errors
        }, status=400)
