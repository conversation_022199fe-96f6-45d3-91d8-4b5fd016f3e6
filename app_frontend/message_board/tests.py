from django.test import TestCase
from django.contrib.auth.models import User
from django.urls import reverse
from .models import Message, Reply


class MessageBoardTestCase(TestCase):
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        
        self.message = Message.objects.create(
            title='Test Message',
            content='This is a test message content.',
            is_anonymous=True,
            anonymous_name='Test User'
        )
    
    def test_message_creation(self):
        """Test that a message can be created"""
        self.assertEqual(self.message.title, 'Test Message')
        self.assertEqual(self.message.get_author_name(), 'Test User')
        self.assertTrue(self.message.is_anonymous)
    
    def test_message_board_view(self):
        """Test that the message board view loads correctly"""
        response = self.client.get(reverse('message_board:message_board'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Test Message')
    
    def test_message_detail_view(self):
        """Test that the message detail view loads correctly"""
        response = self.client.get(reverse('message_board:message_detail', args=[self.message.id]))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Test Message')
    
    def test_reply_creation(self):
        """Test that a reply can be created by authenticated user"""
        self.client.login(username='testuser', password='testpass123')
        
        response = self.client.post(reverse('message_board:message_detail', args=[self.message.id]), {
            'post_reply': '1',
            'content': 'This is a test reply.'
        })
        
        self.assertEqual(response.status_code, 302)  # Redirect after successful post
        
        reply = Reply.objects.filter(message=self.message).first()
        self.assertIsNotNone(reply)
        self.assertEqual(reply.content, 'This is a test reply.')
        self.assertEqual(reply.user, self.user)
    
    def test_anonymous_message_posting(self):
        """Test that anonymous users can post messages"""
        response = self.client.post(reverse('message_board:message_board'), {
            'post_message': '1',
            'title': 'Anonymous Message',
            'content': 'This is an anonymous message.',
            'is_anonymous': True,
            'anonymous_name': 'Anonymous User'
        })
        
        self.assertEqual(response.status_code, 302)  # Redirect after successful post
        
        message = Message.objects.filter(title='Anonymous Message').first()
        self.assertIsNotNone(message)
        self.assertTrue(message.is_anonymous)
        self.assertEqual(message.get_author_name(), 'Anonymous User')
