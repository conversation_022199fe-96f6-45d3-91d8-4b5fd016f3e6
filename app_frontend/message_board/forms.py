from django import forms
from .models import Message, Reply


class MessageForm(forms.ModelForm):
    is_anonymous = forms.BooleanField(
        required=False, 
        initial=True,
        label="Post anonymously",
        help_text="Check this box to post anonymously"
    )
    anonymous_name = forms.CharField(
        max_length=100, 
        required=False,
        initial="Anonymous",
        label="Display name",
        help_text="Name to display (optional, defaults to 'Anonymous')"
    )
    anonymous_email = forms.EmailField(
        required=False,
        label="Email address",
        help_text="Email address (optional, will not be displayed publicly)"
    )
    
    class Meta:
        model = Message
        fields = ['title', 'content', 'image', 'is_anonymous', 'anonymous_name', 'anonymous_email']
        widgets = {
            'title': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Enter message title...',
                'maxlength': 200
            }),
            'content': forms.Textarea(attrs={
                'class': 'form-control',
                'placeholder': 'Write your message here...',
                'rows': 6
            }),
            'image': forms.FileInput(attrs={
                'class': 'form-control',
                'accept': 'image/*'
            }),
            'is_anonymous': forms.CheckboxInput(attrs={
                'class': 'form-check-input'
            }),
            'anonymous_name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Your display name (optional)'
            }),
            'anonymous_email': forms.EmailInput(attrs={
                'class': 'form-control',
                'placeholder': '<EMAIL> (optional)'
            })
        }
        labels = {
            'title': 'Message Title',
            'content': 'Message Content',
            'image': 'Attach Image (Optional)'
        }
    
    def __init__(self, *args, **kwargs):
        self.user = kwargs.pop('user', None)
        super().__init__(*args, **kwargs)
        
        # If user is authenticated, show option to post with their name
        if self.user and self.user.is_authenticated:
            self.fields['is_anonymous'].help_text = f"Uncheck to post as {self.user.get_full_name() or self.user.username}"
    
    def clean(self):
        cleaned_data = super().clean()
        is_anonymous = cleaned_data.get('is_anonymous', True)
        anonymous_name = cleaned_data.get('anonymous_name', '')
        
        # If posting anonymously and no name provided, use default
        if is_anonymous and not anonymous_name.strip():
            cleaned_data['anonymous_name'] = 'Anonymous'
        
        return cleaned_data


class ReplyForm(forms.ModelForm):
    class Meta:
        model = Reply
        fields = ['content', 'image']
        widgets = {
            'content': forms.Textarea(attrs={
                'class': 'form-control',
                'placeholder': 'Write your reply here...',
                'rows': 4
            }),
            'image': forms.FileInput(attrs={
                'class': 'form-control',
                'accept': 'image/*'
            })
        }
        labels = {
            'content': 'Reply Content',
            'image': 'Attach Image (Optional)'
        }
    
    def __init__(self, *args, **kwargs):
        self.user = kwargs.pop('user', None)
        super().__init__(*args, **kwargs)
    
    def clean(self):
        cleaned_data = super().clean()
        
        # Ensure user is authenticated for replies
        if not self.user or not self.user.is_authenticated:
            raise forms.ValidationError("You must be logged in to reply to messages.")
        
        return cleaned_data
