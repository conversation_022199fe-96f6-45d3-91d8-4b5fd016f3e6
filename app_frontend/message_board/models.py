from django.db import models
from django.contrib.auth.models import User
from django.utils import timezone
import uuid


class Message(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    
    # User information - can be anonymous or registered
    user = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    is_anonymous = models.BooleanField(default=True)
    anonymous_name = models.CharField(max_length=100, blank=True, default="Anonymous")
    anonymous_email = models.EmailField(blank=True)
    
    # Message content
    title = models.CharField(max_length=200)
    content = models.TextField()
    image = models.ImageField(upload_to='message_board/images/', blank=True, null=True)
    
    # Timestamps
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)
    
    # Status
    is_active = models.BooleanField(default=True)
    
    class Meta:
        ordering = ['-created_at']
        verbose_name = 'Message'
        verbose_name_plural = 'Messages'
    
    def __str__(self):
        return f"{self.title} - {self.get_author_name()}"
    
    def get_author_name(self):
        if self.is_anonymous:
            return self.anonymous_name or "Anonymous"
        elif self.user:
            return self.user.get_full_name() or self.user.username
        else:
            return "Anonymous"
    
    def get_author_email(self):
        if self.is_anonymous:
            return self.anonymous_email
        elif self.user:
            return self.user.email
        else:
            return ""
    
    def can_reply(self, user):
        """Check if a user can reply to this message"""
        return user and user.is_authenticated
    
    def get_replies_count(self):
        return self.replies.filter(is_active=True).count()


class Reply(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    
    # Related message
    message = models.ForeignKey(Message, on_delete=models.CASCADE, related_name='replies')
    
    # User information - only registered users can reply
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    
    # Reply content
    content = models.TextField()
    image = models.ImageField(upload_to='message_board/replies/', blank=True, null=True)
    
    # Timestamps
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)
    
    # Status
    is_active = models.BooleanField(default=True)
    
    class Meta:
        ordering = ['created_at']
        verbose_name = 'Reply'
        verbose_name_plural = 'Replies'
    
    def __str__(self):
        return f"Reply to '{self.message.title}' by {self.user.username}"
    
    def get_author_name(self):
        return self.user.get_full_name() or self.user.username
    
    def get_author_email(self):
        return self.user.email
