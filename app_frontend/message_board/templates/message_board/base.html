{% extends "html/base.html" %}
{% load static %}

{% block title %}Message Board - HiSage{% endblock %}

{% block content %}
<div class="message-board-container">
    {% block message_board_content %}
    {% endblock %}
</div>

<style>
/* Message Board Styles */
.message-board-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
    background: #f8f9fa;
    min-height: calc(100vh - 100px);
}

.message-board-header {
    text-align: center;
    margin-bottom: 3rem;
    padding: 2rem;
    background: linear-gradient(135deg, #0066cc, #3399ff);
    color: white;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0, 102, 204, 0.2);
}

.message-board-header h1 {
    font-size: 2.5rem;
    margin-bottom: 0.5rem;
    font-weight: 700;
}

.message-board-header p {
    font-size: 1.1rem;
    opacity: 0.9;
    margin: 0;
}

.post-message-section {
    background: white;
    padding: 2rem;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    margin-bottom: 2rem;
}

.post-message-section h3 {
    color: #0066cc;
    margin-bottom: 1.5rem;
    font-size: 1.5rem;
    font-weight: 600;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #333;
}

.form-control {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-control:focus {
    outline: none;
    border-color: #0066cc;
    box-shadow: 0 0 0 3px rgba(0, 102, 204, 0.1);
}

.form-check {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.form-check-input {
    width: auto;
}

.btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
    text-align: center;
}

.btn-primary {
    background: #0066cc;
    color: white;
}

.btn-primary:hover {
    background: #0052a3;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 102, 204, 0.3);
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #545b62;
}

.messages-section {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.messages-header {
    background: #f8f9fa;
    padding: 1.5rem 2rem;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.messages-header h3 {
    color: #0066cc;
    margin: 0;
    font-size: 1.5rem;
    font-weight: 600;
}

.search-form {
    display: flex;
    gap: 0.5rem;
}

.search-form input {
    padding: 0.5rem;
    border: 2px solid #e9ecef;
    border-radius: 6px;
    font-size: 0.9rem;
}

.search-form button {
    padding: 0.5rem 1rem;
    background: #0066cc;
    color: white;
    border: none;
    border-radius: 6px;
    cursor: pointer;
}

.message-item {
    padding: 2rem;
    border-bottom: 1px solid #e9ecef;
    transition: background-color 0.3s ease;
}

.message-item:hover {
    background: #f8f9fa;
}

.message-item:last-child {
    border-bottom: none;
}

.message-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;
}

.message-title {
    font-size: 1.3rem;
    font-weight: 600;
    color: #0066cc;
    margin: 0 0 0.5rem 0;
}

.message-meta {
    font-size: 0.9rem;
    color: #6c757d;
    display: flex;
    gap: 1rem;
    align-items: center;
}

.message-content {
    color: #333;
    line-height: 1.6;
    margin-bottom: 1rem;
}

.message-image {
    max-width: 100%;
    height: auto;
    border-radius: 8px;
    margin: 1rem 0;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.message-actions {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.reply-count {
    background: #e9ecef;
    color: #495057;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 500;
}

.pagination {
    display: flex;
    justify-content: center;
    gap: 0.5rem;
    margin: 2rem 0;
}

.pagination a, .pagination span {
    padding: 0.5rem 1rem;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    text-decoration: none;
    color: #0066cc;
}

.pagination .current {
    background: #0066cc;
    color: white;
    border-color: #0066cc;
}

.alert {
    padding: 1rem;
    border-radius: 8px;
    margin-bottom: 1rem;
}

.alert-success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.alert-error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* Responsive Design */
@media (max-width: 768px) {
    .message-board-container {
        padding: 1rem;
    }
    
    .message-board-header h1 {
        font-size: 2rem;
    }
    
    .post-message-section,
    .message-item {
        padding: 1.5rem;
    }
    
    .messages-header {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }
    
    .search-form {
        width: 100%;
    }
    
    .search-form input {
        flex: 1;
    }
    
    .message-header {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .message-meta {
        flex-wrap: wrap;
    }
}
</style>
{% endblock %}
