{% extends "message_board/base.html" %}
{% load static %}

{% block message_board_content %}
<!-- Navigation -->
<div style="margin-bottom: 2rem;">
    <a href="{% url 'message_board:message_board' %}" class="btn btn-secondary">
        <i class="fas fa-arrow-left"></i> Back to Message Board
    </a>
</div>

<!-- Display Messages -->
{% if messages %}
    {% for message in messages %}
        <div class="alert alert-{{ message.tags }}">
            {{ message }}
        </div>
    {% endfor %}
{% endif %}

<!-- Message Detail -->
<div class="messages-section">
    <div class="message-item" style="border-bottom: none;">
        <div class="message-header">
            <div>
                <h2 class="message-title" style="font-size: 2rem; margin-bottom: 1rem;">
                    {{ message.title }}
                </h2>
                <div class="message-meta">
                    <span><i class="fas fa-user"></i> {{ message.get_author_name }}</span>
                    <span><i class="fas fa-clock"></i> {{ message.created_at|date:"M d, Y H:i" }}</span>
                    <span><i class="fas fa-reply"></i> {{ replies.count }} repl{{ replies.count|pluralize:"y,ies" }}</span>
                </div>
            </div>
        </div>
        
        <div class="message-content" style="font-size: 1.1rem; line-height: 1.7;">
            {{ message.content|linebreaks }}
        </div>
        
        {% if message.image %}
            <img src="{{ message.image.url }}" alt="Message image" class="message-image">
        {% endif %}
    </div>
</div>

<!-- Replies Section -->
{% if replies %}
    <div class="messages-section" style="margin-top: 2rem;">
        <div class="messages-header">
            <h3><i class="fas fa-comments"></i> Replies ({{ replies.count }})</h3>
        </div>
        
        {% for reply in replies %}
            <div class="message-item">
                <div class="message-header">
                    <div>
                        <div class="message-meta">
                            <span><i class="fas fa-user"></i> {{ reply.get_author_name }}</span>
                            <span><i class="fas fa-clock"></i> {{ reply.created_at|date:"M d, Y H:i" }}</span>
                        </div>
                    </div>
                </div>
                
                <div class="message-content">
                    {{ reply.content|linebreaks }}
                </div>
                
                {% if reply.image %}
                    <img src="{{ reply.image.url }}" alt="Reply image" class="message-image" style="max-width: 400px;">
                {% endif %}
            </div>
        {% endfor %}
    </div>
{% endif %}

<!-- Reply Form Section -->
{% if can_reply %}
    <div class="post-message-section" style="margin-top: 2rem;" id="reply-form">
        <h3><i class="fas fa-reply"></i> Post a Reply</h3>
        
        <form method="post" enctype="multipart/form-data">
            {% csrf_token %}
            <input type="hidden" name="post_reply" value="1">
            
            <div class="form-group">
                <label for="{{ reply_form.content.id_for_label }}">{{ reply_form.content.label }}</label>
                {{ reply_form.content }}
                {% if reply_form.content.help_text %}
                    <small class="form-text text-muted">{{ reply_form.content.help_text }}</small>
                {% endif %}
                {% if reply_form.content.errors %}
                    <div class="text-danger">{{ reply_form.content.errors }}</div>
                {% endif %}
            </div>
            
            <div class="form-group">
                <label for="{{ reply_form.image.id_for_label }}">{{ reply_form.image.label }}</label>
                {{ reply_form.image }}
                {% if reply_form.image.help_text %}
                    <small class="form-text text-muted">{{ reply_form.image.help_text }}</small>
                {% endif %}
                {% if reply_form.image.errors %}
                    <div class="text-danger">{{ reply_form.image.errors }}</div>
                {% endif %}
            </div>
            
            <button type="submit" class="btn btn-primary">
                <i class="fas fa-paper-plane"></i> Post Reply
            </button>
        </form>
    </div>
{% else %}
    <div class="post-message-section" style="margin-top: 2rem; text-align: center; background: #f8f9fa;">
        <h3><i class="fas fa-lock"></i> Login Required</h3>
        <p>You need to be logged in to reply to messages.</p>
        <a href="{% url 'login' %}" class="btn btn-primary">
            <i class="fas fa-sign-in-alt"></i> Login
        </a>
        <a href="{% url 'register' %}" class="btn btn-secondary">
            <i class="fas fa-user-plus"></i> Register
        </a>
    </div>
{% endif %}

<!-- Additional Styles for Detail Page -->
<style>
.message-detail-container {
    max-width: 900px;
    margin: 0 auto;
}

.reply-item {
    background: #f8f9fa;
    border-left: 4px solid #0066cc;
    margin-left: 2rem;
}

@media (max-width: 768px) {
    .reply-item {
        margin-left: 0;
    }
}
</style>
{% endblock %}
