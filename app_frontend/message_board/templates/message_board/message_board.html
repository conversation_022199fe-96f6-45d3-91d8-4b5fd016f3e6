{% extends "message_board/base.html" %}
{% load static %}

{% block message_board_content %}
<!-- Header Section -->
<div class="message-board-header">
    <h1><i class="fas fa-comments"></i> Message Board</h1>
    <p>Share your thoughts, ask questions, and connect with the community</p>
</div>

<!-- Display Messages -->
{% if messages %}
    {% for message in messages %}
        <div class="alert alert-{{ message.tags }}">
            {{ message }}
        </div>
    {% endfor %}
{% endif %}

<!-- Post New Message Section -->
<div class="post-message-section">
    <h3><i class="fas fa-edit"></i> Post a New Message</h3>
    
    <form method="post" enctype="multipart/form-data">
        {% csrf_token %}
        <input type="hidden" name="post_message" value="1">
        
        <div class="form-group">
            <label for="{{ message_form.title.id_for_label }}">{{ message_form.title.label }}</label>
            {{ message_form.title }}
            {% if message_form.title.help_text %}
                <small class="form-text text-muted">{{ message_form.title.help_text }}</small>
            {% endif %}
            {% if message_form.title.errors %}
                <div class="text-danger">{{ message_form.title.errors }}</div>
            {% endif %}
        </div>
        
        <div class="form-group">
            <label for="{{ message_form.content.id_for_label }}">{{ message_form.content.label }}</label>
            {{ message_form.content }}
            {% if message_form.content.help_text %}
                <small class="form-text text-muted">{{ message_form.content.help_text }}</small>
            {% endif %}
            {% if message_form.content.errors %}
                <div class="text-danger">{{ message_form.content.errors }}</div>
            {% endif %}
        </div>
        
        <div class="form-group">
            <label for="{{ message_form.image.id_for_label }}">{{ message_form.image.label }}</label>
            {{ message_form.image }}
            {% if message_form.image.help_text %}
                <small class="form-text text-muted">{{ message_form.image.help_text }}</small>
            {% endif %}
            {% if message_form.image.errors %}
                <div class="text-danger">{{ message_form.image.errors }}</div>
            {% endif %}
        </div>
        
        <div class="form-check">
            {{ message_form.is_anonymous }}
            <label for="{{ message_form.is_anonymous.id_for_label }}">
                {{ message_form.is_anonymous.label }}
            </label>
            {% if message_form.is_anonymous.help_text %}
                <small class="form-text text-muted">{{ message_form.is_anonymous.help_text }}</small>
            {% endif %}
        </div>
        
        <div id="anonymous-fields" style="display: block;">
            <div class="form-group">
                <label for="{{ message_form.anonymous_name.id_for_label }}">{{ message_form.anonymous_name.label }}</label>
                {{ message_form.anonymous_name }}
                {% if message_form.anonymous_name.help_text %}
                    <small class="form-text text-muted">{{ message_form.anonymous_name.help_text }}</small>
                {% endif %}
                {% if message_form.anonymous_name.errors %}
                    <div class="text-danger">{{ message_form.anonymous_name.errors }}</div>
                {% endif %}
            </div>
            
            <div class="form-group">
                <label for="{{ message_form.anonymous_email.id_for_label }}">{{ message_form.anonymous_email.label }}</label>
                {{ message_form.anonymous_email }}
                {% if message_form.anonymous_email.help_text %}
                    <small class="form-text text-muted">{{ message_form.anonymous_email.help_text }}</small>
                {% endif %}
                {% if message_form.anonymous_email.errors %}
                    <div class="text-danger">{{ message_form.anonymous_email.errors }}</div>
                {% endif %}
            </div>
        </div>
        
        <button type="submit" class="btn btn-primary">
            <i class="fas fa-paper-plane"></i> Post Message
        </button>
    </form>
</div>

<!-- Messages List Section -->
<div class="messages-section">
    <div class="messages-header">
        <h3><i class="fas fa-list"></i> Recent Messages ({{ total_messages }} total)</h3>
        
        <!-- Search Form -->
        <form method="get" class="search-form">
            <input type="text" name="search" value="{{ search_query }}" placeholder="Search messages...">
            <button type="submit"><i class="fas fa-search"></i></button>
            {% if search_query %}
                <a href="{% url 'message_board:message_board' %}" class="btn btn-secondary">Clear</a>
            {% endif %}
        </form>
    </div>
    
    {% if messages.object_list %}
        {% for message in messages.object_list %}
            <div class="message-item">
                <div class="message-header">
                    <div>
                        <h4 class="message-title">
                            <a href="{% url 'message_board:message_detail' message.id %}" style="text-decoration: none; color: inherit;">
                                {{ message.title }}
                            </a>
                        </h4>
                        <div class="message-meta">
                            <span><i class="fas fa-user"></i> {{ message.get_author_name }}</span>
                            <span><i class="fas fa-clock"></i> {{ message.created_at|date:"M d, Y H:i" }}</span>
                            {% if message.get_replies_count > 0 %}
                                <span class="reply-count">
                                    <i class="fas fa-reply"></i> {{ message.get_replies_count }} repl{{ message.get_replies_count|pluralize:"y,ies" }}
                                </span>
                            {% endif %}
                        </div>
                    </div>
                </div>
                
                <div class="message-content">
                    {{ message.content|truncatewords:50|linebreaks }}
                </div>
                
                {% if message.image %}
                    <img src="{{ message.image.url }}" alt="Message image" class="message-image" style="max-width: 300px;">
                {% endif %}
                
                <div class="message-actions">
                    <a href="{% url 'message_board:message_detail' message.id %}" class="btn btn-primary">
                        <i class="fas fa-eye"></i> View Details
                    </a>
                    {% if user.is_authenticated %}
                        <a href="{% url 'message_board:message_detail' message.id %}#reply-form" class="btn btn-secondary">
                            <i class="fas fa-reply"></i> Reply
                        </a>
                    {% else %}
                        <span class="text-muted">
                            <i class="fas fa-info-circle"></i> Login to reply
                        </span>
                    {% endif %}
                </div>
            </div>
        {% endfor %}
    {% else %}
        <div class="message-item" style="text-align: center; color: #6c757d;">
            <i class="fas fa-comments" style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.5;"></i>
            <h4>No messages found</h4>
            {% if search_query %}
                <p>No messages match your search for "{{ search_query }}".</p>
                <a href="{% url 'message_board:message_board' %}" class="btn btn-primary">View All Messages</a>
            {% else %}
                <p>Be the first to post a message!</p>
            {% endif %}
        </div>
    {% endif %}
</div>

<!-- Pagination -->
{% if messages.has_other_pages %}
    <div class="pagination">
        {% if messages.has_previous %}
            <a href="?page=1{% if search_query %}&search={{ search_query }}{% endif %}">&laquo; First</a>
            <a href="?page={{ messages.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}">&lsaquo; Previous</a>
        {% endif %}
        
        <span class="current">
            Page {{ messages.number }} of {{ messages.paginator.num_pages }}
        </span>
        
        {% if messages.has_next %}
            <a href="?page={{ messages.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}">Next &rsaquo;</a>
            <a href="?page={{ messages.paginator.num_pages }}{% if search_query %}&search={{ search_query }}{% endif %}">Last &raquo;</a>
        {% endif %}
    </div>
{% endif %}

<script>
// Toggle anonymous fields based on checkbox
document.addEventListener('DOMContentLoaded', function() {
    const anonymousCheckbox = document.getElementById('{{ message_form.is_anonymous.id_for_label }}');
    const anonymousFields = document.getElementById('anonymous-fields');
    
    function toggleAnonymousFields() {
        if (anonymousCheckbox.checked) {
            anonymousFields.style.display = 'block';
        } else {
            anonymousFields.style.display = 'none';
        }
    }
    
    // Initial state
    toggleAnonymousFields();
    
    // Listen for changes
    anonymousCheckbox.addEventListener('change', toggleAnonymousFields);
});
</script>
{% endblock %}
