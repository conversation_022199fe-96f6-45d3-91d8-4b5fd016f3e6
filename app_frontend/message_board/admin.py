from django.contrib import admin
from .models import Message, Reply


@admin.register(Message)
class MessageAdmin(admin.ModelAdmin):
    list_display = ['title', 'get_author_name', 'is_anonymous', 'created_at', 'is_active', 'get_replies_count']
    list_filter = ['is_anonymous', 'is_active', 'created_at']
    search_fields = ['title', 'content', 'anonymous_name', 'user__username', 'user__email']
    readonly_fields = ['id', 'created_at', 'updated_at']
    list_per_page = 20
    
    fieldsets = (
        ('Message Information', {
            'fields': ('id', 'title', 'content', 'image')
        }),
        ('Author Information', {
            'fields': ('user', 'is_anonymous', 'anonymous_name', 'anonymous_email')
        }),
        ('Status & Timestamps', {
            'fields': ('is_active', 'created_at', 'updated_at')
        }),
    )
    
    def get_replies_count(self, obj):
        return obj.get_replies_count()
    get_replies_count.short_description = 'Replies'


@admin.register(Reply)
class ReplyAdmin(admin.ModelAdmin):
    list_display = ['message', 'get_author_name', 'created_at', 'is_active']
    list_filter = ['is_active', 'created_at']
    search_fields = ['content', 'user__username', 'user__email', 'message__title']
    readonly_fields = ['id', 'created_at', 'updated_at']
    list_per_page = 20
    
    fieldsets = (
        ('Reply Information', {
            'fields': ('id', 'message', 'content', 'image')
        }),
        ('Author Information', {
            'fields': ('user',)
        }),
        ('Status & Timestamps', {
            'fields': ('is_active', 'created_at', 'updated_at')
        }),
    )
    
    def get_author_name(self, obj):
        return obj.get_author_name()
    get_author_name.short_description = 'Author'
