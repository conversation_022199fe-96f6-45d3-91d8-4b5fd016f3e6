{% extends './base.html' %} 
{% load static %} 
{% block fullpage %}
    <div class="tw-bg-white tw-fixed tw-top-0 tw-z-[5000] tw-flex tw-flex-col tw-place-content-center tw-items-center tw-h-full tw-w-full">

        <div class="tw-bg-white tw-rounded-md tw-shadow-xl tw-max-w-[550px] tw-h-[300px] tw-flex tw-flex-col tw-place-content-center tw-p-4">
            <div class="tw-text-xl">
                A confirmation mail has been sent to <b>{{to_email}}</b>. Please confirm your email
            </div>
            <div class="tw-flex tw-flex-gap-2 tw-space-x-5 tw-place-content-center tw-text-center tw-mt-[10%] ">
                
                <a href="https://outlook.live.com/"  
                    rel="nofollow" target="_blank"
                    class="tw-border-solid tw-border-gray-200 tw-w-[150px] tw-rounded-lg tw-p-1 tw-m-1"
                    >
                    <img src="{% static "./assets/logos/outlook.svg" %}" style="width:50px;height:50px" alt="" srcset="">
                    <p>Open Outlook</p>
                </a>
                
                <a href="https://mail.google.com/mail/u/0/#search/from%3A{{from_email}}"  
                    rel="nofollow" target="_blank"
                    class="tw-border-solid tw-border-gray-200 tw-w-[150px] tw-rounded-lg tw-p-1 tw-m-1"
                    >
                    <img src="{% static "./assets/logos/gmail.svg" %}" style="width:50px;height:50px" alt="" srcset="">
                    <p>Open Gmail</p>
                </a>
    
            </div>
            <div class="tw-text-sm tw-mt-4 tw-text-center">
                <span>Go back to login</span>
                <a href="{% url "login" %}" class="tw-underline tw-text-blue-600">Login</a>
            </div>
            <div class="tw-text-sm tw-mt-2 tw-text-center">
                <span>couldn't find it?</span>
                <a href="{% url "resend-verification" %}" class="tw-underline tw-text-blue-600">resend confirmation</a>
            </div>
        </div>

       
        
    </div>
{% endblock fullpage %}