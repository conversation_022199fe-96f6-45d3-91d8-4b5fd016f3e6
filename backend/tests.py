import argparse
import torch
import os
from sklearn.metrics import root_mean_squared_error

from mmse_prediction.pipeline_functions.other_functions import load_tst_dict
from mmse_prediction.pipeline_functions.pipeline import mmse_predict_pipeline

def parse_args():

    parser = argparse.ArgumentParser(description='Pipeline')
    parser.add_argument('--audio_dir', type=str, default=None)
    parser.add_argument('--meta_file', type=str, default=None)
    parser.add_argument('--format', type=str, default='.wav')
    args = parser.parse_args()

    return args

if __name__ == '__main__':

    ### use the data and models from AD_detection_python_code_backup57

    # --audio_dir /home/<USER>/PhD_learning/AD_detection/corpus/ADReSS-IS2020-data/test/Full_wave_enhanced_audio --meta_file /home/<USER>/PhD_learning/AD_detection/corpus/ADReSS-IS2020-data/test/meta_data_test.csv  # Perfromance rmse1 3.581, rmse2 4.479
    # --audio_dir /home/<USER>/PhD_learning/AD_detection/corpus/ADReSSo21/diagnosis/test-dist/audio --meta_file /home/<USER>/PhD_learning/AD_detection/corpus/ADReSSo21/diagnosis/test-dist/meta_data.csv  # Perfromance rmse1 3.321, rmse2 4.425

    args = parse_args()
    torch.set_default_dtype(torch.float32)
    os.environ["CUDA_VISIBLE_DEVICES"] = "0"

    ## load test datadict
    tst_dict = load_tst_dict(args.meta_file, args.audio_dir, args.format)

    ## moca score prediction pipeline
    pipe = mmse_predict_pipeline()

    all_subject_scores1 = []
    for audio_file in tst_dict['audio_paths']:

        file_name = os.path.basename(audio_file)

        sub_dict = pipe.predict(audio_file)

        scores1 = sub_dict['Predicted mmse score']
        all_subject_scores1.append(scores1)

        print('File name %s, score1 %.3f' % (file_name, scores1))

    rmse1 = root_mean_squared_error(tst_dict['mmses'], all_subject_scores1)

    print("Perfromance rmse1 %.3f" % (rmse1))