#!/usr/bin/env python3
"""
Fix Admin Permissions Script
Run this in the backend directory with: python manage.py shell < fix_admin_permissions.py
"""

print("=" * 60)
print("Fixing Admin Permissions for Message Management")
print("=" * 60)

try:
    from django.contrib.auth import get_user_model
    from django.contrib.auth.models import Group
    from django.db import models
    from api.models import Inquiry

    User = get_user_model()
    
    # Step 1: Create Data Analysts group if it doesn't exist
    print("\n1. CREATING/CHECKING DATA ANALYSTS GROUP")
    print("-" * 40)
    
    group, created = Group.objects.get_or_create(name='Data Analysts')
    if created:
        print("✅ Created 'Data Analysts' group")
    else:
        print("✅ 'Data Analysts' group already exists")
    
    # Step 2: Find admin users and ensure they have proper permissions
    print("\n2. CHECKING ADMIN USERS")
    print("-" * 40)
    
    # Find users who should be admins (staff or superusers)
    potential_admins = User.objects.filter(
        models.Q(is_staff=True) | models.Q(is_superuser=True)
    ).distinct()
    
    if potential_admins.exists():
        print(f"Found {potential_admins.count()} potential admin users:")
        
        for user in potential_admins:
            print(f"\nUser: {user.email}")
            print(f"  is_staff: {user.is_staff}")
            print(f"  is_superuser: {user.is_superuser}")
            print(f"  is_active: {user.is_active}")
            
            # Ensure user is staff (required for message management)
            if not user.is_staff:
                user.is_staff = True
                user.save()
                print(f"  ✅ Set is_staff=True for {user.email}")
            
            # Add to Data Analysts group if not already a member
            if not user.groups.filter(name='Data Analysts').exists():
                user.groups.add(group)
                print(f"  ✅ Added {user.email} to Data Analysts group")
            else:
                print(f"  ✅ {user.email} already in Data Analysts group")
    else:
        print("❌ No admin users found!")
        print("Please create an admin user first:")
        print("  python manage.py createsuperuser")
        
        # Try to find any users and suggest making them admin
        all_users = User.objects.all()[:5]
        if all_users.exists():
            print(f"\nFound {all_users.count()} regular users. You can make one of them admin:")
            for user in all_users:
                print(f"  {user.email} - to make admin, run:")
                print(f"    user = User.objects.get(email='{user.email}')")
                print(f"    user.is_staff = True")
                print(f"    user.save()")
    
    # Step 3: Test permissions for all admin users
    print("\n3. TESTING PERMISSIONS")
    print("-" * 40)
    
    admin_users = User.objects.filter(
        models.Q(is_staff=True) | 
        models.Q(is_superuser=True) | 
        models.Q(groups__name='Data Analysts')
    ).distinct()
    
    for user in admin_users:
        # Test the exact permission logic used in ContactMessageListView
        has_permission = user.is_staff or user.groups.filter(name='Data Analysts').exists()
        
        print(f"\n{user.email}:")
        print(f"  Permission check result: {has_permission}")
        
        if has_permission:
            print(f"  ✅ Should have access to message management")
        else:
            print(f"  ❌ Will be denied access to message management")
            
            # Fix the permission issue
            if not user.is_staff:
                user.is_staff = True
                user.save()
                print(f"  ✅ Fixed: Set is_staff=True")
            
            if not user.groups.filter(name='Data Analysts').exists():
                user.groups.add(group)
                print(f"  ✅ Fixed: Added to Data Analysts group")
    
    # Step 4: Create test inquiry if none exist
    print("\n4. CHECKING TEST DATA")
    print("-" * 40)
    
    inquiry_count = Inquiry.objects.count()
    print(f"Total inquiries in database: {inquiry_count}")
    
    if inquiry_count == 0:
        print("Creating test inquiry...")
        try:
            test_inquiry = Inquiry.objects.create(
                name="Test Admin User",
                email="<EMAIL>",
                inquiry_type=0,
                description="This is a test inquiry created by the permission fix script. Admin should be able to see this message in the message management interface."
            )
            
            # Add status if field exists
            if hasattr(test_inquiry, 'status'):
                test_inquiry.status = 'new'
                test_inquiry.save()
            
            print(f"✅ Created test inquiry with ID: {test_inquiry.id}")
        except Exception as e:
            print(f"❌ Failed to create test inquiry: {e}")
    else:
        print("✅ Test data already exists")
    
    # Step 5: Summary and next steps
    print("\n5. SUMMARY")
    print("-" * 40)
    
    final_admin_count = User.objects.filter(
        models.Q(is_staff=True) | 
        models.Q(groups__name='Data Analysts')
    ).distinct().count()
    
    print(f"✅ Admin users with message access: {final_admin_count}")
    print(f"✅ Total inquiries available: {Inquiry.objects.count()}")
    print(f"✅ Data Analysts group exists: {Group.objects.filter(name='Data Analysts').exists()}")
    
    print("\nNext steps:")
    print("1. Log in to the frontend with your admin account")
    print("2. Visit: http://localhost:8000/message/")
    print("3. Or use debug page: http://localhost:8000/contact-us/debug/")
    print("4. Click 'Test Messages API' to verify access")
    
    print("\n" + "=" * 60)
    print("Permission fix completed!")
    print("=" * 60)
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()


