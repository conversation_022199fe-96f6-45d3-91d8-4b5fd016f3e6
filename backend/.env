# =============================================================================
# 邮件服务选择
# =============================================================================
# 可选值: console, resend
# console: 开发模式，邮件打印到控制台
# resend: Resend邮件服务
EMAIL_SERVICE=resend
# =============================================================================
# Resend 配置 (当 EMAIL_SERVICE=resend 时使用)
# =============================================================================
# Resend API密钥 (从Resend控制台获取)
RESEND_API_KEY=re_fvF79Az1_A9CEUsKmeQj7ozBzrzGV9GVY
# AWS SES 区域 (推荐: us-east-1, us-west-2, eu-west-1)
AWS_SES_REGION_NAME=us-east-1
# =============================================================================
# 通用邮件配置
# =============================================================================
# 默认发送方邮箱 (必须是已验证的身份)
DEFAULT_FROM_EMAIL=<EMAIL>
# =============================================================================
# stripe密匙
# 可选值: test(测试), production(生产)
# =============================================================================
DJANGO_ENV=test
# 测试
STRIPE_PUBLISHABLE_KEY=pk_test_51Rnfu8IQfqEv1YpXsU3lgAFAITHdcdvQYW3ubWOmizXXCMEkkYiscFuiYOBdBj7BSNZANCqIt8ci9PYGIvHQ888x00VKhjm0xj
STRIPE_SECRET_KEY=sk_test_51Rnfu8IQfqEv1YpXJ9qStwxkdhb0V8MVNULDh5QVzwZkxz6DdGxy6LNmigtudPpsUNpuAvezqgGxbDKvwazJWrdG00GzCI6Icc
# 生产
STRIPE_LIVE_PUBLISHABLE_KEY=pk_test_51Rnfu8IQfqEv1YpXsU3lgAFAITHdcdvQYW3ubWOmizXXCMEkkYiscFuiYOBdBj7BSNZANCqIt8ci9PYGIvHQ888x00VKhjm0xj
STRIPE_LIVE_SECRET_KEY=sk_test_51Rnfu8IQfqEv1YpXJ9qStwxkdhb0V8MVNULDh5QVzwZkxz6DdGxy6LNmigtudPpsUNpuAvezqgGxbDKvwazJWrdG00GzCI6Icc

