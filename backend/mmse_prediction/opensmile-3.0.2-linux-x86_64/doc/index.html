

<!DOCTYPE html>
<!--[if IE 8]><html class="no-js lt-ie9" lang="en" > <![endif]-->
<!--[if gt IE 8]><!--> <html class="no-js" lang="en" > <!--<![endif]-->
<head>
  <meta charset="utf-8">
  
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  
  <title>openSMILE &mdash; openSMILE Documentation</title>
  

  
  
    <link rel="shortcut icon" href="_static/favicon.png"/>
  
  
  

  

  
  
    

  
  <link rel="stylesheet" href="_static/pygments.css" type="text/css" />
  <link rel="stylesheet" href="_static/css/theme.css" type="text/css" />
  <link rel="stylesheet" href="_static/css/audeering.css" type="text/css" />
    <link rel="author" title="About these documents" href="about.html" />
    <link rel="index" title="Index" href="genindex.html" />
    <link rel="search" title="Search" href="search.html" />
    <link rel="next" title="About openSMILE" href="about.html" />
    
  

  
  <script src="_static/js/modernizr.min.js"></script>

</head>

<body class="wy-body-for-nav">

   
  <div class="wy-grid-for-nav">

    
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search">
          

          <a href="#">
          
            
            <img src="_static/openSMILE-logoSlogan-white.svg" class="logo" alt="Logo"/>
          
          
            
          
          </a>

          
            
            
              <div class="version">
                3.0.2
              </div>
            
          

          
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>

          
        </div>

        <div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="main navigation">
          
            
            
              
            
            
              <ul>
<li class="toctree-l1"><a class="reference internal" href="about.html">About openSMILE</a></li>
<li class="toctree-l1"><a class="reference internal" href="get-started.html">Get started</a></li>
<li class="toctree-l1"><a class="reference internal" href="reference.html">Reference section</a></li>
<li class="toctree-l1"><a class="reference internal" href="developer.html">Developer’s documentation</a></li>
<li class="toctree-l1"><a class="reference internal" href="acknowledgement.html">Acknowledgement</a></li>
<li class="toctree-l1"><a class="reference internal" href="bibliography.html">References</a></li>
</ul>

            
          
        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap">

      
      <nav class="wy-nav-top" aria-label="top navigation">
        
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="#">openSMILE</a>
        
      </nav>


      <div class="wy-nav-content">
        
        <div class="rst-content">
        
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="#" class="icon icon-home"></a> &raquo;</li>
      <li>openSMILE</li>
      <li class="wy-breadcrumbs-aside">
              <a href="https://github.com/audeering/opensmile/" class="fa fa-github"> GitHub</a>
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
            
  <div class="section" id="opensmile">
<h1>openSMILE<a class="headerlink" href="#opensmile" title="Permalink to this heading">¶</a></h1>
<p>Welcome to the documentation of openSMILE (<strong>open</strong>-<strong>S</strong>ource <strong>M</strong>edia
<strong>I</strong>nterpretation by <strong>L</strong>arge feature-space <strong>E</strong>xtraction).</p>
<p>openSMILE allows you to extract audio and video features for signal
processing and machine learning, with an emphasis on features enabling
emotion recognition from speech.</p>
<p>The official openSMILE homepage can be found at:
<a class="reference external" href="https://opensmile.audeering.com/">https://opensmile.audeering.com/</a>.</p>
<p>To obtain binaries, source code and report issues, visit the
openSMILE page on GitHub:
<a class="reference external" href="https://github.com/audeering/opensmile">https://github.com/audeering/opensmile</a></p>
<div class="line-block">
<div class="line">Original authors: Florian Eyben, Felix Weninger, Martin Wöllmer,
Björn Schuller</div>
<div class="line">E-mails: fe, fw, mw, bs at audeering.com</div>
</div>
<a class="reference internal image-reference" href="_images/logo-audeering.png"><img alt="_images/logo-audeering.png" src="_images/logo-audeering.png" style="width: 150px;" /></a>
<div class="line-block">
<div class="line">audEERING GmbH</div>
<div class="line">D-82205 Gilching, Germany</div>
<div class="line"><a class="reference external" href="http://audeering.com/">http://audeering.com/</a></div>
<div class="line"><br /></div>
</div>
<hr class="docutils" />
<p class="rubric">Contents</p>
<div class="toctree-wrapper compound">
<ul>
<li class="toctree-l1"><a class="reference internal" href="about.html">About openSMILE</a><ul>
<li class="toctree-l2"><a class="reference internal" href="about.html#what-is-opensmile">What is openSMILE?</a></li>
<li class="toctree-l2"><a class="reference internal" href="about.html#who-needs-opensmile">Who needs openSMILE?</a></li>
<li class="toctree-l2"><a class="reference internal" href="about.html#capabilities">Capabilities</a></li>
<li class="toctree-l2"><a class="reference internal" href="about.html#history-and-changelog">History and Changelog</a></li>
<li class="toctree-l2"><a class="reference internal" href="about.html#licensing">Licensing</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="get-started.html">Get started</a><ul>
<li class="toctree-l2"><a class="reference internal" href="get-started.html#obtaining-and-installing-opensmile">Obtaining and Installing openSMILE</a></li>
<li class="toctree-l2"><a class="reference internal" href="get-started.html#compiling-from-source">Compiling from source</a></li>
<li class="toctree-l2"><a class="reference internal" href="get-started.html#extracting-your-first-features">Extracting your first features</a></li>
<li class="toctree-l2"><a class="reference internal" href="get-started.html#default-feature-sets">Default feature sets</a></li>
<li class="toctree-l2"><a class="reference internal" href="get-started.html#using-portaudio-for-live-recording-playback">Using PortAudio for live recording/playback</a></li>
<li class="toctree-l2"><a class="reference internal" href="get-started.html#extracting-features-with-opencv">Extracting features with OpenCV</a></li>
<li class="toctree-l2"><a class="reference internal" href="get-started.html#visualising-data-with-gnuplot">Visualising data with Gnuplot</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="reference.html">Reference section</a><ul>
<li class="toctree-l2"><a class="reference internal" href="reference.html#smilextract-command-line-options">SMILExtract command-line options</a></li>
<li class="toctree-l2"><a class="reference internal" href="reference.html#opensmile-architecture">openSMILE architecture</a></li>
<li class="toctree-l2"><a class="reference internal" href="reference.html#configuration-files">Configuration files</a></li>
<li class="toctree-l2"><a class="reference internal" href="reference.html#components">Components</a></li>
<li class="toctree-l2"><a class="reference internal" href="reference.html#smileapi-c-api-and-wrappers">SMILEapi C API and wrappers</a></li>
<li class="toctree-l2"><a class="reference internal" href="reference.html#feature-names">Feature names</a></li>
<li class="toctree-l2"><a class="reference internal" href="reference.html#feature-extraction-algorithms">Feature extraction algorithms</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="developer.html">Developer’s documentation</a><ul>
<li class="toctree-l2"><a class="reference internal" href="developer.html#writing-components">Writing components</a></li>
<li class="toctree-l2"><a class="reference internal" href="developer.html#writing-plugins">Writing plugins</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="acknowledgement.html">Acknowledgement</a></li>
<li class="toctree-l1"><a class="reference internal" href="bibliography.html">References</a></li>
</ul>
</div>
</div>


           </div>
           
          </div>
          <footer>
  
    <div class="rst-footer-buttons" role="navigation" aria-label="footer navigation">
      
        <a href="about.html" class="btn btn-neutral float-right" title="About openSMILE" accesskey="n" rel="next">Next <span class="fa fa-arrow-circle-right"></span></a>
      
      
    </div>
  

  <hr/>

  <div>
    <p>
        
        
        
          Built with <a href="https://www.sphinx-doc.org/en/master/">Sphinx</a> on 2023/10/19 using the <a href="https://github.com/audeering/sphinx-audeering-theme/">audEERING theme</a>
        
    </p>
  </div>

  <div role="contentinfo">
    <p>
        
      &copy; 2013-2023 audEERING GmbH and 2008-2013 TU München, MMK
    </p>
  </div> 

</footer>
        </div>
      </div>

    </section>

  </div>
  



  
  <!--[if lt IE 9]>
    <script src="_static/js/html5shiv.min.js"></script>
  <![endif]-->
  

    
    
      <script type="text/javascript" id="documentation_options" data-url_root="./" src="_static/documentation_options.js"></script>
        <script data-url_root="./" id="documentation_options" src="_static/documentation_options.js"></script>
        <script src="_static/jquery.js"></script>
        <script src="_static/underscore.js"></script>
        <script src="_static/_sphinx_javascript_frameworks_compat.js"></script>
        <script src="_static/doctools.js"></script>
        <script src="_static/sphinx_highlight.js"></script>
    

  

  <script type="text/javascript" src="_static/js/theme.js"></script>

  <script type="text/javascript">
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>