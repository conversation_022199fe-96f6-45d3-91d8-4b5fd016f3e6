Search.setIndex({"docnames": ["about", "acknowledgement", "bibliography", "developer", "get-started", "index", "reference"], "filenames": ["about.rst", "acknowledgement.rst", "bibliography.rst", "developer.rst", "get-started.rst", "index.rst", "reference.rst"], "titles": ["About openSMILE", "Acknowledgement", "References", "Developer\u2019s documentation", "Get started", "openSMILE", "Reference section"], "terms": {"we": [0, 4, 6], "start": [0, 5, 6], "introduc": [0, 2, 6], "address": 0, "two": [0, 4, 6], "import": [0, 3, 4, 6], "question": 0, "user": [0, 6], "ar": [0, 3, 4, 6], "new": [0, 3, 4, 6], "If": [0, 4, 6], "you": [0, 3, 4, 5, 6], "want": [0, 3, 4, 6], "us": [0, 3, 5, 6], "right": [0, 4, 6], "awai": 0, "should": [0, 4, 6], "read": [0, 3, 4, 6], "section": [0, 4, 5], "get": [0, 5, 6], "extract": [0, 5], "your": [0, 3, 5, 6], "first": [0, 5, 6], "have": [0, 3, 4, 6], "alreadi": [0, 4, 6], "instal": [0, 5, 6], "The": [0, 1, 2, 3, 5], "munich": [0, 2], "open": [0, 2, 4, 5, 6], "sourc": [0, 2, 3, 5, 6], "media": 0, "interpret": [0, 6], "larg": [0, 6], "space": [0, 5, 6], "toolkit": [0, 2], "modular": [0, 4], "flexibl": [0, 3], "extractor": [0, 4], "machin": [0, 4, 5, 6], "learn": [0, 4, 5, 6], "applic": [0, 4, 6], "primari": 0, "focu": 0, "clearli": 0, "put": [0, 3], "howev": [0, 4, 6], "due": [0, 6], "high": [0, 6], "degre": 0, "abstract": [0, 6], "can": [0, 3, 4, 5, 6], "also": [0, 3, 4, 6], "analys": [0, 4], "from": [0, 1, 3, 5, 6], "modal": 0, "physiolog": 0, "visual": [0, 6], "physic": 0, "sensor": 0, "given": [0, 4, 6], "suitabl": [0, 4, 6], "It": [0, 4, 6], "written": [0, 3, 4, 6], "pure": [0, 4], "c": [0, 5], "ha": [0, 1, 3, 4, 6], "fast": [0, 2], "effici": [0, 3, 4, 6], "architectur": [0, 4, 5], "run": [0, 3, 4, 6], "common": [0, 3], "platform": [0, 4], "linux": 0, "window": [0, 3, 6], "maco": [0, 4], "android": [0, 6], "io": [0, 6], "design": 0, "real": [0, 3, 4, 6], "time": [0, 3, 4, 6], "onlin": [0, 4], "off": [0, 4, 6], "line": [0, 4, 5], "batch": [0, 4], "mode": [0, 4, 6], "set": [0, 5, 6], "thi": [0, 3, 4, 6], "rare": 0, "found": [0, 4, 5, 6], "tool": [0, 4, 6], "most": [0, 3, 4, 6], "relat": [0, 4], "project": 0, "requir": [0, 3, 4, 6], "whole": 0, "present": [0, 4, 6], "increment": [0, 4], "arriv": [0, 6], "By": [0, 4, 6], "portaudio": [0, 5], "1": [0, 4, 6], "librari": [0, 6], "support": [0, 3], "independ": [0, 6], "live": [0, 5], "playback": [0, 5], "enabl": [0, 4, 5], "To": [0, 4, 5, 6], "facilit": 0, "interoper": 0, "write": [0, 4, 5, 6], "variou": [0, 3, 4, 6], "format": [0, 3, 4, 6], "commonli": 0, "field": [0, 4, 6], "mine": 0, "These": [0, 3, 4, 6], "includ": [0, 3, 4], "pcm": [0, 4, 6], "wave": [0, 3, 4, 6], "file": [0, 3, 5], "csv": [0, 4, 6], "comma": [0, 4, 6], "separ": [0, 4, 6], "valu": [0, 3, 4, 6], "spreadsheet": 0, "arff": [0, 3, 4, 6], "weka": [0, 4], "text": [0, 4, 6], "base": [0, 3, 6], "htk": [0, 3, 4], "hidden": 0, "markov": 0, "paramet": [0, 4, 6], "simpl": [0, 4, 6], "binari": [0, 4, 5, 6], "float": [0, 4], "matrix": [0, 6], "come": [0, 6], "slim": 0, "power": [0, 4, 6], "api": [0, 4, 5], "allow": [0, 3, 4, 5, 6], "seamless": [0, 6], "integr": [0, 6], "wrapper": [0, 4, 5], "avail": [0, 4, 6], "python": [0, 6], "net": 0, "provid": [0, 3, 4, 6], "programmat": [0, 6], "access": [0, 3, 6], "all": [0, 3, 6], "command": [0, 4, 5], "pass": [0, 4, 6], "softwar": 0, "gnuplot": [0, 5], "2": [0, 4, 6], "which": [0, 3, 4, 6], "dump": [0, 4], "visualis": [0, 3, 5], "A": [0, 3, 4, 6], "strength": 0, "its": [0, 4, 6], "highli": [0, 6], "almost": [0, 4, 6], "intermedi": 0, "gener": [0, 3, 4, 6], "dure": [0, 6], "spectra": 0, "etc": [0, 3, 4, 6], "save": [0, 4, 6], "further": [0, 4, 6], "intend": [0, 4, 6], "primarili": 0, "research": [0, 4, 6], "demonstr": [0, 6], "prototyp": 0, "thu": [0, 3, 4, 6], "target": [0, 4], "group": [0, 4, 6], "system": [0, 4, 6], "develop": [0, 1, 5, 6], "compact": 0, "code": [0, 4, 5, 6], "final": [0, 4, 6], "product": 0, "feasibl": 0, "well": [0, 4, 6], "would": [0, 4, 6], "like": [0, 3, 4, 6], "stress": 0, "distribut": [0, 4, 6], "under": [0, 1, 4, 6], "onli": [0, 4, 6], "see": [0, 3, 4, 6], "next": [0, 4, 6], "detail": [0, 4, 6], "current": [0, 4, 6], "around": [0, 6], "world": 0, "compani": 0, "work": [0, 4, 6], "speech": [0, 4, 5, 6], "recognit": [0, 2, 5], "front": [0, 4, 6], "end": [0, 4, 6], "keyword": 0, "spot": 0, "area": 0, "affect": [0, 2, 4, 6], "comput": [0, 2, 4, 6], "emot": [0, 2, 5], "sensit": 0, "virtual": [0, 6], "agent": 0, "music": [0, 4, 6], "inform": [0, 4, 6], "retriev": [0, 4, 6], "chord": [0, 4], "beat": 0, "track": [0, 4], "onset": [0, 4], "detect": [0, 6], "sinc": [0, 3, 4, 6], "0": [0, 4, 6], "releas": 0, "wider": 0, "multi": [0, 6], "commun": [0, 1, 4, 6], "popular": [0, 4], "opencv": [0, 5], "give": [0, 4], "brief": [0, 6], "summari": [0, 3, 4, 6], "": [0, 1, 2, 4, 5, 6], "distinguish": 0, "follow": [0, 3, 4, 6], "categori": 0, "nativ": [0, 4], "riff": 0, "uncompress": [0, 4], "built": [0, 3, 6], "addit": [0, 3, 4, 6], "option": [0, 5], "depend": [0, 3, 4, 6], "ani": [0, 4, 6], "ffmpeg": [0, 4], "stream": [0, 4], "via": [0, 3, 4, 6], "In": [0, 2, 3, 4, 6], "when": [0, 4, 6], "interfac": [0, 3, 4, 6], "smileapi": [0, 5], "raw": [0, 6], "sampl": [0, 3, 6], "For": [0, 3, 4, 6], "compat": [0, 4], "offici": [0, 4, 5], "config": [0, 4, 6], "cwavesourc": [0, 4, 6], "default": [0, 5, 6], "mean": [0, 4, 6], "store": [0, 6], "compress": [0, 4], "mp3": [0, 3], "mp4": 0, "ogg": 0, "convert": 0, "befor": [0, 4], "directli": [0, 4, 6], "build": [0, 3, 6], "yourself": [0, 4], "replac": [0, 4, 6], "cffmpegsourc": 0, "record": [0, 5], "pc": 0, "sound": 0, "card": 0, "white": 0, "nois": 0, "sinusoid": 0, "tone": [0, 4], "constant": 0, "pre": [0, 4, 6], "prior": [0, 6], "rectangular": 0, "ham": [0, 4], "hann": 0, "rais": [0, 4], "cosin": 0, "gauss": [0, 4], "sine": 0, "triangular": [0, 4], "bartlett": 0, "blackmann": 0, "harri": 0, "lanczo": 0, "de": [0, 6], "emphasi": [0, 4, 5], "e": [0, 3, 4, 5, 6], "1st": [0, 4], "order": [0, 3, 4, 6], "resampl": 0, "spectral": [0, 3, 4, 6], "domain": [0, 3], "algorithm": [0, 3, 4, 5], "fft": [0, 3, 4], "magnitud": [0, 4], "phase": [0, 4, 6], "complex": [0, 4], "invers": 0, "scale": [0, 4], "axi": [0, 4], "spline": 0, "interpol": 0, "dba": 0, "weight": [0, 4], "spectrum": [0, 4], "autocorrel": [0, 4], "acf": [0, 4], "ifft": 0, "averag": [0, 4], "differ": [0, 3, 4, 6], "amdf": 0, "perform": [0, 4, 6], "number": [0, 4, 6], "oper": [0, 4, 6], "normalis": [0, 4, 6], "modif": 0, "differenti": [0, 4], "varianc": 0, "rang": [0, 4], "delta": [0, 4, 6], "regress": [0, 4, 6], "coeffici": [0, 4, 6], "ser07": [0, 2], "vector": [0, 3, 4, 6], "length": [0, 4, 6], "element": [0, 6], "wise": [0, 4, 6], "multipl": [0, 3, 4, 6], "logarithm": [0, 4], "move": [0, 4], "filter": [0, 3, 4], "smooth": [0, 4, 6], "contour": [0, 4, 6], "over": [0, 3, 4, 6], "specif": [0, 3, 6], "descriptor": [0, 4], "frame": [0, 3, 4, 6], "energi": [0, 4, 6], "intens": [0, 4], "loud": [0, 4], "approxim": [0, 4], "critic": 0, "band": [0, 4], "mel": [0, 4], "bark": 0, "octav": 0, "mask": 0, "frequenc": [0, 4], "cepstral": [0, 4], "mfcc": [0, 3, 6], "auditori": [0, 4], "perceptu": 0, "linear": [0, 4], "predict": 0, "plp": 0, "cc": [0, 4], "lpc": [0, 4], "pair": [0, 4, 6], "lsp": 0, "aka": 0, "lsf": [0, 4], "fundament": [0, 4], "cepstrum": [0, 4], "method": [0, 3, 4, 6], "subharmon": 0, "summat": 0, "sh": [0, 4], "probabl": [0, 4], "voic": [0, 4], "peak": 0, "qualiti": 0, "jitter": [0, 4], "shimmer": [0, 4], "formant": 0, "bandwidth": 0, "zero": [0, 4, 6], "cross": [0, 4], "rate": [0, 4, 6], "arbitrari": [0, 4, 6], "roll": 0, "point": [0, 3, 4, 6], "centroid": 0, "entropi": 0, "maxpo": [0, 4], "minpo": [0, 4], "spread": 0, "skew": [0, 4], "kurtosi": [0, 4], "slope": [0, 4], "psychoacoust": 0, "sharp": 0, "harmon": [0, 4], "chroma": [0, 3], "warp": [0, 6], "semiton": [0, 4], "cen": 0, "deriv": 0, "kei": [0, 4, 6], "f0": [0, 4, 6], "ratio": 0, "hsv": [0, 4], "colour": [0, 4], "histogram": [0, 4], "local": [0, 4], "pattern": [0, 4], "lbp": [0, 4], "optic": [0, 4], "flow": [0, 4, 6], "face": [0, 4], "an": [0, 3, 5, 6], "automat": [0, 2, 3, 4, 6], "facial": [0, 4], "region": [0, 4], "full": [0, 4, 6], "imag": [0, 4], "map": 0, "onto": 0, "fix": [0, 4, 6], "dimension": [0, 6], "appli": [0, 4, 6], "extrem": [0, 6], "posit": [0, 4, 6], "arithmet": [0, 4], "quadrat": [0, 4], "geometr": 0, "moment": [0, 4], "standard": [0, 6], "deviat": [0, 4], "percentil": [0, 4], "error": [0, 4, 6], "segment": [0, 4], "durat": [0, 4], "offset": [0, 4], "discret": 0, "transform": [0, 6], "dct": 0, "gain": 0, "task": [0, 3, 4], "often": 0, "activ": 0, "turn": [0, 4], "detector": [0, 4], "purpos": [0, 3, 4, 6], "classif": [0, 6], "implement": [0, 3, 4, 6], "libsvm": 0, "fuzzi": 0, "logic": [0, 4, 6], "lstm": 0, "rnn": 0, "train": [0, 4], "model": [0, 4], "svm": 0, "sink": [0, 3, 4, 6], "load": [0, 6], "smo": 0, "kernel": 0, "gmm": 0, "experiment": [0, 4, 6], "enterfac": 0, "12": [0, 4], "soon": [0, 4], "neural": 0, "network": [0, 3], "rnnlib": 0, "currennt": 0, "openear": [0, 1, 2], "type": [0, 4, 6], "receiv": [0, 1, 4], "messag": [0, 4, 6], "addition": 0, "besid": [0, 6], "few": [0, 4], "avoid": [0, 4, 6], "confus": 0, "do": [0, 4, 6], "term": [0, 6], "here": [0, 4, 6], "thread": [0, 4, 6], "parallel": [0, 4], "make": [0, 3, 4, 6], "cpu": 0, "core": [0, 3, 4], "speed": 0, "up": [0, 4, 6], "where": [0, 4, 6], "version": [0, 4, 6], "3": [0, 4, 6], "lower": 0, "ignor": [0, 4, 6], "alwai": [0, 4, 6], "singl": [0, 3, 4, 6], "plugin": [0, 4, 5, 6], "share": [0, 4, 6], "dll": [0, 4, 6], "link": 0, "against": 0, "Such": [0, 4], "initialis": [0, 6], "program": [0, 4, 6], "thei": [0, 4, 6], "place": [0, 4, 6], "subdirectori": 0, "extens": [0, 4, 6], "log": [0, 4, 6], "print": [0, 4, 6], "control": [0, 4, 6], "easier": [0, 6], "msg": [0, 4, 6], "warn": 0, "wrn": [0, 6], "err": [0, 6], "debug": [0, 6], "dbg": [0, 4, 6], "configur": [0, 3, 5], "fulli": 0, "one": [0, 3, 4, 6], "kept": 0, "yet": [0, 3, 4, 6], "veri": [0, 3, 4, 6], "ini": [0, 6], "style": [0, 2, 4, 6], "each": [0, 3, 4, 6], "own": [0, 4], "connect": [0, 4, 6], "central": [0, 6], "memori": [0, 3, 4, 6], "even": [0, 4, 6], "defin": [0, 4], "custom": [0, 4, 6], "g": [0, 3, 4, 6], "inclus": 0, "reusabl": 0, "block": [0, 3, 6], "name": [0, 4, 5], "specifi": [0, 3, 4, 6], "maximum": [0, 4], "script": [0, 3, 4], "strict": [0, 6], "guidelin": 0, "meet": 0, "sequenc": [0, 4, 6], "seek": 0, "back": [0, 6], "forth": 0, "within": [0, 4, 6], "exampl": [0, 3, 4, 6], "princip": [0, 6], "must": [0, 4, 6], "abl": [0, 4, 6], "least": [0, 3, 4, 6], "possibl": [0, 3, 4, 6], "some": [0, 3, 4, 6], "except": [0, 4, 6], "rule": [0, 4], "been": [0, 3, 4, 6], "grant": [0, 1], "certain": [0, 3], "obvious": 0, "buffer": [0, 4, 6], "higher": [0, 4, 6], "exist": [0, 3, 4, 6], "http": [0, 4, 5, 6], "www": [0, 4, 6], "com": [0, 4, 5, 6], "info": [0, 4], "wa": [0, 4, 6], "origin": [0, 5], "creat": [0, 3, 6], "scope": 0, "european": [0, 1], "eu": 0, "fp7": [0, 1], "semain": [0, 1], "acoust": [0, 4, 6], "engin": 0, "spotter": 0, "dialogu": 0, "serv": [0, 4, 6], "were": [0, 4, 6], "made": [0, 4], "main": [0, 3, 4, 6], "publicli": 0, "contain": [0, 3, 4, 6], "intellig": [0, 2], "interact": [0, 2, 6], "acii": [0, 2], "confer": [0, 2], "2009": [0, 2], "One": 0, "year": 0, "later": [0, 4, 6], "publish": [0, 4, 6], "aim": 0, "reach": [0, 6], "analysi": [0, 4, 6], "acm": 0, "multimedia": 0, "2010": 0, "challeng": [0, 2], "small": [0, 4], "bugfix": 0, "shortli": 0, "afterward": 0, "taken": [0, 4], "subvers": 0, "repositori": [0, 4], "sourceforg": 0, "2011": 0, "continu": [0, 6], "privat": 0, "issu": [0, 4, 5], "intern": [0, 2, 4, 6], "third": [0, 4], "parti": [0, 4], "rc1": 0, "major": 0, "after": [0, 4, 6], "revis": 0, "long": [0, 4, 6], "list": [0, 3, 4, 6], "improv": 0, "old": [0, 4], "extend": [0, 3], "document": [0, 4, 5, 6], "restructur": 0, "tree": 0, "synchronis": [0, 4], "backward": 0, "interspeech": [0, 2], "json": [0, 6], "minor": 0, "audeer": [0, 4, 5], "geneva": 0, "minimalist": 0, "gemap": [0, 6], "jni": [0, 4], "updat": [0, 4], "gui": [0, 3, 4, 6], "compar": [0, 6], "2013": [0, 1, 6], "2015": 0, "baselin": [0, 4], "sever": [0, 4, 6], "notabl": 0, "standalon": [0, 6], "chang": [0, 3, 4, 6], "rewritten": 0, "cmake": [0, 3, 4, 6], "usag": [0, 4, 6], "html": 0, "numer": [0, 4, 6], "refactor": 0, "begin": [0, 4, 6], "host": [0, 4], "github": [0, 4, 5], "dual": 0, "goal": 0, "widespread": 0, "freeli": [0, 4, 6], "educ": 0, "sort": 0, "commerci": 0, "permit": 0, "result": [0, 4], "bui": 0, "contact": 0, "u": 0, "visit": [0, 5], "more": [0, 3, 4, 6], "opensmil": [1, 3], "fund": 1, "seventh": 1, "framework": 1, "programm": 1, "2007": [1, 2], "agreement": 1, "No": [1, 4], "211486": 1, "ewollmers09": [2, 4], "florian": [2, 4, 5], "eyben": [2, 4, 5, 6], "martin": [2, 5], "w": 2, "\u00f6": 2, "llmer": 2, "bj": 2, "rn": 2, "schuller": [2, 4, 5], "proceed": [2, 4], "4th": [2, 4], "humain": 2, "associ": [2, 6], "volum": 2, "i": [2, 3, 4, 5, 6], "576": 2, "581": 2, "ieee": 2, "gerhard": [2, 4], "rigol": [2, 4], "robust": [2, 4], "meter": 2, "tempo": 2, "discrimin": 2, "ballroom": 2, "danc": 2, "icassp": 2, "honolulu": 2, "hawaii": 2, "ssb09": [2, 4], "steidl": [2, 4], "anton": [2, 4], "batlin": [2, 4], "isca": [2, 4], "brighton": 2, "uk": 2, "src": [3, 4], "directori": [3, 4, 6], "mai": [3, 4, 6], "help": [3, 4, 6], "comment": [3, 4], "cmysmilecompon": 3, "typic": [3, 6], "need": [3, 4, 5, 6], "appropri": 3, "subfold": 3, "dsp": 3, "mysmilecompon": 3, "cpp": [3, 6], "correspond": [3, 4, 6], "header": [3, 4, 6], "sub": [3, 4, 6], "folder": [3, 4], "hpp": [3, 4], "componentlist": 3, "top": [3, 4], "registercompon": 3, "class": [3, 6], "variabl": [3, 4], "add": [3, 4, 6], "path": [3, 4, 6], "opensmile_sourc": 3, "cmakelist": 3, "txt": 3, "special": 3, "extern": [3, 4, 6], "perl": 3, "clonecomp": 3, "pl": 3, "copi": [3, 4, 6], "definit": [3, 6], "inputcompbas": 3, "yourcompbas": 3, "inputcompnam": 3, "yourcompnam": 3, "complet": [3, 4, 6], "smilextract": [3, 4, 5], "l": [3, 4, 5, 6], "descend": [3, 6], "csmilecompon": [3, 6], "encapsul": 3, "function": [3, 4, 6], "data": [3, 5, 6], "manag": [3, 4], "finalis": [3, 6], "process": [3, 4, 5], "tick": [3, 6], "speak": [3, 4], "three": [3, 4, 6], "cdatasourc": 3, "examplesourc": 3, "writer": [3, 4, 6], "cdatawrit": [3, 6], "respons": [3, 6], "exactli": [3, 6], "level": [3, 4, 6], "cdataprocessor": [3, 6], "descript": [3, 6], "below": [3, 4], "input": [3, 4, 6], "featur": [3, 5], "processor": [3, 6], "exampleprocessor": 3, "both": [3, 4, 6], "reader": [3, 4, 6], "cdataread": [3, 6], "note": [3, 4, 6], "exclus": 3, "inherit": 3, "pleas": [3, 4, 6], "kind": [3, 4], "cdatasink": 3, "examplesink": 3, "output": [3, 4, 6], "send": [3, 6], "cvectorprocessor": 3, "easi": [3, 6], "mostli": 3, "frames": [3, 4, 6], "signal": [3, 4, 5, 6], "examplevectorprocessor": 3, "cwindowprocessor": 3, "overlap": [3, 4, 6], "past": [3, 6], "futur": [3, 4], "offer": 3, "preemphasi": 3, "cwintovecprocessor": 3, "take": [3, 4, 6], "produc": [3, 4, 6], "framestep": [3, 4, 6], "form": [3, 4], "statist": [3, 4, 6], "framer": [3, 4], "runtim": [3, 6], "adjust": [3, 4, 6], "plugindev": 3, "pluginmain": 3, "individu": [3, 4, 6], "similar": 3, "componentmanag": [3, 4], "now": [4, 6], "describ": [4, 6], "how": [4, 6], "explain": 4, "skip": 4, "life": 4, "bit": 4, "simpler": 4, "teach": 4, "compon": [4, 5], "player": 4, "outlin": 4, "plot": 4, "latest": 4, "previou": [4, 6], "intel": 4, "x64": 4, "modifi": [4, 6], "desir": 4, "older": 4, "archiv": 4, "recommend": [4, 6], "wai": [4, 6], "unix": 4, "mandatori": [4, 6], "x86": 4, "arm": 4, "packag": [4, 6], "execut": [4, 6], "ex": 4, "bin": 4, "other": 4, "test": 4, "h": [4, 6], "shell": 4, "prompt": 4, "everyth": [4, 6], "libopensmil": 4, "libc6": 4, "pthread": 4, "downsid": 4, "cannot": [4, 6], "As": [4, 6], "without": [4, 6], "than": [4, 6], "opensl": 4, "coreaudio": 4, "step": [4, 6], "git": 4, "gnu": 4, "gcc": 4, "sure": [4, 6], "recent": 4, "4": [4, 6], "8": [4, 6], "known": 4, "incompat": 4, "instead": [4, 6], "clang": 4, "11": 4, "5": [4, 6], "perl5": 4, "root": 4, "privileg": 4, "ubuntu": 4, "debian": 4, "easili": [4, 6], "sudo": 4, "apt": 4, "clone": 4, "altern": 4, "download": 4, "unpack": 4, "zip": 4, "master": 4, "Then": 4, "newli": 4, "cd": 4, "build_flag": 4, "switch": 4, "between": [4, 6], "bash": 4, "abov": [4, 6], "doesn": 4, "t": [4, 6], "success": 4, "progsrc": [4, 6], "search": 4, "show": [4, 6], "19": 4, "re": 4, "through": [4, 6], "portaudio19": 4, "dev": 4, "might": [4, 6], "case": [4, 6], "instruct": 4, "prerequisit": 4, "just": 4, "libopencv": 4, "care": 4, "otherwis": [4, 6], "decod": 4, "don": [4, 6], "forget": 4, "ld": 4, "so": [4, 6], "conf": [4, 6], "usr": 4, "lib": 4, "successfulli": [4, 6], "with_opencv": 4, "ON": [4, 6], "verifi": 4, "copencvsourc": 4, "appear": [4, 6], "export": [4, 6], "ld_library_path": 4, "microsoft": 4, "7": [4, 6], "studio": [4, 6], "2019": 4, "15": 4, "environ": 4, "menu": 4, "rightarrow": 4, "v": 4, "ps1": 4, "powershel": 4, "executionpolici": 4, "bypass": 4, "vcpkg": 4, "check": [4, 6], "out": [4, 6], "bootstrap": 4, "bat": 4, "admin": 4, "triplet": 4, "choos": 4, "whether": [4, 6], "uncom": 4, "cmake_toolchain_fil": 4, "usual": 4, "23": 4, "juli": 4, "2018": 4, "bug": 4, "lead": [4, 6], "being": 4, "attempt": 4, "fail": [4, 6], "workaround": [4, 6], "exclud": 4, "mani": 4, "view": [4, 6], "On": [4, 6], "debugg": 4, "cmake_build_typ": 4, "d": [4, 5, 6], "advantag": [4, 6], "ship": 4, "portabl": 4, "still": 4, "though": 4, "static_link": 4, "anoth": [4, 6], "locat": [4, 6], "destin": 4, "dir": 4, "everi": [4, 6], "reboot": 4, "call": [4, 6], "profil": 4, "bashrc": 4, "home": 4, "suffici": 4, "keep": 4, "same": [4, 6], "chapter": 4, "templat": [4, 6], "swig": 4, "gradlew": 4, "aar": 4, "gradl": 4, "assembl": 4, "p": 4, "ad": [4, 6], "flatdir": 4, "ext": 4, "assembleusesourc": 4, "regard": 4, "properti": 4, "minimum": [4, 6], "univers": 4, "buildiosuniversallib": 4, "build_io": 4, "preprocessor": 4, "macro": 4, "__ios__": 4, "With": 4, "m": [4, 5], "mm": 4, "configmanag": 4, "coreaudiosourc": 4, "usingsmilelib": 4, "smileio": 4, "There": [4, 6], "them": [4, 6], "approach": [4, 6], "either": [4, 6], "readi": 4, "correctli": [4, 6], "cportaudio": 4, "o": 4, "copencv": 4, "prefix": 4, "quick": 4, "demo": 4, "demo1_energi": 4, "wav": [4, 6], "finish": [4, 6], "last": [4, 6], "ran": [4, 6], "someth": [4, 6], "editor": 4, "graphic": 4, "discuss": 4, "ourselv": 4, "scenario": 4, "our": 4, "capabl": [4, 5], "myconfig": 4, "navig": [4, 6], "hold": 4, "newlin": 4, "cfgfiletempl": [4, 6], "configdflt": [4, 6], "cframer": [4, 6], "cenergi": 4, "ccsvsink": 4, "demo1": 4, "while": [4, 6], "shall": [4, 6], "part": [4, 6], "suppress": 4, "termin": 4, "cfgfiledescript": [4, 6], "demo1_descript": 4, "consist": [4, 6], "look": [4, 6], "componentinst": [4, 6], "ccomponentmanag": [4, 6], "instanc": [4, 6], "datamemori": [4, 6], "cdatamemori": [4, 6], "wavesourc": 4, "csvsink": 4, "amount": [4, 6], "displai": [4, 6], "printlevelstat": 4, "nthread": [4, 6], "determin": 4, "what": [4, 5, 6], "instanti": [4, 6], "handl": [4, 6], "uniqu": 4, "instancenam": [4, 6], "componenttyp": 4, "becaus": [4, 6], "overrid": 4, "explicitli": 4, "carefulli": 4, "those": [4, 6], "consid": [4, 6], "good": [4, 6], "practic": [4, 6], "ensur": [4, 6], "whatev": 4, "reason": 4, "moreov": [4, 6], "increas": [4, 6], "readabl": 4, "manual": 4, "done": 4, "assign": [4, 6], "dataread": 4, "datawrit": 4, "dmlevel": [4, 6], "regist": [4, 6], "therebi": [4, 6], "limit": [4, 6], "concaten": [4, 6], "column": [4, 6], "25m": [4, 6], "10m": [4, 6], "represent": [4, 6], "xxxx": 4, "remov": 4, "superflu": 4, "010000": 4, "wavefram": 4, "copyinputnam": [4, 6], "framemod": 4, "025000": 4, "framecenterspeci": 4, "left": 4, "noposteoiprocess": 4, "rm": 4, "filenam": [4, 6], "myenergi": 4, "renam": 4, "cm": [4, 6], "inputfil": 4, "outputfil": 4, "conclud": 4, "introductori": 4, "hope": 4, "understand": [4, 6], "basic": [4, 6], "among": 4, "competit": 4, "influenc": 4, "frequent": 4, "prosodi": 4, "pitch": [4, 6], "obsolet": 4, "basi": 4, "appreci": 4, "upload": 4, "person": 4, "web": 4, "page": [4, 5], "url": 4, "paper": 4, "indic": [4, 6], "reproduc": 4, "themselv": 4, "mechan": 4, "syntax": [4, 6], "putconfigfilenameher": 4, "ccmdhelp": [4, 6], "second": [4, 6], "rel": 4, "standard_wave_input": 4, "inc": 4, "behaviour": [4, 6], "framemodefunctionalsconf": 4, "framemodefunct": 4, "buffermoderbconf": 4, "buffermoderb": 4, "buffermoderblagconf": 4, "buffermoderblag": 4, "buffermodeconf": 4, "buffermod": 4, "unit": [4, 6], "content": [4, 5, 6], "illustr": [4, 6], "four": 4, "b": [4, 5, 6], "size": [4, 6], "slide": 4, "shift": 4, "forward": 4, "interv": 4, "6": [4, 6], "9": [4, 6], "10": 4, "framelist": 4, "fly": 4, "cturndetector": 4, "smile": [4, 6], "var": 4, "messagerecp": 4, "emobase_live4": 4, "lag": 4, "viterbi": 4, "summaris": 4, "classifi": [4, 6], "context": [4, 6], "latter": [4, 6], "match": [4, 6], "request": [4, 6], "grow": [4, 6], "growdyn": [4, 6], "act": 4, "ring": [4, 6], "cyclic": 4, "isrb": [4, 6], "levelconf": 4, "nt": 4, "100": 4, "relev": 4, "initi": [4, 6], "lld": 4, "occupi": 4, "infinit": [4, 6], "ram": 4, "crash": 4, "constrain": 4, "achiev": 4, "less": 4, "1000": 4, "avec": 4, "instnam": 4, "string": [4, 6], "unknown": 4, "lldcsvoutput": 4, "appendcsvlld": 4, "append": [4, 6], "overwrit": [4, 6], "timestampcsvlld": 4, "disabl": 4, "timestamp": [4, 6], "headercsvlld": 4, "lldhtkoutput": 4, "lldarffoutput": 4, "appendarfflld": 4, "timestamparfflld": 4, "lldarfftargetsfil": 4, "arff_targets_conf": 4, "appendarff": 4, "timestamparff": 4, "arfftargetsfil": 4, "csvoutput": 4, "appendcsv": 4, "timestampcsv": 4, "headercsv": 4, "htkoutput": 4, "standard_data_output": 4, "low": [4, 6], "arffoutput": 4, "standard_data_output_lldonli": 4, "opensmilebatchgui": 4, "select": [4, 6], "chroma_fft": 4, "semi": 4, "short": [4, 6], "spectrogram": 4, "50m": 4, "ascii": 4, "per": [4, 6], "ctonespec": 4, "ctonefilt": 4, "chroma_filt": 4, "recognis": 4, "song": 4, "sum": 4, "cfunction": 4, "repres": [4, 6], "mfcc12_0_d_a": 4, "13": 4, "26": 4, "lifter": 4, "22": 4, "acceler": 4, "mfcc12_e_d_a": 4, "th": 4, "mfcc12_0_d_a_z": 4, "respect": [4, 6], "mfcc12_e_d_a_z": 4, "k": 4, "97": 4, "8khz": 4, "chtksink": 4, "plp_0_d_a": 4, "predictor": 4, "plp_e_d_a": 4, "plp_0_d_a_z": 4, "plp_e_d_a_z": 4, "factor": 4, "33": 4, "prosodyacf": 4, "prosodysh": 4, "cpitchacf": 4, "cpitchsh": 4, "is09": 4, "is09_emot": 4, "384": 4, "wherebi": [4, 6], "repeatedli": 4, "16": 4, "pcm_rmsenergi": 4, "squar": 4, "pcm_zcr": 4, "voiceprob": 4, "suffix": [4, 6], "_sma": 4, "_de": 4, "max": [4, 6], "min": [4, 6], "absolut": 4, "amean": 4, "linregc1": 4, "linregc2": 4, "linregerrq": 4, "actual": [4, 6], "stddev": 4, "3rd": 4, "is10_par": 4, "1582": 4, "34": 4, "21": 4, "68": 4, "1428": 4, "152": 4, "pseudo": 4, "syllabl": 4, "total": 4, "pcm_loud": 4, "14": 4, "logmelfreqband": 4, "lspfreq": 4, "f0finenv": 4, "envelop": 4, "voicingfinalunclip": 4, "candid": 4, "unclip": 4, "fall": 4, "threshold": 4, "linregerra": 4, "quartile1": 4, "quartil": 4, "25": 4, "quartile2": 4, "50": 4, "quartile3": 4, "75": 4, "iqr1": 4, "inter": 4, "iqr2": 4, "percentile1": 4, "outlier": 4, "percentile99": 4, "99": 4, "pctlrange0": 4, "upleveltime75": 4, "percentag": 4, "upleveltime90": 4, "90": 4, "unvoic": 4, "f0final": 4, "jitterloc": 4, "period": [4, 6], "jitterddp": 4, "shimmerloc": 4, "amplitud": 4, "mention": 4, "is11_speaker_st": 4, "book": [4, 6], "meanwhil": 4, "bj\u00f6rn": [4, 5], "stefan": 4, "schiel": 4, "jarek": 4, "krajewski": 4, "proc": 4, "florenc": 4, "itali": 4, "pp": 4, "3201": 4, "3204": 4, "28": 4, "31": 4, "08": 4, "is12_speaker_trait": 4, "elmar": 4, "n\u00f6th": 4, "alessandro": 4, "vinciarelli": 4, "felix": [4, 5], "burkhardt": 4, "rob": 4, "van": 4, "son": 4, "wening": [4, 5], "tobia": 4, "bocklet": 4, "gelareh": 4, "mohammadi": 4, "benjamin": 4, "weiss": 4, "portland": 4, "OR": 4, "usa": 4, "09": 4, "is13_compar": 4, "vocali": 4, "laughter": 4, "is13_compare_voc": 4, "klau": 4, "scherer": 4, "fabien": 4, "ringev": 4, "moham": 4, "chetouani": 4, "erik": 4, "marchi": 4, "marcello": 4, "mortillaro": 4, "hugu": 4, "salamin": 4, "anna": 4, "polychroni": 4, "fabio": 4, "valent": 4, "samuel": 4, "kim": 4, "social": 4, "conflict": 4, "autism": 4, "lyon": 4, "franc": 4, "hollywood": 4, "movi": 4, "nicola": 4, "lehment": 4, "brute": 4, "forc": 4, "workshop": 4, "pisa": 4, "04": 4, "05": 4, "mediaeval12": 4, "mediaeval_audio_is12based_subwin2": 4, "mediaeval_audio_is12based_subwin2_step0": 4, "mediaeval_videofunct": 4, "emobase2": 4, "988": 4, "simpli": [4, 6], "again": 4, "dummi": 4, "label": 4, "inputn": 4, "anger": 4, "fear": 4, "disgust": 4, "classlabel": 4, "nomin": 4, "charact": [4, 6], "f_0": 4, "larger": [4, 6], "6552": 4, "misc": 4, "emo_larg": 4, "conjunct": [4, 6], "volunt": 4, "welcom": [4, 5], "tweak": 4, "greatli": 4, "enhanc": 4, "art": 4, "audiovisu": 4, "audiovideo": 4, "rgb": 4, "opencvsourc": 4, "extract_fac": 4, "cportaudiosourc": 4, "cportaudiosink": 4, "cwavesink": 4, "expect": 4, "audiorecord": 4, "plai": 4, "audioplay": 4, "sampler": 4, "44100": 4, "channel": 4, "stop": 4, "quit": [4, 6], "ctrl": 4, "captur": 4, "liveprosodyacf": 4, "onc": [4, 6], "loop": [4, 6], "sentenc": 4, "microphon": 4, "press": 4, "simultan": 4, "thumb": 4, "too": 4, "mplayer": 4, "video_fil": 4, "audio_fil": 4, "n": [4, 6], "ag": 4, "gender": 4, "ethnic": 4, "video_arff": 4, "audio_arff": 4, "wherea": 4, "denot": [4, 6], "titl": 4, "chosen": [4, 6], "alphanumer": [4, 6], "underscor": 4, "ground": 4, "truth": 4, "particular": 4, "accordingli": 4, "newer": 4, "nox": 4, "far": 4, "feel": 4, "necessari": 4, "guid": 4, "assum": [4, 6], "plotchroma": 4, "plt": 4, "png": 4, "fig": [4, 6], "decibel": 4, "db": 4, "plotspectrogram": 4, "plotcontour": 4, "At": 4, "fourth": 4, "anywai": 4, "2799": 4, "ourc": 5, "edia": 5, "nterpret": 5, "arg": 5, "xtraction": 5, "audio": [5, 6], "video": 5, "homepag": 5, "obtain": [5, 6], "report": [5, 6], "author": 5, "w\u00f6llmer": 5, "mail": 5, "fe": 5, "fw": 5, "mw": 5, "gmbh": 5, "82205": 5, "gilch": 5, "germani": 5, "about": [5, 6], "who": [5, 6], "histori": 5, "changelog": 5, "licens": 5, "compil": [5, 6], "refer": 5, "acknowledg": 5, "interest": 6, "go": 6, "insid": 6, "comprehens": 6, "cover": 6, "seamlessli": 6, "util": 6, "configfil": 6, "loglevel": 6, "int": 6, "verbos": 6, "unus": 6, "ntick": 6, "iter": 6, "normal": 6, "exit": 6, "confighelp": 6, "componentnam": 6, "shown": 6, "empti": 6, "argument": 6, "pars": 6, "myconfigfil": 6, "exporthelp": 6, "consum": 6, "logfil": 6, "writeabl": 6, "appendlogfil": 6, "noconsoleoutput": 6, "consol": 6, "fairli": 6, "therefor": 6, "split": 6, "overview": 6, "arrai": 6, "alloc": 6, "dimens": 6, "know": 6, "mi": 6, "bogu": 6, "inaccess": 6, "happen": 6, "statu": 6, "return": 6, "seri": 6, "non": 6, "did": 6, "safe": 6, "slightli": 6, "paus": 6, "wait": 6, "condit": 6, "anew": 6, "until": 6, "failur": 6, "mainli": 6, "incomplet": 6, "ccommand": 6, "linepars": 6, "cconfigmanag": 6, "briefli": 6, "role": 6, "paragraph": 6, "configinst": 6, "structur": 6, "configtyp": 6, "manger": 6, "enumer": 6, "scan": 6, "becom": 6, "useabl": 6, "standardis": 6, "asynchron": 6, "indirectli": 6, "synchron": 6, "event": 6, "classificationresult": 6, "caught": 6, "esp": 6, "plug": 6, "ins": 6, "principl": 6, "fanci": 6, "repeat": 6, "storag": 6, "outsid": 6, "side": 6, "x": 6, "infti": 6, "row": 6, "regular": 6, "dynam": 6, "succe": 6, "index": 6, "li": 6, "Be": 6, "awar": 6, "overal": 6, "datasourc": 6, "dataprocessor": 6, "datasink": 6, "partial": 6, "fill": 6, "pointer": 6, "setup": 6, "red": 6, "arrow": 6, "cpitch": 6, "doe": 6, "buffers": 6, "overwritten": 6, "func": 6, "blocksiz": 6, "never": 6, "minim": 6, "smaller": 6, "clarif": 6, "precis": 6, "learnt": 6, "meta": 6, "tempor": 6, "hand": 6, "global": 6, "2d": 6, "nfield": 6, "ntimestemp": 6, "transpos": 6, "belong": 6, "togeth": 6, "divid": 6, "sectionnam": 6, "sectiontyp": 6, "oppos": 6, "colon": 6, "bodi": 6, "attribut": 6, "seen": 6, "variable1": 6, "variable2": 6, "variable3": 6, "char": 6, "subconf": 6, "var1": 6, "mynam": 6, "myarr": 6, "value0": 6, "value1": 6, "anotherarr": 6, "implicit": 6, "noarrai": 6, "quot": 6, "strarr": 6, "name1": 6, "name2": 6, "value2": 6, "express": 6, "variable4": 6, "ident": 6, "dminstanc": 6, "source1": 6, "cexamplesourc": 6, "_": 6, "implicitli": 6, "fact": 6, "rather": 6, "newlevel": 6, "level1": 6, "level2": 6, "0250": 6, "010": 6, "anywher": 6, "longopt": 6, "shortopt": 6, "examplesect": 6, "exampletyp": 6, "myattrib1": 6, "descr": 6, "myattrib2": 6, "neither": 6, "examplesection2": 6, "f": 6, "xyz": 6, "yourconfigfil": 6, "equal": 6, "sign": 6, "plan": 6, "duplic": 6, "maintain": 6, "whitespac": 6, "doubl": 6, "slash": 6, "itself": 6, "problem": 6, "close": 6, "marketplac": 6, "visualstudio": 6, "item": 6, "itemnam": 6, "chausner": 6, "highlight": 6, "statement": 6, "diagnost": 6, "inbuilt": 6, "cmycomponentnam": 6, "rebuild": 6, "doc": 6, "sphinx": 6, "readm": 6, "rst": 6, "handi": 6, "usecas": 6, "libsmileapi": 6, "static": 6, "flag": 6, "smileapi_static_link": 6, "public": 6, "dotnet": 6, "mobil": 6, "app": 6, "advanc": 6, "pip": 6, "modul": 6, "numpi": 6, "scheme": 6, "nameappend": 6, "insert": 6, "null": 6, "hard": 6, "discard": 6, "let": 6, "theoret": 6, "chain": 6, "deduct": 6, "redund": 6, "anyth": 6, "resourc": 6, "springer": 6, "ebook": 6, "isbn": 6, "978": 6, "319": 6, "27299": 6, "date": 6, "peopl": 6, "9783319272986": 6}, "objects": {}, "objtypes": {}, "objnames": {}, "titleterms": {"about": 0, "opensmil": [0, 4, 5, 6], "what": 0, "i": 0, "who": 0, "need": 0, "capabl": 0, "data": [0, 4], "input": 0, "signal": 0, "process": [0, 6], "audio": [0, 4], "featur": [0, 4, 6], "low": 0, "level": 0, "video": [0, 4], "function": 0, "classifi": 0, "other": [0, 6], "compon": [0, 3, 6], "output": 0, "capabilit": 0, "histori": 0, "changelog": 0, "licens": 0, "acknowledg": 1, "refer": [2, 4, 6], "develop": 3, "": 3, "document": 3, "write": 3, "get": [3, 4], "start": [3, 4], "basic": 3, "type": 3, "plugin": 3, "obtain": 4, "instal": 4, "compil": 4, "from": 4, "sourc": 4, "linux": 4, "mac": 4, "portaudio": 4, "opencv": 4, "support": [4, 6], "window": 4, "creat": 4, "customis": 4, "build": 4, "releas": 4, "debug": 4, "static": 4, "dynam": 4, "link": [4, 6], "android": 4, "sampl": 4, "app": 4, "io": 4, "an": 4, "librari": 4, "integr": 4, "xcode": 4, "project": 4, "us": 4, "smileapi": [4, 6], "class": 4, "object": 4, "c": [4, 6], "swift": 4, "extract": [4, 6], "your": 4, "first": 4, "default": 4, "set": 4, "common": 4, "option": [4, 6], "all": 4, "standard": 4, "configur": [4, 6], "file": [4, 6], "chroma": 4, "mfcc": 4, "plp": 4, "prosod": 4, "emot": 4, "recognit": 4, "The": [4, 6], "interspeech": 4, "2009": 4, "challeng": 4, "2010": 4, "paralinguist": 4, "2011": 4, "speaker": 4, "state": 4, "2012": 4, "trait": 4, "2013": 4, "compar": 4, "mediaev": 4, "tum": 4, "violent": 4, "scene": 4, "detect": 4, "openear": 4, "emobas": 4, "larg": 4, "emobase2010": 4, "visual": 4, "base": 4, "live": 4, "record": 4, "playback": 4, "visualis": 4, "gnuplot": 4, "section": 6, "smilextract": 6, "command": 6, "line": 6, "architectur": 6, "parser": 6, "manag": 6, "increment": 6, "terminologi": 6, "enabl": 6, "includ": 6, "defin": 6, "variabl": 6, "comment": 6, "id": 6, "api": 6, "wrapper": 6, "name": 6, "algorithm": 6}, "envversion": {"sphinx.domains.c": 2, "sphinx.domains.changeset": 1, "sphinx.domains.citation": 1, "sphinx.domains.cpp": 8, "sphinx.domains.index": 1, "sphinx.domains.javascript": 2, "sphinx.domains.math": 2, "sphinx.domains.python": 3, "sphinx.domains.rst": 2, "sphinx.domains.std": 2, "sphinxcontrib.bibtex": 9, "sphinx": 57}, "alltitles": {"About openSMILE": [[0, "about-opensmile"]], "What is openSMILE?": [[0, "what-is-opensmile"]], "Who needs openSMILE?": [[0, "who-needs-opensmile"]], "Capabilities": [[0, "capabilities"]], "Data input": [[0, "data-input"]], "Signal processing": [[0, "signal-processing"]], "Data processing": [[0, "data-processing"]], "Audio features (low-level)": [[0, "audio-features-low-level"]], "Video features (low-level)": [[0, "video-features-low-level"]], "Functionals": [[0, "functionals"]], "Classifiers and other components": [[0, "classifiers-and-other-components"]], "Data output": [[0, "data-output"]], "Other capabilites": [[0, "other-capabilites"]], "History and Changelog": [[0, "history-and-changelog"]], "Licensing": [[0, "licensing"]], "Acknowledgement": [[1, "acknowledgement"]], "References": [[2, "references"]], "Developer\u2019s documentation": [[3, "developers-documentation"]], "Writing components": [[3, "writing-components"]], "Getting started": [[3, "getting-started"]], "Basic component types": [[3, "basic-component-types"]], "Writing plugins": [[3, "writing-plugins"]], "Get started": [[4, "get-started"]], "Obtaining and Installing openSMILE": [[4, "obtaining-and-installing-opensmile"]], "Compiling from source": [[4, "compiling-from-source"]], "Compiling on Linux/Mac": [[4, "compiling-on-linux-mac"]], "Compiling on Linux/Mac with PortAudio and OpenCV support": [[4, "compiling-on-linux-mac-with-portaudio-and-opencv-support"]], "Installing PortAudio": [[4, "installing-portaudio"]], "Installing OpenCV": [[4, "installing-opencv"]], "Compiling openSMILE on Linux/Mac with OpenCV video support": [[4, "compiling-opensmile-on-linux-mac-with-opencv-video-support"]], "Compiling on Windows": [[4, "compiling-on-windows"]], "Compiling on Windows with PortAudio and OpenCV support": [[4, "compiling-on-windows-with-portaudio-and-opencv-support"]], "Creating customised builds": [[4, "creating-customised-builds"]], "Release/debug builds": [[4, "release-debug-builds"]], "Static/dynamic linking": [[4, "static-dynamic-linking"]], "Compiling for Android and creating the sample Android app": [[4, "compiling-for-android-and-creating-the-sample-android-app"]], "Compiling for iOS and creating an iOS App": [[4, "compiling-for-ios-and-creating-an-ios-app"]], "Build openSMILE static library": [[4, "build-opensmile-static-library"]], "Integrate openSMILE library into an Xcode project": [[4, "integrate-opensmile-library-into-an-xcode-project"]], "Using SMILEapi and openSMILE classes in Objective-C": [[4, "using-smileapi-and-opensmile-classes-in-objective-c"]], "Using SMILEapi and openSMILE classes in Swift": [[4, "using-smileapi-and-opensmile-classes-in-swift"]], "Extracting your first features": [[4, "extracting-your-first-features"]], "Default feature sets": [[4, "default-feature-sets"]], "Common options for all standard configuration files": [[4, "common-options-for-all-standard-configuration-files"]], "Chroma features": [[4, "chroma-features"]], "MFCC features": [[4, "mfcc-features"]], "PLP features": [[4, "plp-features"]], "Prosodic features": [[4, "prosodic-features"]], "Extracting features for emotion recognition": [[4, "extracting-features-for-emotion-recognition"]], "The INTERSPEECH 2009 Emotion Challenge feature set": [[4, "the-interspeech-2009-emotion-challenge-feature-set"]], "The INTERSPEECH 2010 Paralinguistic Challenge feature set": [[4, "the-interspeech-2010-paralinguistic-challenge-feature-set"]], "The INTERSPEECH 2011 Speaker State Challenge feature set": [[4, "the-interspeech-2011-speaker-state-challenge-feature-set"]], "The INTERSPEECH 2012 Speaker Trait Challenge feature set": [[4, "the-interspeech-2012-speaker-trait-challenge-feature-set"]], "The INTERSPEECH 2013 ComParE Challenge feature set": [[4, "the-interspeech-2013-compare-challenge-feature-set"]], "The MediaEval 2012 TUM feature set for violent video scenes detection": [[4, "the-mediaeval-2012-tum-feature-set-for-violent-video-scenes-detection"]], "The openSMILE/openEAR \u2018emobase\u2019 set": [[4, "the-opensmile-openear-emobase-set"]], "The large openSMILE emotion feature set": [[4, "the-large-opensmile-emotion-feature-set"]], "The openSMILE \u2018emobase2010\u2019 reference set": [[4, "the-opensmile-emobase2010-reference-set"]], "Audio-visual features based on INTERSPEECH 2010 audio features": [[4, "audio-visual-features-based-on-interspeech-2010-audio-features"]], "Using PortAudio for live recording/playback": [[4, "using-portaudio-for-live-recording-playback"]], "Extracting features with OpenCV": [[4, "extracting-features-with-opencv"]], "Visualising data with Gnuplot": [[4, "visualising-data-with-gnuplot"]], "openSMILE": [[5, "opensmile"]], "Reference section": [[6, "reference-section"]], "SMILExtract command-line options": [[6, "smilextract-command-line-options"]], "openSMILE architecture": [[6, "opensmile-architecture"]], "The command-line parser": [[6, "the-command-line-parser"]], "The configuration manager": [[6, "the-configuration-manager"]], "The component manager": [[6, "the-component-manager"]], "Incremental processing": [[6, "incremental-processing"]], "openSMILE terminology": [[6, "opensmile-terminology"]], "Configuration files": [[6, "configuration-files"]], "Enabling components": [[6, "enabling-components"]], "Configuring components": [[6, "configuring-components"]], "Including other configuration files": [[6, "including-other-configuration-files"]], "Linking to command-line options": [[6, "linking-to-command-line-options"]], "Defining variables": [[6, "defining-variables"]], "Comments": [[6, "comments"]], "IDE support for configuration files": [[6, "ide-support-for-configuration-files"]], "Components": [[6, "components"]], "SMILEapi C API and wrappers": [[6, "smileapi-c-api-and-wrappers"]], "Feature names": [[6, "feature-names"]], "Feature extraction algorithms": [[6, "feature-extraction-algorithms"]]}, "indexentries": {}})