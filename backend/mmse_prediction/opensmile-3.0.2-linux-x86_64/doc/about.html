

<!DOCTYPE html>
<!--[if IE 8]><html class="no-js lt-ie9" lang="en" > <![endif]-->
<!--[if gt IE 8]><!--> <html class="no-js" lang="en" > <!--<![endif]-->
<head>
  <meta charset="utf-8">
  
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  
  <title>About openSMILE &mdash; openSMILE Documentation</title>
  

  
  
    <link rel="shortcut icon" href="_static/favicon.png"/>
  
  
  

  

  
  
    

  
  <link rel="stylesheet" href="_static/pygments.css" type="text/css" />
  <link rel="stylesheet" href="_static/css/theme.css" type="text/css" />
  <link rel="stylesheet" href="_static/css/audeering.css" type="text/css" />
    <link rel="author" title="About these documents" href="#" />
    <link rel="index" title="Index" href="genindex.html" />
    <link rel="search" title="Search" href="search.html" />
    <link rel="next" title="Get started" href="get-started.html" />
    <link rel="prev" title="openSMILE" href="index.html" />
    
  

  
  <script src="_static/js/modernizr.min.js"></script>

</head>

<body class="wy-body-for-nav">

   
  <div class="wy-grid-for-nav">

    
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search">
          

          <a href="index.html">
          
            
            <img src="_static/openSMILE-logoSlogan-white.svg" class="logo" alt="Logo"/>
          
          
            
          
          </a>

          
            
            
              <div class="version">
                3.0.2
              </div>
            
          

          
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>

          
        </div>

        <div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="main navigation">
          
            
            
              
            
            
              <ul class="current">
<li class="toctree-l1 current"><a class="current reference internal" href="#">About openSMILE</a><ul>
<li class="toctree-l2"><a class="reference internal" href="#what-is-opensmile">What is openSMILE?</a></li>
<li class="toctree-l2"><a class="reference internal" href="#who-needs-opensmile">Who needs openSMILE?</a></li>
<li class="toctree-l2"><a class="reference internal" href="#capabilities">Capabilities</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#data-input">Data input</a></li>
<li class="toctree-l3"><a class="reference internal" href="#signal-processing">Signal processing</a></li>
<li class="toctree-l3"><a class="reference internal" href="#data-processing">Data processing</a></li>
<li class="toctree-l3"><a class="reference internal" href="#audio-features-low-level">Audio features (low-level)</a></li>
<li class="toctree-l3"><a class="reference internal" href="#video-features-low-level">Video features (low-level)</a></li>
<li class="toctree-l3"><a class="reference internal" href="#functionals">Functionals</a></li>
<li class="toctree-l3"><a class="reference internal" href="#classifiers-and-other-components">Classifiers and other components</a></li>
<li class="toctree-l3"><a class="reference internal" href="#data-output">Data output</a></li>
<li class="toctree-l3"><a class="reference internal" href="#other-capabilites">Other capabilites</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#history-and-changelog">History and Changelog</a></li>
<li class="toctree-l2"><a class="reference internal" href="#licensing">Licensing</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="get-started.html">Get started</a></li>
<li class="toctree-l1"><a class="reference internal" href="reference.html">Reference section</a></li>
<li class="toctree-l1"><a class="reference internal" href="developer.html">Developer’s documentation</a></li>
<li class="toctree-l1"><a class="reference internal" href="acknowledgement.html">Acknowledgement</a></li>
<li class="toctree-l1"><a class="reference internal" href="bibliography.html">References</a></li>
</ul>

            
          
        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap">

      
      <nav class="wy-nav-top" aria-label="top navigation">
        
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="index.html">openSMILE</a>
        
      </nav>


      <div class="wy-nav-content">
        
        <div class="rst-content">
        
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="index.html" class="icon icon-home"></a> &raquo;</li>
      <li>About openSMILE</li>
      <li class="wy-breadcrumbs-aside">
              <a href="https://github.com/audeering/opensmile/" class="fa fa-github"> GitHub</a>
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
            
  <div class="section" id="about-opensmile">
<span id="id1"></span><h1>About openSMILE<a class="headerlink" href="#about-opensmile" title="Permalink to this heading">¶</a></h1>
<p>We start introducing openSMILE by addressing two important questions for users
who are new to openSMILE: <em>What is openSMILE?</em> and <em>Who needs openSMILE?</em>. If
you want to start using openSMILE right away, then you should start reading
Section <a class="reference internal" href="get-started.html#get-started"><span class="std std-ref">Get started</span></a>, or Section <a class="reference internal" href="get-started.html#extracting-your-first-features"><span class="std std-ref">Extracting your first features</span></a>
if you have already installed openSMILE.</p>
<div class="section" id="what-is-opensmile">
<span id="id2"></span><h2>What is openSMILE?<a class="headerlink" href="#what-is-opensmile" title="Permalink to this heading">¶</a></h2>
<p>The Munich open-Source Media Interpretation by Large feature-space
Extraction (openSMILE) toolkit is a modular and flexible feature
extractor for signal processing and machine learning applications. The
primary focus is clearly put on audio-signal features. However, due to
their high degree of abstraction, openSMILE components can also be used
to analyse signals from other modalities, such as physiological signals,
visual signals, and other physical sensors, given suitable input
components. It is written purely in C++, has a fast, efficient, and
flexible architecture, and runs on common platforms such as
Linux, Windows, MacOS, Android and iOS.</p>
<p>openSMILE is designed for real-time online processing but can also be
used off-line in batch mode for processing of large data-sets. This is
a feature rarely found in other tools for feature extraction. Most of
related projects are designed for off-line extraction and require the
whole input to be present. openSMILE can extract features incrementally
as new data arrives. By using the PortAudio <a class="footnote-reference brackets" href="#id16" id="id3">1</a> library, openSMILE
supports platform-independent live audio input and live audio playback,
enabling the extraction of audio features in real-time.</p>
<p>To facilitate interoperability, openSMILE supports reading and writing
of various data formats commonly used in the field of data mining and
machine learning. These formats include PCM WAVE for audio files, CSV
(Comma Separated Value, spreadsheet format) and ARFF (Weka Data Mining)
for text-based data files, HTK (Hidden-Markov Toolkit) parameter files,
and a simple binary float matrix format for binary feature data.</p>
<p>openSMILE comes with a slim but powerful C API, allowing a seamless
integration into other applications. Wrappers are available for Python,
.NET, Android and iOS. The API provides programmatic access to all
functionality that is available in the command-line tool, including
passing audio or feature data to and from openSMILE in real-time.</p>
<p>Using the open-source software gnuplot <a class="footnote-reference brackets" href="#id17" id="id4">2</a>, extracted features which
are dumped to files can be visualised. A strength of openSMILE, enabled
by its highly modular architecture, is that almost all intermediate data
which is generated during the feature extraction process (such as
windowed audio data, spectra, etc.) can be accessed and saved to files
for visualisation or further processing.</p>
</div>
<div class="section" id="who-needs-opensmile">
<span id="id5"></span><h2>Who needs openSMILE?<a class="headerlink" href="#who-needs-opensmile" title="Permalink to this heading">¶</a></h2>
<p>openSMILE is intended to be used primarily for research applications,
demonstrators and prototypes. Thus, the target group of users is
researchers and system developers. Due to its compact code and modular
architecture, using openSMILE in a final product is feasible, as well.
However, we would like to stress that openSMILE is distributed under a
research-only license (see the next section for details).</p>
<p>Currently, openSMILE is used all around the world by researchers and
companies working in the field of speech recognition (feature
extraction front-end, keyword spotting, etc.), the area of affective
computing (emotion recognition, affect sensitive virtual agents, etc.),
and Music Information Retrieval (chord recognition, beat tracking, onset
detection etc.). Since the 2.0 release, we target the wider
multi-media community by including the popular OpenCV library for video
processing and video feature extraction.</p>
</div>
<div class="section" id="capabilities">
<span id="id6"></span><h2>Capabilities<a class="headerlink" href="#capabilities" title="Permalink to this heading">¶</a></h2>
<p>This section gives a brief summary on openSMILE’s capabilities. The
capabilities are distinguished by the following categories: data input,
signal processing, general data processing, low-level audio features,
functionals, classifiers and other components, data output, and other
capabilities.</p>
<div class="section" id="data-input">
<span id="id7"></span><h3>Data input<a class="headerlink" href="#data-input" title="Permalink to this heading">¶</a></h3>
<p>openSMILE can read data from the following file formats natively:</p>
<ul class="simple">
<li><p>RIFF-WAVE (uncompressed PCM only)</p></li>
<li><p>Comma Separated Value (CSV)</p></li>
<li><p>HTK parameter files</p></li>
<li><p>WEKA’s ARFF format</p></li>
</ul>
<p>If built with additional, optional dependencies, openSMILE can also
read data in the following formats:</p>
<ul class="simple">
<li><p>Any audio format supported by FFmpeg</p></li>
<li><p>Video streams via OpenCV</p></li>
</ul>
<p>In addition, when openSMILE is interfaced via SMILEapi, the following
two data formats can be passed programmatically as input:</p>
<ul class="simple">
<li><p>Raw float PCM audio samples</p></li>
<li><p>Raw float feature data</p></li>
</ul>
<p>For compatibility, official binaries are not built with FFmpeg
support and all config files use cWaveSource components by default.
This means that audio stored in compressed formats such as MP3, MP4
or OGG needs to be converted to uncompressed WAVE format before it
can be analysed with openSMILE. If you would like to directly process
other audio formats, you need to build openSMILE yourself with FFmpeg
support and replace uses of cWaveSource with cFFmpegSource in config
files.</p>
<p>Live recording of audio from any PC sound card is supported via the
PortAudio library. For generating white noise, sinusoidal tones, and
constant values a signal Generator is provided.</p>
</div>
<div class="section" id="signal-processing">
<span id="id8"></span><h3>Signal processing<a class="headerlink" href="#signal-processing" title="Permalink to this heading">¶</a></h3>
<p>The following functionality is provided for general signal processing or
signal pre-processing (prior to feature extraction):</p>
<ul class="simple">
<li><p>Windowing-functions (Rectangular, Hamming, Hann (raised cosine),
Gauss, Sine, Triangular, Bartlett, Bartlett-Hann, Blackmann,
Blackmann-Harris, Lanczos)</p></li>
<li><p>Pre-/De-emphasis (i.e. 1st order high/low-pass)</p></li>
<li><p>Resampling (spectral domain algorithm)</p></li>
<li><p>FFT (magnitude, phase, complex) and inverse</p></li>
<li><p>Scaling of spectral axis via spline interpolation</p></li>
<li><p>dbA weighting of magnitude spectrum</p></li>
<li><p>Autocorrelation function (ACF) (via IFFT of power spectrum)</p></li>
<li><p>Average magnitude difference function (AMDF)</p></li>
</ul>
</div>
<div class="section" id="data-processing">
<span id="id9"></span><h3>Data processing<a class="headerlink" href="#data-processing" title="Permalink to this heading">¶</a></h3>
<p>openSMILE can perform a number of operations for feature normalisation,
modification, and differentiation:</p>
<ul class="simple">
<li><p>Mean-Variance normalisation (off-line and on-line)</p></li>
<li><p>Range normalisation (off-line and on-line)</p></li>
<li><p>Delta-Regression coefficients (and simple differential)</p></li>
<li><p>Weighted Differential as in <span id="id10">[<a class="reference internal" href="bibliography.html#id2" title="Björn Schuller, Florian Eyben, and Gerhard Rigoll. Fast and robust meter and tempo recognition for the automatic discrimination of ballroom dance styles. In Proceedings ICASSP 2007. Honolulu, Hawaii, 2007.">SER07</a>]</span></p></li>
<li><p>Various vector operations: length, element-wise addition,
multiplication, logarithm and power</p></li>
<li><p>Moving average filter for smoothing of contour over time</p></li>
</ul>
</div>
<div class="section" id="audio-features-low-level">
<span id="id11"></span><h3>Audio features (low-level)<a class="headerlink" href="#audio-features-low-level" title="Permalink to this heading">¶</a></h3>
<p>The following (audio-specific) low-level descriptors can be computed by
openSMILE:</p>
<ul class="simple">
<li><p>Frame Energy</p></li>
<li><p>Frame Intensity / Loudness (approximation)</p></li>
<li><p>Critical Band spectra (Mel/Bark/Octave, triangular masking filters)</p></li>
<li><p>Mel-/Bark-Frequency-Cepstral Coefficients (MFCC)</p></li>
<li><p>Auditory Spectra</p></li>
<li><p>Loudness approximated from auditory spectra</p></li>
<li><p>Perceptual Linear Predictive (PLP) Coefficients</p></li>
<li><p>Perceptual Linear Predictive Cepstral Coefficients (PLP-CC)</p></li>
<li><p>Linear Predictive Coefficients (LPC)</p></li>
<li><p>Line Spectral Pairs (LSP, aka. LSF)</p></li>
<li><p>Fundamental Frequency (via ACF/Cepstrum method and via
Subharmonic-Summation (SHS))</p></li>
<li><p>Probability of Voicing from ACF and SHS spectrum peak</p></li>
<li><p>Voice-Quality: Jitter and Shimmer</p></li>
<li><p>Formant frequencies and bandwidths</p></li>
<li><p>Zero and Mean Crossing rate</p></li>
<li><p>Spectral features (arbitrary band energies, roll-off points,
centroid, entropy, maxpos, minpos, variance (= spread), skewness,
kurtosis, slope)</p></li>
<li><p>Psychoacoustic sharpness, spectral harmonicity</p></li>
<li><p>CHROMA (octave-warped semitone spectra) and CENS features
(energy-normalised and smoothed CHROMA)</p></li>
<li><p>CHROMA-derived features for Chord and Key recognition</p></li>
<li><p>F0 Harmonics ratios</p></li>
</ul>
</div>
<div class="section" id="video-features-low-level">
<span id="id12"></span><h3>Video features (low-level)<a class="headerlink" href="#video-features-low-level" title="Permalink to this heading">¶</a></h3>
<p>The following video low-level descriptors can be currently computed by
openSMILE, based on the OpenCV library:</p>
<ul class="simple">
<li><p>HSV colour histograms</p></li>
<li><p>Local binary patterns (LBP)</p></li>
<li><p>LBP histograms</p></li>
<li><p>Optical flow and optical flow histograms</p></li>
<li><p>Face detection: all these features can be extracted from an
automatically detected facial region or from the full image.</p></li>
</ul>
</div>
<div class="section" id="functionals">
<span id="id13"></span><h3>Functionals<a class="headerlink" href="#functionals" title="Permalink to this heading">¶</a></h3>
<p>In order to map contours of audio and video low-level descriptors onto a
vector of fixed dimensionality, the following functionals can be
applied:</p>
<ul class="simple">
<li><p>Extreme values and positions</p></li>
<li><p>Means (arithmetic, quadratic, geometric)</p></li>
<li><p>Moments (standard deviation, variance, kurtosis, skewness)</p></li>
<li><p>Percentiles and percentile ranges</p></li>
<li><p>Regression (linear and quadratic approximation, regression error)</p></li>
<li><p>Centroid</p></li>
<li><p>Peaks</p></li>
<li><p>Segments</p></li>
<li><p>Sample values</p></li>
<li><p>Times/durations</p></li>
<li><p>Onsets/Offsets</p></li>
<li><p>Discrete Cosine Transformation (DCT)</p></li>
<li><p>Zero Crossings</p></li>
<li><p>Linear Predictive Coding (LPC) coefficients and gain</p></li>
</ul>
</div>
<div class="section" id="classifiers-and-other-components">
<span id="id14"></span><h3>Classifiers and other components<a class="headerlink" href="#classifiers-and-other-components" title="Permalink to this heading">¶</a></h3>
<p>Live demonstrators for audio processing tasks often require segmentation
of the audio stream. openSMILE provides voice activity detection
algorithms and a turn detector for this purpose. For incremental
classification of extracted features, Support Vector Machines are
implemented using the LIBSVM library.</p>
<ul class="simple">
<li><p>Voice Activity Detection based on Fuzzy Logic</p></li>
<li><p>Voice Activity Detection based on LSTM-RNN with pre-trained models</p></li>
<li><p>Turn/Speech segment detector</p></li>
<li><p>LIBSVM (on-line)</p></li>
<li><p>SVM sink (for loading WEKA SMO models using a linear kernel)</p></li>
<li><p>GMM (experimental implementation from the eNTERFACE’12 project, to be
released soon)</p></li>
<li><p>LSTM-RNN (Neural Network) classifier which can load RNNLIB and
CURRENNT nets</p></li>
<li><p>Speech Emotion recognition pre-trained models (openEAR)</p></li>
</ul>
</div>
<div class="section" id="data-output">
<span id="id15"></span><h3>Data output<a class="headerlink" href="#data-output" title="Permalink to this heading">¶</a></h3>
<p>For writing data to files, the following formats are natively supported:</p>
<ul class="simple">
<li><p>RIFF-WAVE (PCM uncompressed audio)</p></li>
<li><p>Comma Separated Value (CSV)</p></li>
<li><p>HTK parameter file</p></li>
<li><p>WEKA ARFF file</p></li>
<li><p>LIBSVM feature file format</p></li>
<li><p>Binary float matrix format</p></li>
</ul>
<p>In addition, when openSMILE is interfaced via SMILEapi, the following
types of data can be received programmatically as output:</p>
<ul class="simple">
<li><p>Raw float PCM audio samples</p></li>
<li><p>Raw float feature data</p></li>
<li><p>Component messages</p></li>
</ul>
<p>Additionally, live audio playback is supported via the PortAudio
library.</p>
</div>
<div class="section" id="other-capabilites">
<span id="other-capabilities"></span><h3>Other capabilites<a class="headerlink" href="#other-capabilites" title="Permalink to this heading">¶</a></h3>
<p>Besides input, signal processing, feature extraction, functionals and
output components, openSMILE comes with a few other capabilites (to
avoid confusion, we do not use the term ‘features’ here):</p>
<dl>
<dt>Multi-threading</dt><dd><p>Independent components can be run in parallel to make use of
multiple CPUs or CPU cores and thus speed up feature extraction
where performance is critical.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Multi-threaded processing is only supported in openSMILE
versions 2.3 and lower. Starting with version 3.0, the setting
is ignored and processing always uses a single thread.</p>
</div>
</dd>
<dt>Plugin support</dt><dd><p>Additional components can be built as shared libraries (DLLs on
Windows) linked against openSMILE’s core API library. Such plugins
are automatically detected during initialisation of the program if
they are placed in the <code class="docutils literal notranslate"><span class="pre">plugins</span></code> subdirectory.</p>
</dd>
<dt>Extensive logging</dt><dd><p>Log messages can be printed to the standard error output, saved to
a file or received programmatically via SMILEapi. The detail of the
messages can be controlled via a log level setting. For easier
interpretation of the messages, the types <em>Message</em> (MSG),
<em>Warning</em> (WRN), <em>Error</em> (ERR), and <em>Debug</em> (DBG) are distinguished.</p>
</dd>
<dt>Flexible configuration</dt><dd><p>openSMILE can be fully configured via one single text-based
configuration file. This file is kept in a simple, yet very
powerful, INI-style file format. Each component has its own section
and all components can be connected to a central data memory
component. The configuration file even allows for defining custom
command-line options (e.g. for input and output files), and
inclusion of other configuration files to build reusable modular
configuration blocks. The name of the configuration file to include
can be specified on the command-line, allowing maximum flexibility
in scripting.</p>
</dd>
<dt>Incremental processing</dt><dd><p>All components in openSMILE follow strict guidelines to meet the
requirements of incremental processing. It is not allowed to require
access to the full input sequence and seek back and forth within the
sequence, for example. Principally each component must be able to
process its data frame by frame or at least as soon as possible.
Some exceptions to this rule have been granted for components which
are only used during off-line feature extraction, such as components
for full-input mean normalisation.</p>
</dd>
<dt>Multi-pass processing in batch mode</dt><dd><p>For certain tasks, multi-pass processing is required, which
obviously can only be applied in off-line (or buffered) mode.
openSMILE 2.0 and higher supports multi-pass processing for all
existing components.</p>
</dd>
</dl>
<dl class="footnote brackets">
<dt class="label" id="id16"><span class="brackets"><a class="fn-backref" href="#id3">1</a></span></dt>
<dd><p><a class="reference external" href="http://www.portaudio.com">http://www.portaudio.com</a></p>
</dd>
<dt class="label" id="id17"><span class="brackets"><a class="fn-backref" href="#id4">2</a></span></dt>
<dd><p><a class="reference external" href="http://www.gnuplot.info/">http://www.gnuplot.info/</a></p>
</dd>
</dl>
</div>
</div>
<div class="section" id="history-and-changelog">
<span id="id18"></span><h2>History and Changelog<a class="headerlink" href="#history-and-changelog" title="Permalink to this heading">¶</a></h2>
<p>openSMILE was originally created in the scope of the European EU-FP7
research project SEMAINE (<a class="reference external" href="http://www.semaine-project.eu">http://www.semaine-project.eu</a>) and is used
there as the acoustic emotion recognition engine and keyword spotter in
a real-time affective dialogue system. To serve the research community,
open-source releases of openSMILE were made independently of the main
project’s code releases.</p>
<p>The first publicly available version of openSMILE was contained in the
first Emotion and Affect recognition toolkit openEAR as the feature
extraction core. openEAR was introduced at the Affective Computing and
Intelligent Interaction (ACII) conference in 2009. One year later, the
first independent release of openSMILE (version 1.0.0) was published,
which aimed at reaching a wider community of audio analysis researchers.
It was presented at ACM Multimedia 2010 in the Open-Source Software
Challenge. This first release was followed by a small bugfix release
(1.0.1) shortly afterwards. Since then, development has taken place in
the subversion repository on SourceForge. Since 2011, the development
has been continued in a private repository due to various licensing
issues with internal and third-party projects.</p>
<p>openSMILE 2.0 (rc1) is the next major release after the 1.0.1 version
and contains revised core components and a long list of bugfixes, new
components, as well as improved old components, extended documentation,
a restructured source tree and new major functionality such as a
multi-pass mode and support for synchronised audio-visual feature
extraction based on OpenCV.</p>
<p>Version 2.1 contains further fixes, improved backwards compatibility of
the standard INTERSPEECH challenge parameter sets, support for reading
JSON neural network files created with the CURRENNT toolkit, an F0
harmonics component, a fast linear SVM sink component for integrating
models trained with WEKA SMO, as well as a number of other minor new
components and features. It is the first version published and supported
by audEERING.</p>
<p>Version 2.2 comes with the configuration files for the first release of
the Geneva Minimalistic Acoustic Parameter Set (GeMAPS).</p>
<p>Version 2.3 includes Android JNI integration, an updated configuration
file interface, a batch feature extraction GUI for Windows, improved
backwards compatibility, an updated version of the ComParE 2013–2015
baseline acoustic parameter set, as well as several bugfixes and
performance improvements.</p>
<p>Version 3.0 is the third major release of openSMILE featuring a large
number of incremental improvements and fixes. Most notably, it
introduces the new SMILEapi C API and a standalone Python library.
Other changes include a fully rewritten build process using CMake,
support for the iOS platform, an updated Android integration,
an FFmpeg audio source component, major performance and memory usage
improvements, documentation in HTML format, and numerous other minor
updates, code refactorings and fixes. Beginning with version 3.0,
openSMILE binaries and source code are hosted on GitHub.</p>
</div>
<div class="section" id="licensing">
<span id="id19"></span><h2>Licensing<a class="headerlink" href="#licensing" title="Permalink to this heading">¶</a></h2>
<p>openSMILE follows a dual-licensing model. Since the main goal of the
project is a widespread use of the software to facilitate research in
the field of machine learning from audio-visual signals, the source code
and binaries are freely available for private, research, and educational
use under an open-source license. It is not allowed to use the
open-source version of openSMILE for any sort of commercial product.
Fundamental research in companies, for example, is permitted, but if a
product is the result of the research, we require you to buy a
commercial development license. Contact us at <a class="reference external" href="mailto:info&#37;&#52;&#48;audeering&#46;com">info<span>&#64;</span>audeering<span>&#46;</span>com</a> (or
visit us at <a class="reference external" href="https://www.audeering.com">https://www.audeering.com</a>) for more information.</p>
</div>
</div>


           </div>
           
          </div>
          <footer>
  
    <div class="rst-footer-buttons" role="navigation" aria-label="footer navigation">
      
        <a href="get-started.html" class="btn btn-neutral float-right" title="Get started" accesskey="n" rel="next">Next <span class="fa fa-arrow-circle-right"></span></a>
      
      
        <a href="index.html" class="btn btn-neutral float-left" title="openSMILE" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left"></span> Previous</a>
      
    </div>
  

  <hr/>

  <div>
    <p>
        
        
        
          Built with <a href="https://www.sphinx-doc.org/en/master/">Sphinx</a> on 2023/10/19 using the <a href="https://github.com/audeering/sphinx-audeering-theme/">audEERING theme</a>
        
    </p>
  </div>

  <div role="contentinfo">
    <p>
        
      &copy; 2013-2023 audEERING GmbH and 2008-2013 TU München, MMK
    </p>
  </div> 

</footer>
        </div>
      </div>

    </section>

  </div>
  



  
  <!--[if lt IE 9]>
    <script src="_static/js/html5shiv.min.js"></script>
  <![endif]-->
  

    
    
      <script type="text/javascript" id="documentation_options" data-url_root="./" src="_static/documentation_options.js"></script>
        <script data-url_root="./" id="documentation_options" src="_static/documentation_options.js"></script>
        <script src="_static/jquery.js"></script>
        <script src="_static/underscore.js"></script>
        <script src="_static/_sphinx_javascript_frameworks_compat.js"></script>
        <script src="_static/doctools.js"></script>
        <script src="_static/sphinx_highlight.js"></script>
    

  

  <script type="text/javascript" src="_static/js/theme.js"></script>

  <script type="text/javascript">
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>