.. openSMILE documentation master file

openSMILE
=========

.. only:: html

    Welcome to the documentation of openSMILE (**open**\ -\ **S**\ ource **M**\ edia 
    **I**\ nterpretation by **L**\ arge feature-space **E**\ xtraction).

    openSMILE allows you to extract audio and video features for signal
    processing and machine learning, with an emphasis on features enabling
    emotion recognition from speech.

    The official openSMILE homepage can be found at:
    https://opensmile.audeering.com/.

    To obtain binaries, source code and report issues, visit the 
    openSMILE page on GitHub:
    https://github.com/audeering/opensmile

    | Original authors: <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>,
      <PERSON><PERSON><PERSON><PERSON>
    | E-mails: fe, fw, mw, bs at audeering.com

    .. image:: _static/images/logo-audeering.png
        :width: 150px

    | audEERING GmbH
    | D-82205 Gilching, Germany
    | http://audeering.com/
    |

    ----

    .. rubric:: Contents


.. toctree::
   :maxdepth: 2

   about
   get-started
   reference
   developer
   acknowledgement
   bibliography
