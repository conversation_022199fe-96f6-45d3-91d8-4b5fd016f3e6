///////////////////////////////////////////////////////////////////////////////////////
///////// > openSMILE LSTM-RNN voice activity detector<              //////////////////
/////////                                                            //////////////////
///////// (c) audEERING UG (haftungsbeschränkt),                     //////////////////
/////////     All rights reserved.                                  //////////////////
///////////////////////////////////////////////////////////////////////////////////////


[componentInstances:cComponentManager]
instance[dataMemory].type = cDataMemory
instance[waveSource].type = cWaveSource
; verbose level stats
; printLevelStats = 6

[waveSource:cWaveSource]
writer.dmLevel = wave
filename = \cm[inputfile(I){input.wav}:name of input file]
monoMixdown = 1
start = 0
end = -1
endrel = 0
noHeader = 0
buffersize_sec = 10

;; includes the VAD module
\{vad_opensource.conf.inc}


[componentInstances:cComponentManager]
; output vad activations
instance[csvSink].type = cCsvSink

[csvSink:cCsvSink]
reader.dmLevel=vad_VAD_voice
filename= \cm[csvoutput(O){vad.csv}:name of VAD output file]
printHeader = 0
timestamp = 1
number = 0
delimChar = ,

