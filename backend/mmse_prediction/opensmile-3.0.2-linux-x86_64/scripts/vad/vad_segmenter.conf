///////////////////////////////////////////////////////////////////////////////////////
///////// > openSMILE LSTM-RNN voice activity detector<              //////////////////
/////////                                                            //////////////////
///////// (c) audEERING UG (haftungsbeschränkt),                     //////////////////
/////////     All rights reserved.                                  //////////////////
///////////////////////////////////////////////////////////////////////////////////////


[componentInstances:cComponentManager]
instance[dataMemory].type = cDataMemory
instance[waveSource].type = cWaveSource

[waveSource:cWaveSource]
writer.dmLevel = wave
filename = \cm[inputfile(I){input.wav}:name of input file]
monoMixdown = 1
start = 0
end = -1
endrel = 0
noHeader = 0
buffersize_sec = 10


 ; inculdes a VAD module
\{vad_opensource.conf.inc}
 ; and a turn detector module
\{\cm[turnDetector(T){turnDetector.conf.inc}:filename of turn detector config file]}


[componentInstances:cComponentManager]
 ; the wave file segmenter
instance[waveSinkCut].type = cWaveSinkCut
 ; optional: CSV output
instance[csvSink].type = cCsvSink
printLevelStats = 0

[waveSinkCut:cWaveSinkCut]
reader.dmLevel = framesVAD
fileBase = \cm[waveoutput(W){output_segment_}:prefix of WAV output files]
fileExtension = .wav
fileNameFormatString = %s%04d%s
startIndex = 1
preSil = 0.1
postSil = 0.1
multiOut = 1
sampleFormat = 16bit
; sample rate should be read from the input level
; automatically. In some cases this does not work due to
; round-off errors, so you can force it manually here:
;forceSampleRate = 44100
;forceSampleRate = 16000
saveSegmentTimes = \cm[saveSegmentTimes{?}:file to save segment times to]

[csvSink:cCsvSink]
reader.dmLevel=vad_VAD_voice
filename= \cm[csvoutput{?}:name of VAD output file]
printHeader = 0
timestamp = 1
number = 0

