#You must ensure nmake.exe, cl.exe, link.exe are in system path.
#VCVARS32.bat
#Under dosbox prompt
#nmake -f Makefile.win

PYTHON_INC = c:\python26\include
PYTHON_LIB = c:\python26\libs\python26.lib
##########################################
CXX = cl.exe
CFLAGS = -nologo -O2 -EHsc -I. -D __WIN32__ -D _CRT_SECURE_NO_DEPRECATE
TARGET = windows

all: $(TARGET)\svm-train.exe $(TARGET)\svm-predict.exe $(TARGET)\svm-scale.exe $(TARGET)\svm-toy.exe

python: $(TARGET)\python\svmc.pyd

$(TARGET)\svm-predict.exe: svm.h svm-predict.c svm.obj
    $(CXX) $(CFLAGS) svm-predict.c svm.obj -Fe$(TARGET)\svm-predict.exe

$(TARGET)\svm-train.exe: svm.h svm-train.c svm.obj
    $(CXX) $(CFLAGS) svm-train.c svm.obj -Fe$(TARGET)\svm-train.exe

$(TARGET)\svm-scale.exe: svm.h svm-scale.c
    $(CXX) $(CFLAGS) svm-scale.c -Fe$(TARGET)\svm-scale.exe

$(TARGET)\svm-toy.exe: svm.h svm.obj svm-toy\windows\svm-toy.cpp
	$(CXX) $(CFLAGS) svm-toy\windows\svm-toy.cpp svm.obj user32.lib gdi32.lib comdlg32.lib  -Fe$(TARGET)\svm-toy.exe

svm.obj: svm.cpp svm.h
    $(CXX) $(CFLAGS) -c svm.cpp

$(TARGET)\python\svmc.pyd: python\svmc_wrap.c svm.obj $(PYTHON_LIB)
    $(CXX) $(CFLAGS) -I$(PYTHON_INC) -LD $** -Fe$(TARGET)\python\svmc.pyd
# $** means all dependencies

clean:
    -erase /Q *.obj $(TARGET)\. $(TARGET)\python\. 


