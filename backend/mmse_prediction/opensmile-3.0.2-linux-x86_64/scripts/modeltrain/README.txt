
Use the perl script "makemodel.pl" to build an emotion/affect recognition LibSVM model (both SVM and SVR supported, see buildmodel.pl if you want to change parameters).

The script takes either an Arff-file as single argument OR a corpus directory according to <PERSON><PERSON><PERSON>'s emotion class directory standard. In the latter case the script takes a second parameter, which is the openSMILE configuration file to use.

