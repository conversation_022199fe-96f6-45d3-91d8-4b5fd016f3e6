///////////////////////////////////////////////////////////////////////////////////////
///////// > openSMILE configuration file                             //////////////////
/////////   for AVEC 2013                                            //////////////////
/////////   based on avec2011.conf                                   //////////////////
/////////  * written 2013 by <PERSON><PERSON><PERSON> *                         //////////////////
/////////                                                            //////////////////
///////// (c) 2013 TUM, MMK                                          //////////////////
///////// (c) 2014 audEERING UG (limited)                            //////////////////
/////////   All rights reserved, see file COPYING for license terms  //////////////////
///////////////////////////////////////////////////////////////////////////////////////


\{avec2013_lld_core.conf}

;;;;;;;;; output all features in HTK and ARFF format ...

[componentInstances:cComponentManager]
instance[htksink].type=cHtkSink

[htksink:cHtkSink]
reader.dmLevel=lldA_smo;lldA_smo_de;lldB_smo;lldB_smo_de;lld_nzsmo;lld_nzsmo_de
filename=\cm[output_htk(O){default_lld.htk}:output binary htk file for feature data]
parmKind = 9
errorOnNoOutput = 1
append = 0


[componentInstances:cComponentManager]
instance[arffsink].type=cArffSink

[arffsink:cArffSink]
reader.dmLevel=lldA_smo;lldA_smo_de;lldB_smo;lldB_smo_de;lld_nzsmo;lld_nzsmo_de
filename=\cm[output_arff{default_lld.arff}:output arff file for feature data]
append=0
frameIndex=0
frameTime=1
relation=avec2013_lld_per_session
instanceBase=\cm[instname{unlabelled}:name of file which will be saved in arff file]
errorOnNoOutput = 1
class[0].name = unused_target_label
class[0].type = numeric
target[0].all = 0.0

;; more class labels, nor used for now..
;class[0].name = arousal
;class[0].type = numeric
;class[1].name = valence
;class[1].type = numeric
;class[2].type = depression
;class[2].type = numeric
 ; the class label or value for the current instance
;\{labels.inc}
 


