///////////////////////////////////////////////////////////////////////////////////////
///////// > openSMILE configuration file                             //////////////////
/////////   for AVEC 2013                                            //////////////////
/////////   based on avec2011.conf                                   //////////////////
/////////  * written 2013 by <PERSON><PERSON><PERSON> *                         //////////////////
/////////                                                            //////////////////
///////// (c) 2013 TUM, MMK                                          //////////////////
///////// (c) 2014-2019 audEERING                                    //////////////////
/////////   All rights reserved, see file COPYING for license terms  //////////////////
///////////////////////////////////////////////////////////////////////////////////////


\{avec2013_lld_core.conf}
\{avec2013_functionals_core.conf}

;;;;;;;;; output all features....

[componentInstances:cComponentManager]
instance[arffsink].type=cArffSink

[arffsink:cArffSink]
reader.dmLevel=functionalsDur;functionalsA;functionalsNz;functionalsAde;functionalsNzDe;functionalsF0v;functionalsF0p
filename=\cm[output(O){default_functionals.arff}:output arff file for feature data]
append=1
frameIndex=0
frameTime=1
frameTimeAdd=\cm[frameTimeAdd(F){0}:frame time offset (start of segment in seconds)]
relation=\cm[frameModeConf]
instanceName=\cm[instname{unlabelled}:name of file which will be saved in arff file]
errorOnNoOutput = 1
class[0].name = unused_target_label
class[0].type = numeric
target[0].all = 0.0

;; more class labels, nor used for now..
;class[0].name = arousal
;class[0].type = numeric
;class[1].name = valence
;class[1].type = numeric
;class[2].type = depression
;class[2].type = numeric
 ; the class label or value for the current instance
;\{labels.inc}
 


