///////////////////////////////////////////////////////////////////////////////////////
///////// > openSMILE configuration file                             //////////////////
/////////   for AVEC 2013                                            //////////////////
/////////   based on avec2011.conf                                   //////////////////
/////////  * written 2013 by <PERSON><PERSON><PERSON> *                         //////////////////
/////////                                                            //////////////////
///////// (c) 2013 TUM, MMK                                          //////////////////
///////// (c) 2014 audEERING UG (limited)                            //////////////////
/////////   All rights reserved, see file COPYING for license terms  //////////////////
///////////////////////////////////////////////////////////////////////////////////////


\{avec2013_lld_core.conf}

;;;;;;;;; output all features....

[componentInstances:cComponentManager]
instance[htksink].type=cHtkSink

[htksink:cHtkSink]
reader.dmLevel=lldA_smo;lldA_smo_de;lldB_smo;lldB_smo_de;lld_nzsmo;lld_nzsmo_de
filename=\cm[output_htk(O){default_lld.htk}:output binary htk file for feature data]
parmKind = 9
errorOnNoOutput = 1
append = 0


