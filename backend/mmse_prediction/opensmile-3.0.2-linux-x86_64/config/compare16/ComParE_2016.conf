///////////////////////////////////////////////////////////////////////////////////////
///////// > openSMILE configuration file for ComParE <               //////////////////
/////////   updated version of ComParE 2013 set, numerical fixes     //////////////////
/////////                                                            //////////////////
///////// (c) 2014-2016 by audEERING,                                //////////////////
/////////     All rights reserved. See file COPYING for details.    //////////////////
///////////////////////////////////////////////////////////////////////////////////////



;;;;;;; component list ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;

[componentInstances:cComponentManager]
instance[dataMemory].type=cDataMemory
printLevelStats=0


;;;;;;;;;;;;;;;;;;;;;;;;;;;; main section ;;;;;;;;;;;;;;;;;;;;;;;;;;;

\{../shared/standard_wave_input.conf.inc}
\{ComParE_2016_core.lld.conf.inc}
\{ComParE_2016_core.func.conf.inc}

;;;;;;;;; prepare features for standard output module

[componentInstances:cComponentManager]
instance[is13_lldconcat].type=cVectorConcat
instance[is13_llddeconcat].type=cVectorConcat
instance[is13_funcconcat].type=cVectorConcat

[is13_lldconcat:cVectorConcat]
reader.dmLevel = is13_lld_nzsmo;is13_lldA_smo;is13_lldB_smo
writer.dmLevel = lld
includeSingleElementFields = 1

[is13_llddeconcat:cVectorConcat]
reader.dmLevel = is13_lld_nzsmo_de;is13_lldA_smo_de;is13_lldB_smo_de
writer.dmLevel = lld_de
includeSingleElementFields = 1

[is13_funcconcat:cVectorConcat]
reader.dmLevel = is13_functionalsA;is13_functionalsB;is13_functionalsNz;is13_functionalsF0;is13_functionalsLLD;is13_functionalsDelta
writer.dmLevel = func
includeSingleElementFields = 1

\{../shared/standard_data_output.conf.inc}

