
///////////////////////////////////////////////////////////////////////////////////////
///////// > openSMILE configuration file to extract PLP features <   //////////////////
/////////   HTK target kind: PLP_E_D_A_Z, numCeps=5                  //////////////////
/////////                                                            //////////////////
/////////  * written 2010 by Florian <PERSON>ben *                         //////////////////
/////////                                                            //////////////////
///////// (c) audEERING UG (haftungsbeschränkt),                     //////////////////
/////////     All rights reserved.                                  //////////////////
///////////////////////////////////////////////////////////////////////////////////////



///////////////////////////////////////////////////////////////////////////////////////
;
; This section is always required in openSMILE configuration files
;   it configures the componentManager and gives a list of all components which are to be loaded
; The order in which the components are listed should match 
;   the order of the data flow for most efficient processing
;
///////////////////////////////////////////////////////////////////////////////////////
[componentInstances:cComponentManager]
instance[dataMemory].type=cDataMemory
 ; wave file input
instance[waveIn].type=cWaveSource
 ; audio framer
instance[frame].type=cFramer
 ; speech pre-emphasis (on a per frame basis as HTK does it)
instance[pe].type=cVectorPreemphasis
 ; apply a window function to pre-emphasised frames
instance[win].type=cWindower
 ; transform to the frequency domain using FFT
instance[fft].type=cTransformFFT
 ; compute magnitude of the complex fft from the previous component
instance[fftmag].type=cFFTmagphase
 ; compute critical-bands from magnitude spectrum
instance[melspec].type=cMelspec
 ; compute PLP from critical-band spectrum
instance[plp].type=cPlp
instance[cms].type=cFullinputMean
 ; compute log-energy from raw signal frames 
 ; (not windowed, not pre-emphasised: that's the way HTK does it)
instance[energy].type=cEnergy
 ; concat mfcc and energy, so we can compute delta and acceleration 
 ; coefficients of both features at the same tim
instance[cat].type=cVectorConcat
 ; compute delta coefficients from mfcc and energy
instance[delta].type=cDeltaRegression
 ; compute acceleration coefficients from delta coefficients of mfcc and energy
instance[accel].type=cDeltaRegression
 ; write the result to an HTK parameter file
instance[htkout].type=cHtkSink

; run single threaded (nThreads=1)
; NOTE: a single thread is more efficient for processing small files, since multi-threaded processing involves more 
;       overhead during startup, which will make the system slower in the end
nThreads=1
; do not show any internal dataMemory level settings 
; (if you want to see them set the value to 1, 2, 3, or 4, depending on the amount of detail you wish)
printLevelStats=0


/////////////////////////////////////////////////////////////////////////////////////////////
/////////////////////////   component configuration  ////////////////////////////////////////
/////////////////////////////////////////////////////////////////////////////////////////////
; the following sections configure the components listed above
; a help on configuration parameters can be obtained with 
;  SMILExtract -H
; or
;  SMILExtract -H configTypeName (= componentTypeName)
/////////////////////////////////////////////////////////////////////////////////////////////

[waveIn:cWaveSource]
 ; this sets the level this component writes to
 ; the level will be created by this component
 ; no other components may write to a level having the same name
writer.dmLevel=wave
 ; this defines a new commandline option "-I" or "-inputfile", which can be used to specify 
 ; the filename on the commandline instead of having it "hard-coded" in the config file
filename=\cm[inputfile(I){test.wav}:name of input file]
 ; mix stereo files down to mono for analysis
monoMixdown=1

[frame:cFramer]
reader.dmLevel=wave
writer.dmLevel=frames
frameSize = 0.0250
frameStep = 0.010
frameMode = fixed
frameCenterSpecial = left

[pe:cVectorPreemphasis]
reader.dmLevel=frames
writer.dmLevel=framespe
k=0.97
de=0

[win:cWindower]
reader.dmLevel=framespe
writer.dmLevel=winframes
 ; hamming window
winFunc = ham
 ; no gain
gain = 1.0
offset = 0

[fft:cTransformFFT]
reader.dmLevel=winframes
writer.dmLevel=fft

[fftmag:cFFTmagphase]
reader.dmLevel=fft
writer.dmLevel=fftmag

[melspec:cMelspec]
reader.dmLevel=fftmag
writer.dmLevel=melspec
; htk compatible sample value scaling
htkcompatible = 1
nBands = 26
; use power spectrum instead of magnitude spectrum
usePower = 1
lofreq = 0
hifreq = 8000
specScale = mel

[plp:cPlp]
reader.dmLevel=melspec
writer.dmLevel=plp
writer.levelconf.growDyn=1
writer.levelconf.isRb=0
buffersize=1000
firstCC = 1
lpOrder = 5
cepLifter = 22
compression = 0.33
htkcompatible = 1 
doIDFT = 1
doLpToCeps = 1
doLP = 1
doInvLog = 0
doAud = 1
doLog = 0

[cms:cFullinputMean]
reader.dmLevel=plp
writer.dmLevel=plpM

[energy:cEnergy]
reader.dmLevel=frames
writer.dmLevel=energy
writer.levelconf.growDyn=1
writer.levelconf.isRb=0
buffersize=1000
htkcompatible=1
rms = 0
log = 1

[cat:cVectorConcat]
reader.dmLevel=plp;energy
writer.dmLevel=ft0
processArrayFields=0

[delta:cDeltaRegression]
reader.dmLevel=ft0
writer.dmLevel=ft0de
deltawin=2
blocksize=1

[accel:cDeltaRegression]
reader.dmLevel=ft0de
writer.dmLevel=ft0dede
deltawin=2
blocksize=1

  //////////////////////////////////////////////////////////////////////
 ///////////////////  data output configuration  //////////////////////
//////////////////////////////////////////////////////////////////////

; the HTK sink writes data in HTK parameter format
[htkout:cHtkSink]
 ; data from the following dataMemory levels in concattenated
reader.dmLevel=plpM;energy;ft0de;ft0dede
 ; this again defines a commandline option for the output file (see waveIn)
filename=\cm[output(O){mfcc.htk}:name of MFCC output filename (HTK format)]
append=0
 ; PLP_0_D_A  = 11 + 256 + 512 + 8192 = 8971
parmKind=8971

//////---------------------- END -------------------------///////

