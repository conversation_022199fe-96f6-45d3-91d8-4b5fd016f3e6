
 ///////////////////////////////////////////////////////////////////////////
 // openSMILE example configuration file for the signal generator         //
 ///////////////////////////////////////////////////////////////////////////

 ;===== component manager configuration (= list of enabled components!) =====

[componentInstances:cComponentManager]
 // this line configures the default data memory:
instance[dataMemory].type = cDataMemory
instance[signalGenerator].type = cSignalGenerator
instance[waveSink].type = cWaveSink
 // Here you can control the amount of detail displayed for the data memory
  // level configuration. 0 is no information at all, 5 is maximum detail.
printLevelStats = 0
 // You can set the number of parallel threads (experimental):
nThreads = 1

// ============= component configuration sections begin here ==============


  ;;;; default (template) configuration section for component 'cSignalGenerator' ;;;;
[signalGenerator:cSignalGenerator]
writer.dmLevel = sig
 ; 16 kHz wave files
period = 0.0000625
 ; 44.1 kHz wave files
;period = 2.2675736961451247165532879818594e-5
 ; 1 channel...
nFields = 1
 ; sine wave   (use 'white' for Gaussian white noise)
signalType = sine
randSeed = 1
scale = 1
const = 0
frequency = 440
phase = 0
length = 3
 

  ;;;; default (template) configuration section for component 'cWaveSink' ;;;;
[waveSink:cWaveSink]
reader.dmLevel = sig
filename = output.wav
sampleFormat = 16bit
 

// ################### END OF openSMILE CONFIG FILE ######################

