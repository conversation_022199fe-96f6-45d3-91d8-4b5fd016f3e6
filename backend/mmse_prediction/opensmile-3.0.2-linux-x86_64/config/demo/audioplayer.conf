
 ///////////////////////////////////////////////////////////////////////////
  //  openSMILE configuration example for a simple WAVE player //
 ///////////////////////////////////////////////////////////////////////////

 ;===== component manager configuration (= list of enabled components!) =====

[componentInstances:cComponentManager]
 // this line configures the default data memory:
instance[dataMemory].type = cDataMemory
instance[waveSource].type = cWaveSource
instance[portaudioSink].type = cPortaudioSink
 // Here you can control the amount of detail displayed for the data memory
  // level configuration. 0 is no information at all, 5 is maximum detail.
printLevelStats = 0
 // You can set the number of parallel threads (experimental):
nThreads = 1

// ============= component configuration sections begin here ==============


  ;;;; default (template) configuration section for component 'cWaveSource' ;;;;
[waveSource:cWaveSource]
writer.dmLevel = wave
period = 0
filename = \cm[inputfile(I){input.wav}:name of wave (PCM) input file]
monoMixdown = 0
start = 0
end = -1
endrel = 0
noHeader = 0
blocksize_sec=0.1 

  ;;;; default (template) configuration section for component 'cPortaudioSink' ;;;;
[portaudioSink:cPortaudioSink]
reader.dmLevel = wave
monoMixdown = 0
device = -1
listDevices = 0
sampleRate = 0
audioBuffersize_sec = 0.20000
 

// ################### END OF openSMILE CONFIG FILE ######################

