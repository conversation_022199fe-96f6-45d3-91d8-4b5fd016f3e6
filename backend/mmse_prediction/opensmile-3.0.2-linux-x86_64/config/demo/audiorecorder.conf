
 ///////////////////////////////////////////////////////////////////////////
  // openSMILE configuration example for a simple wave (PCM) recorder //
 ///////////////////////////////////////////////////////////////////////////

 ;===== component manager configuration (= list of enabled components!) =====

[componentInstances:cComponentManager]
 // this line configures the default data memory:
instance[dataMemory].type = cDataMemory
instance[portaudioSource].type = cPortaudioSource
instance[waveSink].type = cWaveSink
 // Here you can control the amount of detail displayed for the data memory
  // level configuration. 0 is no information at all, 5 is maximum detail.
printLevelStats = 0
 // You can set the number of parallel threads (experimental):
nThreads = 1

// ============= component configuration sections begin here ==============


  ;;;; default (template) configuration section for component 'cPortaudioSource' ;;;;
[portaudioSource:cPortaudioSource]
writer.dmLevel = wave
monoMixdown = 0
 ; -1 is the default device
device = \cm[device{-1}:portaudio device to use for recording, see -listDevices option]
listDevices = \cm[listDevices{0}:add -listDevices 1 to the command-line to see a list available of portaudio devices]
sampleRate = \cm[sampleRate{44100}:set the sampling rate in Hz for recording]
channels = \cm[channels{2}:set the number of audio channels to record]
nBits = 16
audioBuffersize_sec = 0.050000
buffersize_sec=2.0
 

  ;;;; default (template) configuration section for component 'cWaveSink' ;;;;
[waveSink:cWaveSink]
reader.dmLevel = wave
blocksize=500
filename = \cm[outputfile(O){output.wav}:name of output wave file]
sampleFormat = 16bit
 

// ################### END OF openSMILE CONFIG FILE ######################

