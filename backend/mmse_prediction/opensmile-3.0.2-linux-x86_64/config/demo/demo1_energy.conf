
 ///////////////////////////////////////////////////////////////////////////
 // openSMILE configuration file "demo1"				  //
 ///////////////////////////////////////////////////////////////////////////

 ;===== component manager configuration (= list of enabled components!) =====

[componentInstances:cComponentManager]
 // this line configures the default data memory:
instance[dataMemory].type = cDataMemory
instance[waveSource].type = cWaveSource
instance[framer].type = cFramer
instance[energy].type = cEnergy
instance[csvSink].type = cCsvSink
 // Here you can control the amount of detail displayed for the data memory
 // level configuration. 0 is no information at all, 5 is maximum detail.
printLevelStats = 0
 // You can set the number of parallel threads (experimental):
nThreads = 1

// ============= component configuration sections begin here ==============


  ;;;; default (template) configuration section for component 'cWaveSource' ;;;;
[waveSource:cWaveSource]
writer.dmLevel = wave
period = 0
; filename = input.wav
filename = \cm[inputfile(I){input.wav}:file name of the input wave file]
monoMixdown = 1 

  ;;;; default (template) configuration section for component 'cFramer' ;;;;
[framer:cFramer]
reader.dmLevel = wave
writer.dmLevel = waveframes
copyInputName = 1
frameMode = fixed
frameSize = 0.025000
frameStep = 0.010000
frameCenterSpecial = left
noPostEOIprocessing = 1

  ;;;; default (template) configuration section for component 'cEnergy' ;;;;
[energy:cEnergy]
reader.dmLevel = waveframes
writer.dmLevel = energy
nameAppend = energy
copyInputName = 1
processArrayFields = 0
htkcompatible = 0
rms = 0
log = 1
 

  ;;;; default (template) configuration section for component 'cCsvSink' ;;;;
[csvSink:cCsvSink]
reader.dmLevel = energy
; filename = myenergy.csv
filename = \cm[outputfile(O){output.csv}:file name of the output CSV file]
delimChar = ;
append = 0
timestamp = 1
number = 1
printHeader = 1
 

// ################### END OF openSMILE CONFIG FILE ######################

