///////////////////////////////////////////////////////////////////////////////////////
///////// > openSMILE configuration file                             //////////////////
/////////   for AVEC 2013                                            //////////////////
/////////   based on avec2011.conf                                   //////////////////
/////////  * written 2013 by <PERSON><PERSON><PERSON> *                         //////////////////
/////////                                                            //////////////////
///////// (c) 2013-2016 by audEERING.                                //////////////////
/////////     All rights reserved. See file COPYING for details.     //////////////////
///////////////////////////////////////////////////////////////////////////////////////


\{avec2013_lld_core.conf.inc}
\{avec2013_functionals_core.conf.inc}

;;;;;;;;; output all features....

[componentInstances:cComponentManager]
instance[avec2013_lldconcat].type=cVectorConcat
instance[avec2013_llddeconcat].type=cVectorConcat
instance[avec2013_funcconcat].type=cVectorConcat

[avec2013_lldconcat:cVectorConcat]
reader.dmLevel = lldA_smo;lldB_smo;lld_nzsmo
writer.dmLevel = lld
includeSingleElementFields = 1

[avec2013_llddeconcat:cVectorConcat]
reader.dmLevel = lldA_smo_de;lldB_smo_de;lld_nzsmo_de
writer.dmLevel = lld_de
includeSingleElementFields = 1

[avec2013_funcconcat:cVectorConcat]
reader.dmLevel = functionalsDur;functionalsA;functionalsNz;functionalsAde;functionalsNzDe;functionalsF0v;functionalsF0p
writer.dmLevel = func
includeSingleElementFields = 1

\{../shared/standard_data_output.conf.inc}

 


