///////////////////////////////////////////////////////////////////////////////////////
///////// > openSMILE configuration file, Geneva feature set <       //////////////////
/////////                                                            //////////////////
///////// (c) 2014 by audEERING                                      //////////////////
/////////     All rights reserved. See file COPYING for details.     //////////////////
///////////////////////////////////////////////////////////////////////////////////////

;;;;;;; component list ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;

[componentInstances:cComponentManager]
instance[dataMemory].type=cDataMemory
printLevelStats=0

;;;;;;;;;;;;;;;;;;;;;;;;;;;; main section ;;;;;;;;;;;;;;;;;;;;;;;;;;;

\{../../shared/standard_wave_input.conf.inc}
\{GeMAPSv01a_core.lld.conf.inc}
\{GeMAPSv01a_core.func.conf.inc}

;;;;;;;; prepare for standard data output ;;;;;;;;;;;;;;;;;;;;;

[componentInstances:cComponentManager]
instance[lldconcat].type=cVectorConcat
instance[funcconcat].type=cVectorConcat

[lldconcat:cVectorConcat]
reader.dmLevel = gemapsv01a_lldsetE_smo;gemapsv01a_lldsetF_smo
writer.dmLevel = lld
includeSingleElementFields = 1

[funcconcat:cVectorConcat]
reader.dmLevel = gemapsv01a_functionalsF0;gemapsv01a_functionalsLoudness;gemapsv01a_functionalsMeanStddevVoiced;gemapsv01a_functionalsMeanSpectralUnvoiced;gemapsv01a_temporalSet
writer.dmLevel = func
includeSingleElementFields = 1

\{../../shared/standard_data_output_no_lld_de.conf.inc}


