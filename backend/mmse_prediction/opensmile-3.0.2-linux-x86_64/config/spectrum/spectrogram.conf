[componentInstances:cComponentManager]
instance[dataMemory].type=cDataMemory

\{../shared/standard_wave_input.conf.inc}

[componentInstances:cComponentManager]
instance[frame].type=cFramer
instance[win].type=cWindower
instance[fft].type=cTransformFFT
instance[fftmag].type=cFFTmagphase

[frame:cF<PERSON><PERSON>]
reader.dmLevel=wave
writer.dmLevel=frames
frameSize = \cm[frameSize{0.0250}:Frame size in seconds]
frameStep = \cm[frameStep{0.010}:Frame step in seconds]
frameMode = fixed
frameCenterSpecial = left

[win:cWindower]
reader.dmLevel=frames
writer.dmLevel=winframes
 ; hamming window
winFunc = ham
 ; no gain
gain = 1.0
offset = 0

[fft:cTransformFFT]
reader.dmLevel=winframes
writer.dmLevel=fft

[fftmag:cFFTmagphase]
reader.dmLevel=fft
writer.dmLevel=lld
dBpsd = \cm[dB{0}: Flag to trigger the computation of the power spectral density in dB]

\{../shared/standard_data_output_lldonly.conf.inc}
