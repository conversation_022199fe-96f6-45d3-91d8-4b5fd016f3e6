
;;;;; all the components we require are listed here:
[componentInstances:cComponentManager]
 // this line configures the default data memory:
instance[dataMemory].type=cDataMemory
instance[liveIn].type=cPortaudioSource
/*
instance[fr1].type=cFramer
*/
instance[energy].type=cEnergy
instance[frame].type=cFramer
instance[int].type=cIntensity
instance[win].type=cWindower
instance[fft].type=cTransformFFT
instance[fftmp].type=cFFTmagphase
instance[scale].type=cSpecScale
instance[shs].type=cPitchShs
instance[smooth].type=cPitchSmoother
instance[smo].type=cContourSmoother

instance[gui2].type = cSimpleVisualiserGUI

printLevelStats=0
nThreads=1
;execDebug=1


;;;;;;;;;;;;;;;;; below the config sections for the components start ;;;;;;;;;

[liveIn:cPortaudioSource]
writer.dmLevel=wave
; audio buffersize must be > then buffersize of framer to avoid 100% cpu hog
;audioBuffersize = 2500
sampleRate=16000
monoMixdown=1


/////////

/*
[fr1:cFramer]
reader.dmLevel=wave
writer.dmLevel=frames
buffersize_sec=1.0
frameSize = 0.0250
frameStep = 0.010

*/
[energy:cEnergy]
reader.dmLevel=outp
writer.dmLevel=energy
buffersize_sec=1.0
nameAppend=energy
rms=1
log=1
;escaleLog = 0.1
htkcompatible=0



[frame:cFramer]
reader.dmLevel=wave
writer.dmLevel=outp
frameSize = 0.070
frameStep = 0.010
frameCenterSpecial = left

[int:cIntensity]
reader.dmLevel = outp
writer.dmLevel = intens
// nameAppend =
copyInputName = 1
processArrayFields = 1
intensity = 0
loudness = 1

[win:cWindower]
reader.dmLevel=outp
writer.dmLevel=win
winFunc=gauss
gain=1.0
sigma=0.4

[fft:cTransformFFT]
reader.dmLevel=win
writer.dmLevel=fftc

[fftmp:cFFTmagphase]
reader.dmLevel=fftc
writer.dmLevel=fftmag

[scale:cSpecScale]
reader.dmLevel=fftmag
writer.dmLevel=hps
// nameAppend =
copyInputName = 1
processArrayFields = 0
scale=octave
sourceScale = lin
// logScaleBase = 2
// logSourceScaleBase = 2
// firstNote = 55
interpMethod = spline
minF = 25
maxF = -1
nPointsTarget = 0
specSmooth = 1
specEnhance = 1
auditoryWeighting = 1 

[shs:cPitchShs]
reader.dmLevel=hps
writer.dmLevel=pitchShs
// nameAppend =
copyInputName = 1
processArrayFields = 0
maxPitch = 400
minPitch = 52
nCandidates = 6
scores = 1
voicing = 1
F0C1 = 0
voicingC1 = 0
F0raw = 1
voicingClip = 1
voicingCutoff = 0.75000
inputFieldSearch = Mag_octScale
octaveCorrection = 0
nHarmonics = 15
compressionFactor = 0.850000

[smooth:cPitchSmoother]
reader.dmLevel=pitchShs
writer.dmLevel=pitch
// nameAppend =
copyInputName = 1
processArrayFields = 0
medianFilter0 = 0
postSmoothing = 0
postSmoothingMethod = simple
 ; note: octave correction is too agressive, thus we disable it..
octaveCorrection = 0
F0final = 1
F0finalEnv = 0
no0f0 = 0
voicingFinalClipped = 0
voicingFinalUnclipped = 1
F0raw = 0
voicingC1 = 0
voicingClip = 0

[smo:cContourSmoother]
reader.dmLevel = pitch;intens
writer.dmLevel = smo
nameAppend = sma
copyInputName = 1
noPostEOIprocessing = 0
smaWin = 3

[gui2:cSimpleVisualiserGUI]
reader.dmLevel=smo;energy
;reader.dmLevel=fftmag
action=moving2dplot
;action=movingMatplot
;fullVectorAsInput = 1
inputs=F0final_sma;LOGenergy
;inputscaleFullinput = 125.0
;inputoffsetFullinput = 0.0
inputScale=0.5;20
inputOffset=0.0;12.0
inputColours=blue;red

