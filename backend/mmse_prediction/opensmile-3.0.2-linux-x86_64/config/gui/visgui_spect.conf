
;;;;; all the components we require are listed here:
[componentInstances:cComponentManager]
 // this line configures the default data memory:
instance[dataMemory].type=cDataMemory
instance[liveIn].type=cPortaudioSource
instance[energy].type=cEnergy
instance[frame].type=c<PERSON>ramer
instance[int].type=cIntensity
instance[win].type=cWindower
instance[fft].type=cTransformFFT
instance[fftmp].type=cFFTmagphase

instance[gui2].type = cSimpleVisualiserGUI


printLevelStats=0
nThreads=1
;execDebug=1


;;;;;;;;;;;;;;;;; below the config sections for the components start ;;;;;;;;;

[liveIn:cPortaudioSource]
writer.dmLevel=wave
; audio buffersize must be > then buffersize of framer to avoid 100% cpu hog
;audioBuffersize = 2500
sampleRate=16000
monoMixdown=1


/////////

[energy:cEnergy]
reader.dmLevel=outp
writer.dmLevel=energy
buffersize_sec=1.0
nameAppend=energy
rms=1
log=1
;escaleLog = 0.1
htkcompatible=0



[frame:cFramer]
reader.dmLevel=wave
writer.dmLevel=outp
frameSize = 0.030
frameStep = 0.020
frameCenterSpecial = left

[int:cIntensity]
reader.dmLevel = outp
writer.dmLevel = intens
// nameAppend =
copyInputName = 1
processArrayFields = 1
intensity = 0
loudness = 1

[win:cWindower]
reader.dmLevel=outp
writer.dmLevel=win
winFunc=gauss
gain=1.0
sigma=0.4

[fft:cTransformFFT]
reader.dmLevel=win
writer.dmLevel=fftc

[fftmp:cFFTmagphase]
reader.dmLevel=fftc
writer.dmLevel=fftmag
nameAppend = fftspec

[scale:cSpecScale]
reader.dmLevel=fftmag
writer.dmLevel=hps
// nameAppend =
copyInputName = 1
processArrayFields = 0
scale=oct
sourceScale = lin
// logScaleBase = 2
// logSourceScaleBase = 2
// firstNote = 55
interpMethod = spline
minF = 25
maxF = -1
nPointsTarget = 0
specSmooth = 1
specEnhance = 1
auditoryWeighting = 1 

[melspec:cMelspec]
reader.dmLevel = fftmag
writer.dmLevel = mel
nameAppend = melspec
copyInputName = 1
processArrayFields = 1
nBands = 26
lofreq = 20
hifreq = 8000
usePower = 1
htkcompatible = 0
specScale = mel

[mfcc:cMfcc]
reader.dmLevel = mel
writer.dmLevel = mfcc
// nameAppend =
copyInputName = 1
processArrayFields = 1
firstMfcc = 1
lastMfcc = 21
melfloor = 0.000000
cepLifter = 22
htkcompatible = 0

[smo:cContourSmoother]
reader.dmLevel = pitch;intens
writer.dmLevel = smo
nameAppend = sma
copyInputName = 1
noPostEOIprocessing = 0
smaWin = 3

[gui2:cSimpleVisualiserGUI]
reader.dmLevel=fftmag
matMultiplier = 1
action=movingMatplot
fullVectorAsInput = 1
inputscaleFullinput = 80
inputoffsetFullinput = 0.0

