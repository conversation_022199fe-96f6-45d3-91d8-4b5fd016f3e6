
;;;;; all the components we require are listed here:
[componentInstances:cComponentManager]
 // this line configures the default data memory:
instance[dataMemory].type=cDataMemory
instance[liveIn].type=cPortaudioSource
instance[frame].type=c<PERSON>ramer
instance[win].type=cWindower
instance[fft].type=cTransformFFT
instance[fftmp].type=cFFTmagphase
instance[tonespec].type = cTonespec
instance[chroma].type = cChroma
instance[gui2].type = cSimpleVisualiserGUI


printLevelStats=0
nThreads=1
;execDebug=1


;;;;;;;;;;;;;;;;; below the config sections for the components start ;;;;;;;;;

[liveIn:cPortaudioSource]
writer.dmLevel=wave
; audio buffersize must be > then buffersize of framer to avoid 100% cpu hog
;audioBuffersize = 2500
sampleRate=16000
monoMixdown=1


/////////


[frame:cFramer]
reader.dmLevel=wave
writer.dmLevel=outp
frameSize = 0.064
frameStep = 0.010
frameCenterSpecial = left

[win:cWindower]
reader.dmLevel=outp
writer.dmLevel=win
winFunc=gauss
gain=1.0
sigma=0.4

[fft:cTransformFFT]
reader.dmLevel=win
writer.dmLevel=fftc

[fftmp:cFFTmagphase]
reader.dmLevel=fftc
writer.dmLevel=fftmag
nameAppend = fftspec

  ;;;; default (template) configuration section for component 'cTonespec' ;;;;
[tonespec:cTonespec]
reader.dmLevel = fftmag
writer.dmLevel = tonespec
nameAppend = note
copyInputName = 1
processArrayFields = 0
nOctaves = 6
firstNote = 55
filterType = gau
usePower = 1
dbA = 1
 

  ;;;; default (template) configuration section for component 'cChroma' ;;;;
[chroma:cChroma]
reader.dmLevel = tonespec
writer.dmLevel = chroma
nameAppend = chroma
copyInputName = 0
processArrayFields = 0
octaveSize = 12
 
[gui2:cSimpleVisualiserGUI]
reader.dmLevel=tonespec
matMultiplier = 4
action=movingMatplot
fullVectorAsInput = 1
inputscaleFullinput = 40
inputoffsetFullinput = 0.0

