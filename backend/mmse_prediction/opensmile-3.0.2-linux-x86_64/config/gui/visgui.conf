
;;;;; all the components we require are listed here:
[componentInstances:cComponentManager]
 // this line configures the default data memory:
instance[dataMemory].type=cDataMemory
instance[liveIn].type=cPortaudioSource
instance[fr1].type=c<PERSON><PERSON>er
instance[energy].type=cEnergy
;instance[gui].type = cSimpleVisualiserGUI
instance[gui2].type = cSimpleVisualiserGUI

printLevelStats=0
nThreads=1


;;;;;;;;;;;;;;;;; below the config sections for the components start ;;;;;;;;;

[liveIn:cPortaudioSource]
writer.dmLevel=wave
; audio buffersize must be > then buffersize of framer to avoid 100% cpu hog
;audioBuffersize = 2500
sampleRate=16000
monoMixdown=1


/////////


[fr1:cFramer]
reader.dmLevel=wave
writer.dmLevel=frames
buffersize_sec=1.0
frameSize = 0.0250
frameStep = 0.010


[energy:cEnergy]
reader.dmLevel=frames
writer.dmLevel=energy
buffersize_sec=1.0
nameAppend=energy
rms=1
log=1
;escaleLog = 0.1
htkcompatible=0



[gui:cSimpleVisualiserGUI]
reader.dmLevel=energy
action=movingplot
inputs=RMSenergy
inputScale=200.0
inputOffset=0.0

[gui2:cSimpleVisualiserGUI]
reader.dmLevel=energy
action=movingplot
inputs=LOGenergy
inputScale=10.0
inputOffset=10.0

