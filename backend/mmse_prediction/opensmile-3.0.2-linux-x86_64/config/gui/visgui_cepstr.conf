
;;;;; all the components we require are listed here:
[componentInstances:cComponentManager]
 // this line configures the default data memory:
instance[dataMemory].type=cDataMemory
instance[liveIn].type=cPortaudioSource
instance[energy].type=cEnergy
instance[frame].type=cFramer
instance[int].type=cIntensity
instance[win].type=cWindower
instance[fft].type=cTransformFFT
instance[fftmp].type=cFFTmagphase
instance[cep].type=cAcf
instance[vectorOperation].type=cVectorOperation
instance[gui2].type = cSimpleVisualiserGUI


printLevelStats=0
nThreads=1
;execDebug=1


;;;;;;;;;;;;;;;;; below the config sections for the components start ;;;;;;;;;

[liveIn:cPortaudioSource]
writer.dmLevel=wave
; audio buffersize must be > then buffersize of framer to avoid 100% cpu hog
;audioBuffersize = 2500
sampleRate=16000
monoMixdown=1


/////////

[energy:cEnergy]
reader.dmLevel=outp
writer.dmLevel=energy
buffersize_sec=1.0
nameAppend=energy
rms=1
log=1
;escaleLog = 0.1
htkcompatible=0



[frame:cFramer]
reader.dmLevel=wave
writer.dmLevel=outp
frameSize = 0.030
frameStep = 0.020
frameCenterSpecial = left

[int:cIntensity]
reader.dmLevel = outp
writer.dmLevel = intens
// nameAppend =
copyInputName = 1
processArrayFields = 1
intensity = 0
loudness = 1

[win:cWindower]
reader.dmLevel=outp
writer.dmLevel=win
winFunc=gauss
gain=1.0
sigma=0.4

[fft:cTransformFFT]
reader.dmLevel=win
writer.dmLevel=fftc

[fftmp:cFFTmagphase]
reader.dmLevel=fftc
writer.dmLevel=fftmag
nameAppend = fftspec

[cep:cAcf]
reader.dmLevel=fftmag
writer.dmLevel=cepstrum
cepstrum=1


[vectorOperation:cVectorOperation]
reader.dmLevel = cepstrum
writer.dmLevel = logcepstrum
// nameAppend =
copyInputName = 1
processArrayFields = 1
operation = exp
param1 = 1
param2 = 1
logfloor = 0.0000001
powOnlyPos = 0

[gui2:cSimpleVisualiserGUI]
reader.dmLevel=cepstrum
matMultiplier = 1
action=movingMatplot
fullVectorAsInput = 1
inputscaleFullinput = 2
inputoffsetFullinput = 0.0

