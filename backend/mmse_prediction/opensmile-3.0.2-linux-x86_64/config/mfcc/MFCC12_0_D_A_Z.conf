///////////////////////////////////////////////////////////////////////////////////////
///////// > openSMILE configuration file to extract MFCC features <  //////////////////
/////////   HTK target kind: MFCC_0_D_A_Z, numCeps=12                //////////////////
/////////                                                            //////////////////
/////////  * written 2009 by Florian <PERSON>ben *                         //////////////////
/////////                                                            //////////////////
///////// (c) audEERING UG (haftungsbeschränkt),                     //////////////////
/////////     All rights reserved.                                  //////////////////
///////////////////////////////////////////////////////////////////////////////////////



///////////////////////////////////////////////////////////////////////////////////////
;
; This section is always required in openSMILE configuration files
;   it configures the componentManager and gives a list of all components which are to be loaded
; The order in which the components are listed should match 
;   the order of the data flow for most efficient processing
;
///////////////////////////////////////////////////////////////////////////////////////
[componentInstances:cComponentManager]
instance[dataMemory].type=cDataMemory
 ; wave file input
instance[waveIn].type=cWaveSource
 ; audio framer
instance[frame].type=cFramer
 ; speech pre-emphasis (on a per frame basis as HTK does it)
instance[pe].type=cVectorPreemphasis
 ; apply a window function to pre-emphasised frames
instance[win].type=cWindower
 ; transform to the frequency domain using FFT
instance[fft].type=cTransformFFT
 ; compute magnitude of the complex fft from the previous component
instance[fftmag].type=cFFTmagphase
 ; compute Mel-bands from magnitude spectrum
instance[melspec].type=cMelspec
 ; compute MFCC from Mel-band spectrum
instance[mfcc].type=cMfcc
 ; "cepstral" mean subtraction of mfcc, delta, and accel coefficients
instance[cms].type=cFullinputMean
 ; delta and accel of MFCC
instance[delta1].type=cDeltaRegression
instance[delta2].type=cDeltaRegression
 ; write the result to an HTK parameter file
instance[htkout].type=cHtkSink

; run single threaded (nThreads=1)
; NOTE: a single thread is more efficient for processing small files, since multi-threaded processing involves more 
;       overhead during startup, which will make the system slower in the end
nThreads=1
; do not show any internal dataMemory level settings 
; (if you want to see them set the value to 1, 2, 3, or 4, depending on the amount of detail you wish)
printLevelStats=0


/////////////////////////////////////////////////////////////////////////////////////////////
/////////////////////////   component configuration  ////////////////////////////////////////
/////////////////////////////////////////////////////////////////////////////////////////////
; the following sections configure the components listed above
; a help on configuration parameters can be obtained with 
;  SMILExtract -H
; or
;  SMILExtract -H configTypeName (= componentTypeName)
/////////////////////////////////////////////////////////////////////////////////////////////

[waveIn:cWaveSource]
 ; this sets the level this component writes to
 ; the level will be created by this component
 ; no other components may write to a level having the same name
writer.dmLevel=wave
 ; this defines a new commandline option "-I" or "-inputfile", which can be used to specify 
 ; the filename on the commandline instead of having it "hard-coded" in the config file
filename=\cm[inputfile(I){test.wav}:name of input file]
 ; mix stereo files down to mono for analysis
monoMixdown=1

[frame:cFramer]
reader.dmLevel=wave
writer.dmLevel=frames
noPostEOIprocessing = 1
copyInputName = 1
frameSize = 0.0250
frameStep = 0.010
frameMode = fixed
frameCenterSpecial = left

[pe:cVectorPreemphasis]
reader.dmLevel=frames
writer.dmLevel=framespe
k=0.97
de = 0

[win:cWindower]
reader.dmLevel=framespe
writer.dmLevel=winframes
copyInputName = 1
processArrayFields = 1
 ; hamming window
winFunc = ham
 ; no gain
gain = 1.0
offset = 0

[fft:cTransformFFT]
reader.dmLevel=winframes
writer.dmLevel=fft
copyInputName = 1
processArrayFields = 1
inverse = 0

[fftmag:cFFTmagphase]
reader.dmLevel=fft
writer.dmLevel=fftmag
copyInputName = 1
processArrayFields = 1
inverse = 0
magnitude = 1
phase = 0

[melspec:cMelspec]
reader.dmLevel=fftmag
writer.dmLevel=melspec
copyInputName = 1
processArrayFields = 1
; htk compatible sample value scaling
htkcompatible = 1
nBands = 26
; use power spectrum instead of magnitude spectrum
usePower = 1
lofreq = 0
hifreq = 8000
specScale = mel
inverse = 0

[mfcc:cMfcc]
reader.dmLevel=melspec
writer.dmLevel=mfcc
writer.levelconf.growDyn=1
writer.levelconf.isRb=0
buffersize=1000
copyInputName = 1
processArrayFields = 1
firstMfcc = 0
lastMfcc  = 12
cepLifter = 22.0
htkcompatible = 1

[delta1:cDeltaRegression]
reader.dmLevel=mfcc
writer.dmLevel=mfccD
writer.levelconf.growDyn=1
writer.levelconf.isRb=0
buffersize=1000
nameAppend = de
copyInputName = 1
noPostEOIprocessing = 0
deltawin=2
blocksize=1

[delta2:cDeltaRegression]
reader.dmLevel=mfccD
writer.dmLevel=mfccA
writer.levelconf.growDyn=1
writer.levelconf.isRb=0
buffersize=1000
nameAppend = de
copyInputName = 1
noPostEOIprocessing = 0
deltawin=2
blocksize=1

[cms:cFullinputMean]
reader.dmLevel=mfcc
writer.dmLevel=mfccM

  //////////////////////////////////////////////////////////////////////
 ///////////////////  data output configuration  //////////////////////
//////////////////////////////////////////////////////////////////////

; the HTK sink writes data in HTK parameter format
[htkout:cHtkSink]
 ; data from the following dataMemory levels in concattenated
 ; to form the output vectors
reader.dmLevel=mfccM;mfccD;mfccA
 ; this again defines a commandline option for the output file (see waveIn)
filename=\cm[output(O){mfcc.htk}:name of MFCC output filename (HTK format)]
append=0
 ; MFCC_0_D_A_Z = 6+256+512+2048+8192 = 2886
parmKind=11014





