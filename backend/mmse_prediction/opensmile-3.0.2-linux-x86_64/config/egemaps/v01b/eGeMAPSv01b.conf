///////////////////////////////////////////////////////////////////////////////////////
///////// > openSMILE configuration file, Geneva feature set <       //////////////////
/////////                                                            //////////////////
///////// (c) 2014, 2020 by audEERING                                //////////////////
/////////     All rights reserved. See file COPYING for details.     //////////////////
///////////////////////////////////////////////////////////////////////////////////////

;;;;;;; component list ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;

[componentInstances:cComponentManager]
instance[dataMemory].type=cDataMemory
printLevelStats=0

;;;;;;;;;;;;;;;;;;;;;;;;;;;; main section ;;;;;;;;;;;;;;;;;;;;;;;;;;;

\{../../shared/standard_wave_input.conf.inc}
\{../../gemaps/v01b/GeMAPSv01b_core.lld.conf.inc}
\{eGeMAPSv01b_core.lld.conf.inc}
\{../../gemaps/v01b/GeMAPSv01b_core.func.conf.inc}
\{eGeMAPSv01b_core.func.conf.inc}

[componentInstances:cComponentManager]
instance[lldconcat].type=cVectorConcat
instance[funcconcat].type=cVectorConcat

[lldconcat:cVectorConcat]
reader.dmLevel = egemapsv01b_lldsetE_smo;gemapsv01b_lldsetF_smo
writer.dmLevel = lld
includeSingleElementFields = 1

[funcconcat:cVectorConcat]
reader.dmLevel = gemapsv01b_functionalsF0;gemapsv01b_functionalsLoudness;egemapsv01b_functionalsMeanStddevZ;egemapsv01b_functionalsMeanStddevVoiced;egemapsv01b_functionalsMeanUnvoiced;gemapsv01b_temporalSet;egemapsv01b_leq
writer.dmLevel = func
includeSingleElementFields = 1

\{../../shared/standard_data_output_no_lld_de.conf.inc}

