///////////////////////////////////////////////////////////////////////////////////////
///////// audio features for multi-modal speaker trait detection     /////////////////
/////////                                                            //////////////////
///////// (c) audEERING UG (haftungsbeschränkt),                     //////////////////
/////////     All rights reserved.                                  //////////////////
///////////////////////////////////////////////////////////////////////////////////////

 ;===== component manager configuration (= list of enabled components!) =====

[componentInstances:cComponentManager]
 // this line configures the default data memory:
instance[dataMemory].type = cDataMemory
// AUDIO
instance[waveIn].type=cWaveSource
 ;;; 40 ms frames features:
instance[fr40].type=cFramer
instance[w40].type=cWindower
instance[fft40].type=cTransformFFT
instance[fftmagphase40].type=cFFTmagphase
 ; SHS Pitch:
instance[scale].type=cSpecScale
instance[pitchShs].type=cPitchShs
instance[pitchSmooth].type=cPitchSmoother
instance[pitchJitter].type=cPitchJitter
instance[pitchSmooth2].type=cPitchSmoother
instance[res].type=cSpecResample

 ;;; 25 ms frames features:
instance[fr25].type=cFramer
instance[pe].type=cVectorPreemphasis
instance[win].type=cWindower
instance[fft].type=cTransformFFT
instance[fftmagphase].type=cFFTmagphase
 ; mfcc
instance[mspec].type=cMelspec
instance[mfcc].type=cMfcc
 ; log mel frequency bands (mfb)
instance[mspec2].type=cMelspec
instance[vo].type=cVectorOperation
instance[lpc].type=cLpc
 ; Line Spectral Frequencies
instance[lsp].type=cLsp
 ; Loudness (narrow-band approximation)
instance[intens].type=cIntensity
 ;;; all LLD concattenated and smoothed using a moving average filter
instance[lld].type=cContourSmoother
instance[lld2].type=cContourSmoother
 ; delta coefficients of LLD
instance[delta1].type=cDeltaRegression
instance[delta2].type=cDeltaRegression
 ;;; functionals over FULL input (e.g. turns)
instance[functL1].type=cFunctionals
instance[functL1nz].type=cFunctionals

 ;;; write/append ONE instance to a Weka ARFF file
instance[arffsink].type=cArffSink

 // Here you can control the amount of detail displayed for the data memory
 // level configuration. 0 is no information at all, 5 is maximum detail.
printLevelStats = 0
 // You can set the number of parallel threads (experimental):
nThreads = 1

// ============= component configuration sections begin here ==============

// AUDIO PART

[waveIn:cWaveSource]
 ; this sets the level this component writes to
 ; the level will be created by this component
 ; no other components may write to a level having the same name
writer.dmLevel=wave
 ; this defines a new commandline option "-I" or "-inputfile", which can be used to specify 
 ; the filename on the commandline instead of having it "hard-coded" in the config file
filename=\cm[inputfile(A){test.wav}:name of input file]
 ; mix stereo files down to mono for analysis
monoMixdown=1

[fr40:cFramer]
reader.dmLevel=wave
writer.dmLevel=frames40
frameSize = 0.060
frameStep = 0.010
frameCenterSpecial = left

[w40:cWindower]
reader.dmLevel=frames40
writer.dmLevel=win40frame
winFunc = gauss
sigma=0.25
gain = 1.0


[fft40:cTransformFFT]
reader.dmLevel=win40frame
writer.dmLevel=fftc40

[fftmagphase40:cFFTmagphase]
reader.dmLevel=fftc40
writer.dmLevel=fftmag40


[scale:cSpecScale]
reader.dmLevel=fftmag40
writer.dmLevel=hps
scale=log
 ; octave scale
logScaleBase=2

[pitchShs:cPitchShs]
reader.dmLevel=hps
writer.dmLevel=pitchShs
F0raw = 0
voicingClip = 0
voicingC1=0
scores=1
voicing=1
nCandidates=3
octaveCorrection=0

[pitchSmooth:cPitchSmoother]
reader.dmLevel=pitchShs
writer.dmLevel=pitch
F0raw = 0
F0final = 0
F0finalEnv = 1
voicingFinalUnclipped = 1
medianFilter0 = 0
postSmoothingMethod = simple
;simple
octaveCorrection = 0
writer.levelconf.nT=10
;writer.levelconf.noHang=2
writer.levelconf.isRb=0
writer.levelconf.growDyn=1

[csvsink:cCsvSink]
reader.dmLevel=pitch;pitchA
filename=pitchdbg.csv

[pitchSmooth2:cPitchSmoother]
reader.dmLevel=pitchShs
writer.dmLevel=pitchF
F0raw = 0
F0final = 1
F0finalEnv = 0
voicingFinalUnclipped = 0
medianFilter0 = 0
postSmoothingMethod = simple
octaveCorrection = 0
writer.levelconf.nT=10
;writer.levelconf.noHang=2
writer.levelconf.isRb=0
writer.levelconf.growDyn=1

 ;;;; default (template) configuration section for component 'cPitchJitter' ;;;;
[pitchJitter:cPitchJitter]
reader.dmLevel = wave
writer.dmLevel = jitter
// nameAppend =
copyInputName = 1
F0reader.dmLevel = pitchF
F0field = F0final
searchRangeRel = 0.250000
jitterLocal = 1
jitterDDP = 1
jitterLocalEnv = 0
jitterDDPEnv = 0
shimmerLocal = 1
shimmerLocalEnv = 0
onlyVoiced = 0
;periodLengths = 0
;periodStarts = 0


[fr25:cFramer]
reader.dmLevel=wave
writer.dmLevel=frames
frameSize = 0.025
frameStep = 0.010
frameCenterSpecial = left

[pe:cVectorPreemphasis]
reader.dmLevel=frames
writer.dmLevel=framespe
k=0.97

[win:cWindower]
reader.dmLevel=framespe
writer.dmLevel=winframe
winFunc = ham
gain = 1.0

[fft:cTransformFFT]
reader.dmLevel=winframe
writer.dmLevel=fftc

[fftmagphase:cFFTmagphase]
reader.dmLevel=fftc
writer.dmLevel=fftmag

[mspec:cMelspec]
reader.dmLevel=fftmag
writer.dmLevel=mspec1
htkcompatible = 0
usePower = 1
lofreq = 20
hifreq = 8000
nBands=26

[mfcc:cMfcc]
reader.dmLevel = mspec1
writer.dmLevel = mfcc
htkcompatible = 0
firstMfcc=0
lastMfcc=14
cepLifter=22

[mspec2:cMelspec]
reader.dmLevel=fftmag
writer.dmLevel=mspec2
htkcompatible = 0
usePower = 1
lofreq = 20
hifreq = 6500
nBands=8

[vo:cVectorOperation]
reader.dmLevel=mspec2
writer.dmLevel=mspec2log
operation = log
copyInputName = 0
nameAppend=logMelFreqBand

[res:cSpecResample]
reader.dmLevel=fftc
writer.dmLevel=outpR
targetFs = 11000

[lpc:cLpc]
;reader.dmLevel=framespe
reader.dmLevel=outpR
writer.dmLevel=lpc
p=8
method = acf
saveLPCoeff = 1
lpGain = 0
saveRefCoeff = 0
residual = 0
forwardFilter = 0
lpSpectrum = 0

[lsp:cLsp]
reader.dmLevel=lpc
writer.dmLevel=lsp

[intens:cIntensity]
reader.dmLevel=frames
writer.dmLevel=intens
intensity=0
loudness=1

[mzcr:cMZcr]
reader.dmLevel=frames
writer.dmLevel=mzcr
zcr=1
amax=0
mcr=0
maxmin=0
dc=0

[lld:cContourSmoother]
reader.dmLevel=intens;mfcc;mspec2log;lsp;pitch
writer.dmLevel=lld
buffersize=1000
writer.levelconf.isRb=0
writer.levelconf.growDyn=1
smaWin = 3
; this level must grow to hold ALL the LLD of the full input

// ---- delta regression of LLD ----
[delta1:cDeltaRegression]
reader.dmLevel=lld
writer.dmLevel=lld_de
buffersize=1000
writer.levelconf.isRb=0
writer.levelconf.growDyn=1
deltawin=2
blocksize=1

[lld2:cContourSmoother]
reader.dmLevel=pitchF;jitter
writer.dmLevel=lld2
buffersize=1000
writer.levelconf.isRb=0
writer.levelconf.growDyn=1
; this level must grow to hold ALL the LLD of the full input

// ---- delta regression of LLD ----
[delta2:cDeltaRegression]
reader.dmLevel=lld2
writer.dmLevel=lld2_de
buffersize=1000
writer.levelconf.isRb=0
writer.levelconf.growDyn=1
deltawin=2
blocksize=1

// statistical functionals
[functL1:cFunctionals]
reader.dmLevel=lld;lld_de
writer.dmLevel=func
 ; frameSize and frameStep = 0 => functionals over complete input
 ; (NOTE: buffersize of lld and lld_de levels must be large enough!!)
frameMode = fixed
frameSizeFrames = 500
; was 25
frameStepFrames = 200
; was 10
frameCenterSpecial = left
functionalsEnabled=Extremes;Regression;Moments;Percentiles;Times
Extremes.max = 0
Extremes.min = 0
Extremes.range = 0
Extremes.maxpos = 1
Extremes.minpos = 1
Extremes.amean = 1
Extremes.maxameandist=0
Extremes.minameandist=0
Regression.linregc1 = 1
Regression.linregc2 = 1
Regression.linregerrA = 1
Regression.linregerrQ = 1
Regression.qregc1 = 0
Regression.qregc2 = 0
Regression.qregc3 = 0
Regression.qregerrA = 0
Regression.qregerrQ = 0
Regression.centroid = 0
Moments.variance = 0
Moments.stddev = 1
Moments.skewness = 1
Moments.kurtosis = 1
Moments.amean = 0
Percentiles.quartiles = 1
Percentiles.quartile1 = 0
Percentiles.quartile2 = 0
Percentiles.quartile3 = 0
Percentiles.iqr = 1
Percentiles.iqr12 = 0
Percentiles.iqr23 = 0
Percentiles.iqr13 = 0
Percentiles.interp = 1
Percentiles.percentile = 0.01;0.99
Percentiles.pctlrange=0-1
Times.upleveltime25 = 0
Times.downleveltime25 = 0
Times.upleveltime50 = 0
Times.downleveltime50 = 0
Times.upleveltime75 = 1
Times.downleveltime75 = 0
Times.upleveltime90 = 1
Times.downleveltime90 = 0
Times.risetime = 0
Times.falltime = 0
Times.leftctime = 0
Times.rightctime = 0
Times.duration = 0
Times.norm = turn

// statistical functionals
[functL1nz:cFunctionals]
reader.dmLevel=lld2;lld2_de
writer.dmLevel=funcNz
 ; frameSize and frameStep = 0 => functionals over complete input
 ; (NOTE: buffersize of lld and lld_de levels must be large enough!!)
frameMode = fixed
frameSizeFrames = 500
frameStepFrames = 200
frameCenterSpecial = left
functionalsEnabled=Extremes;Regression;Moments;Percentiles;Times
Extremes.max = 0
Extremes.min = 0
Extremes.range = 0
Extremes.maxpos = 1
Extremes.minpos = 1
Extremes.amean = 1
Extremes.maxameandist=0
Extremes.minameandist=0
Extremes.norm = frame
Regression.linregc1 = 1
Regression.linregc2 = 1
Regression.linregerrA = 1
Regression.linregerrQ = 1
Regression.qregc1 = 0
Regression.qregc2 = 0
Regression.qregc3 = 0
Regression.qregerrA = 0
Regression.qregerrQ = 0
Regression.centroid = 0
Moments.variance = 0
Moments.stddev = 1
Moments.skewness = 1
Moments.kurtosis = 1
Moments.amean = 0
Percentiles.quartiles = 1
Percentiles.quartile1 = 0
Percentiles.quartile2 = 0
Percentiles.quartile3 = 0
Percentiles.iqr = 1
Percentiles.iqr12 = 0
Percentiles.iqr23 = 0
Percentiles.iqr13 = 0
Percentiles.interp = 1
Percentiles.percentile = 0.99
;Percentiles.pctlrange=0-1
Times.upleveltime25 = 0
Times.downleveltime25 = 0
Times.upleveltime50 = 0
Times.downleveltime50 = 0
Times.upleveltime75 = 1
Times.downleveltime75 = 0
Times.upleveltime90 = 1
Times.downleveltime90 = 0
Times.risetime = 0
Times.falltime = 0
Times.leftctime = 0
Times.rightctime = 0
Times.duration = 0
Times.norm = turn
nonZeroFuncts=1

[arffsink:cArffSink]
reader.dmLevel=func;funcNz
 ; do not print "frameNumber" attribute to ARFF file
frameIndex=1
frameTime=0
 ; name of output file as commandline option
filename=\cm[arffout(P){output.arff}:name of WEKA Arff output file]
 ; name of @relation in the ARFF file
relation=\cm[corpus{Audio}:corpus name, arff relation]
 ; name of the current instance (usually file name of input wave file)
instanceName=\cm[instname(N){noname}:name of arff instance]
\{arff_targets_age.conf}

 ; append to an existing file, so multiple calls of SMILExtract on different
 ; input files append to the same output ARFF file
append=1
 

// ################### END OF openSMILE CONFIG FILE ######################

