///////////////////////////////////////////////////////////////////////////////////////
///////// audio+video features for multi-modal speaker trait recog.  /////////////////
/////////                                                            //////////////////
///////// (c) audEERING UG (haftungsbeschränkt),                     //////////////////
/////////     All rights reserved.                                  //////////////////
///////////////////////////////////////////////////////////////////////////////////////

//
// configuration of commandline options for target classes in an ARFF file
// (cArffSink)
//

//
// Working example: Feature extraction for estimating 
//   age, gender and ethnicity from web-videos
//

class[0].name = age
class[0].type = {adult, young}
target[0].all = \cm[age(a){?}:age of speaker (young/adult)]

class[1].name = gender
class[1].type = {f, m}
target[1].all = \cm[gender(g){?}:gender of speaker (m/f)]

class[2].name = ethnicity
class[2].type = {black_asian, white}
target[2].all = \cm[ethnicity(e){?}:ethnicity of speaker (white/black_asian)]
