///////////////////////////////////////////////////////////////////////////////////////
///////// > openCV video features with openSMILE <                   //////////////////
/////////                                                            //////////////////
///////// (c) audEERING UG (haftungsbeschränkt),                     //////////////////
/////////     All rights reserved.                                  //////////////////
///////////////////////////////////////////////////////////////////////////////////////

 ;===== component manager configuration (= list of enabled components!) =====

[componentInstances:cComponentManager]
instance[dataMemory].type = cDataMemory
// VIDEO
instance[openCVSource].type = cOpenCVSource
instance[mean].type=cFunctionals
instance[arffOutput].type=cArffSink

 // Here you can control the amount of detail displayed for the data memory
 // level configuration. 0 is no information at all, 5 is maximum detail.
printLevelStats = 0
 // You can set the number of parallel threads (experimental):
nThreads = 1

// ============= component configuration sections begin here ==============

[openCVSource:cOpenCVSource]
writer.dmLevel = videofeatures
display = 1
videoSource = FILE
filename = \cm[videofile(V){test.avi}:name of input file]
face_cascade_path = /usr/share/opencv/data/lbpcascades/lbpcascade_frontalface.xml
eyes_cascade_path = /usr/share/opencv/data//haarcascades/haarcascade_eye_tree_eyeglasses.xml
extract_face = 1
extract_hsv_histogram = 0
extract_hsv_histogram = 1
extract_optical_flow = 0
include_face_features = 0
ignore_invalid_frames = 1
face_width = 60
lbp_uniformpatterns = 0
hsv_histogram_h_bins = 30
hsv_histogram_s_bins = 30
hsv_histogram_v_bins = 20
normalize_histograms = 1
of_histogram_bins = 20
of_histogram_max_flow = 0.2
of_histogram_downsample = 0.3

[mean:cFunctionals]
reader.dmLevel = videofeatures
writer.dmLevel = mean
copyInputName = 1
frameMode = fixed
frameSizeFrames = 150
frameStepFrames = 60
frameCenterSpecial = left
functionalsEnabled = Means
Means.amean = 1
Means.posamean = 0
Means.absmean = 0
Means.rqmean = 0
Means.flatness = 0
Means.qmean = 0
Means.nzamean = 0
Means.nzabsmean = 0
Means.nzqmean = 0
Means.nzgmean = 0
Means.nnz = 0
Means.norm = frames
nonZeroFuncts = 1

[arffOutput:cArffSink]
reader.dmLevel= mean
number=1
timestamp=0
filename=\cm[arffout(O){output_video.arff}:name of WEKA Arff output file]
relation=\cm[corpus{Video}:corpus name, arff relation]
instanceName=\cm[instname(N){lbp}:name of arff instance]
\{arff_targets_age.conf}
append=1

// ################### END OF openSMILE CONFIG FILE ######################

