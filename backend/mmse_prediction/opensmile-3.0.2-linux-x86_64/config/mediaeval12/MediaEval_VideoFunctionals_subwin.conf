///////////////////////////////////////////////////////////////////////////////////////
///////// > openSMILE configuration file for MediaEval 2012 TUM contrib. //////////////
/////////                                                            //////////////////
/////////  * written 2012 by <PERSON>, <PERSON>lor<PERSON> E<PERSON>ben *         //////////////////
/////////                                                            //////////////////
///////// (c) audEERING UG (haftungsbeschränkt),                     //////////////////
/////////     All rights reserved.                                  //////////////////
///////////////////////////////////////////////////////////////////////////////////////

// Summarizes externally extracted video features


;;;;;;; component list ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;

[componentInstances:cComponentManager]
instance[dataMemory].type=cDataMemory
printLevelStats=0


;;;;;;;;;;;;;;;;;;;;;;;;;;;; main section ;;;;;;;;;;;;;;;;;;;;;;;;;;;

[componentInstances:cComponentManager]
instance[csvIn].type=cCsvSource

[csvIn:cCsvSource]
writer.dmLevel=lld
writer.levelconf.isRb = 0
writer.levelconf.growDyn = 1
writer.levelconf.T = 0.04
filename=\cm[inputfile(I){test.csv}:name of input file]
start=\cm[start(S){1}:start line]
end=\cm[end(E){-1}:end line]

;;;;;;;;; deltas
[componentInstances:cComponentManager]
instance[deA].type=cDeltaRegression

[deA:cDeltaRegression]
reader.dmLevel = lld
writer.dmLevel = lld_de
writer.levelconf.isRb=0
writer.levelconf.growDyn=1

;;;;;;;;; functionals / statistics


[componentInstances:cComponentManager]
instance[functionalsA].type=cFunctionals

; functionals for energy related lld
[functionalsA:cFunctionals]
reader.dmLevel = lld;lld_de
writer.dmLevel = functionals
// nameAppend = 
copyInputName = 1
frameMode = full
frameSize = 4.0
frameStep = 4.0
frameCenterSpecial = left
noPostEOIprocessing = 0
allowLastFrameIncomplete = 1

functionalsEnabled = Means ; Extremes ; Peaks2 ; Regression ; Percentiles ; Moments ; Times ;  Lpc

Means.amean = 1
Means.rqmean = 1
Means.absmean = 0
Means.qmean = 0
Means.nzamean = 0
Means.nzabsmean = 0
Means.nzqmean = 0
Means.nzgmean = 0
Means.nnz = 0
Means.flatness = 1
Means.norm = segment

Extremes.max = 0
Extremes.min = 0
Extremes.maxpos = 1
Extremes.minpos = 1
Extremes.maxameandist = 0
Extremes.minameandist = 0

Regression.linregc1 = 1
Regression.linregc2 = 1
Regression.linregerrA = 0
Regression.linregerrQ = 1
Regression.qregc1 = 1
Regression.qregc2 = 0
Regression.qregc3 = 0
Regression.qregerrA = 0
Regression.qregerrQ = 1
Regression.oldBuggyQerr = 0
Regression.centroid = 1
Regression.normRegCoeff = 0

Peaks2.numPeaks = 0
Peaks2.meanPeakDist = 1
Peaks2.meanPeakDistDelta = 0
Peaks2.peakDistStddev = 1
Peaks2.peakRangeAbs = 1
Peaks2.peakRangeRel = 1
Peaks2.peakMeanAbs = 1
Peaks2.peakMeanMeanDist = 1
Peaks2.peakMeanRel = 1
Peaks2.ptpAmpMeanAbs = 0
Peaks2.ptpAmpMeanRel = 0
Peaks2.ptpAmpStddevAbs = 0
Peaks2.ptpAmpStddevRel = 0
Peaks2.minRangeAbs = 0
Peaks2.minRangeRel = 1
Peaks2.minMeanAbs = 0
Peaks2.minMeanMeanDist = 0
Peaks2.minMeanRel = 0
Peaks2.mtmAmpMeanAbs = 0
Peaks2.mtmAmpMeanRel = 0
Peaks2.mtmAmpStddevAbs = 0
Peaks2.mtmAmpStddevRel = 0
Peaks2.meanRisingSlope = 1
Peaks2.maxRisingSlope = 1
Peaks2.minRisingSlope = 1
Peaks2.stddevRisingSlope = 1
Peaks2.meanFallingSlope = 1
Peaks2.maxFallingSlope = 1
Peaks2.minFallingSlope = 1
Peaks2.stddevFallingSlope = 1
Peaks2.norm = seconds
Peaks2.relThresh = 0.100000
Peaks2.dynRelThresh = 0
;Peaks2.posDbgOutp = minmax.txt
Peaks2.posDbgAppend = 0
Peaks2.consoleDbg = 0

Moments.variance = 0
Moments.stddev = 1
Moments.skewness = 1
Moments.kurtosis = 1
Moments.amean = 0

Percentiles.quartiles = 1
Percentiles.iqr = 1
Percentiles.percentile[0] = 0.01
Percentiles.percentile[1] = 0.99
Percentiles.pctlrange[0] = 0-1
Percentiles.interp = 1

Times.upleveltime25 = 0
Times.downleveltime25 = 1
Times.upleveltime50 = 0
Times.downleveltime50 = 0
Times.upleveltime75 = 0
Times.downleveltime75 = 0
Times.upleveltime90 = 1
Times.downleveltime90 = 0
Times.risetime = 1
Times.falltime = 1
Times.leftctime = 0
Times.rightctime = 0
Times.duration = 1
Times.buggySecNorm = 0
Times.norm = segment

Lpc.lpGain = 1
Lpc.lpc = 1
Lpc.firstCoeff = 0
Lpc.order = 5

nonZeroFuncts = 0
masterTimeNorm = segment





;;;;;;;;; output all features....

[componentInstances:cComponentManager]
instance[arffsink].type=cArffSink

[arffsink:cArffSink]
reader.dmLevel=functionals
filename=\cm[output(O){output.arff}:output arff file for feature data]
append=1
frameIndex=1
frameTime=0
relation=MediaEval_FT
instanceName=\cm[instName{null}:instance name]
\{../shared/arff_targets.conf}

