///////////////////////////////////////////////////////////////////////////////////////
///////// > openSMILE configuration file for MediaEval 2012 TUM contrib. //////////////
/////////                                                            //////////////////
/////////  * written 2012 by <PERSON>, <PERSON>lor<PERSON> E<PERSON>ben *         //////////////////
/////////                                                            //////////////////
///////// (c) audEERING UG (haftungsbeschränkt),                     //////////////////
/////////     All rights reserved.                                  //////////////////
///////////////////////////////////////////////////////////////////////////////////////

// Summarizes externally extracted video features

;;;;;;; component list ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;

[componentInstances:cComponentManager]
instance[dataMemory].type=cDataMemory
printLevelStats=0


;;;;;;;;;;;;;;;;;;;;;;;;;;;; main section ;;;;;;;;;;;;;;;;;;;;;;;;;;;

[componentInstances:cComponentManager]
instance[csvIn].type=cCsvSource
instance[dsel].type=cDataSelector

[csvIn:cCsvSource]
writer.dmLevel=lld0
writer.levelconf.isRb = 0
writer.levelconf.growDyn = 1
writer.levelconf.T = 0.04
filename=\cm[inputfile(I){test.csv}:name of input file]
start=\cm[start(S){1}:start line]
end=\cm[end(E){-1}:end line]

[dsel:cDataSelector]
reader.dmLevel = lld0
writer.dmLevel = lld
// nameAppend = 
copyInputName = 1
// selFile = 
// selected[] = 
selectedRange[0] = 2-$
// newNames[] = 
elementMode = 1
dummyMode = 0

;;;;;;;;; deltas
[componentInstances:cComponentManager]
instance[deA].type=cDeltaRegression

[deA:cDeltaRegression]
reader.dmLevel = lld
writer.dmLevel = lld_de
writer.levelconf.isRb=0
writer.levelconf.growDyn=1

;;;;;;;;; functionals / statistics


[componentInstances:cComponentManager]
instance[functionalsA].type=cFunctionals

; functionals for energy related lld
[functionalsA:cFunctionals]
reader.dmLevel = lld;lld_de
writer.dmLevel = functionals
// nameAppend = 
copyInputName = 1
frameMode = full
frameSize = 0
frameStep = 0
frameCenterSpecial = left

functionalsEnabled = Means; Peaks2 ; Percentiles ; Moments 

Means.amean = 1
Means.rqmean = 1
Means.absmean = 0
Means.qmean = 0
Means.nzamean = 0
Means.nzabsmean = 0
Means.nzqmean = 0
Means.nzgmean = 0
Means.nnz = 0
Means.flatness = 1
Means.norm = segment

Peaks2.numPeaks = 0
Peaks2.meanPeakDist = 0
Peaks2.meanPeakDistDelta = 0
Peaks2.peakDistStddev = 0
Peaks2.peakRangeAbs = 0
Peaks2.peakRangeRel = 0
Peaks2.peakMeanAbs = 1
Peaks2.peakMeanMeanDist = 0
Peaks2.peakMeanRel = 0
Peaks2.ptpAmpMeanAbs = 0
Peaks2.ptpAmpMeanRel = 0
Peaks2.ptpAmpStddevAbs = 0
Peaks2.ptpAmpStddevRel = 0
Peaks2.minRangeAbs = 0
Peaks2.minRangeRel = 0
Peaks2.minMeanAbs = 1
Peaks2.minMeanMeanDist = 0
Peaks2.minMeanRel = 0
Peaks2.mtmAmpMeanAbs = 0
Peaks2.mtmAmpMeanRel = 0
Peaks2.mtmAmpStddevAbs = 0
Peaks2.mtmAmpStddevRel = 0
Peaks2.meanRisingSlope = 1
Peaks2.maxRisingSlope = 1
Peaks2.minRisingSlope = 1
Peaks2.stddevRisingSlope = 0
Peaks2.meanFallingSlope = 1
Peaks2.maxFallingSlope = 1
Peaks2.minFallingSlope = 1
Peaks2.stddevFallingSlope = 0
Peaks2.norm = seconds
Peaks2.relThresh = 0.100000
Peaks2.dynRelThresh = 0
;Peaks2.posDbgOutp = minmax.txt
Peaks2.posDbgAppend = 0
Peaks2.consoleDbg = 0

Moments.variance = 0
Moments.stddev = 1
Moments.skewness = 0
Moments.kurtosis = 0
Moments.amean = 0

Percentiles.quartiles = 0
Percentiles.iqr = 1
Percentiles.percentile[0] = 0.01
Percentiles.percentile[1] = 0.99
Percentiles.pctlrange[0] = 0-1
Percentiles.interp = 1

nonZeroFuncts = 0
masterTimeNorm = segment





;;;;;;;;; output all features....

[componentInstances:cComponentManager]
instance[arffsink].type=cArffSink

[arffsink:cArffSink]
reader.dmLevel=functionals
filename=\cm[output(O){output.arff}:output arff file for feature data]
append=1
frameIndex=0
frameTime=0
relation=MediaEval_FT_VideoNico1
instanceName=\cm[instName{null}:instance name]
\{../shared/arff_targets.conf}

