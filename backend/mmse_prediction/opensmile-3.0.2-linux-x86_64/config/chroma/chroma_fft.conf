///////////////////////////////////////////////////////////////////////////////////////
///////// > openSMILE configuration file for Chroma features <       //////////////////
/////////   Chroma features computed over 64ms frames, Gauss window  //////////////////
/////////   6 octaves from 55.0 Hz (first note)                      //////////////////
/////////   Output: 12 chroma features in a CSV file                 //////////////////
/////////                                                            //////////////////
///////// (c) audEERING UG (haftungsbeschränkt),                     //////////////////
/////////     All rights reserved.                                  //////////////////
///////////////////////////////////////////////////////////////////////////////////////


 ;===== component manager configuration (= list of enabled components!) =====

[componentInstances:cComponentManager]
instance[dataMemory].type = cDataMemory
instance[waveSource].type = cWaveSource
instance[framer].type = cFramer
instance[windower].type = cWindower
instance[transformFFT].type = cTransformFFT
instance[fFTmagphase].type = cFFTmagphase
instance[tonespec].type = cTonespec
instance[chroma].type = cChroma
instance[csvSink].type = cCsvSink
 // Here you can control the amount of detail displayed for the data memory
 // level configuration. 0 is no information at all, 5 is maximum detail.
printLevelStats = 0
 // You can set the number of parallel threads (experimental):
nThreads = 1

// ============= component configuration sections begin here ==============

[waveSource:cWaveSource]
writer.dmLevel = wave
filename = \cm[inputfile(I){input.wav}:name of input wave file]
monoMixdown = 1
start = 0
end = -1
endrel = 0
noHeader = 0
 
[framer:cFramer]
reader.dmLevel = wave
writer.dmLevel = frames
// nameAppend = 
copyInputName = 1
frameMode = fixed
// frameListFile = 
// frameList = 
frameSize = 0.064000
frameStep = 0.010000
frameCenterSpecial = left
noPostEOIprocessing = 1
 
[windower:cWindower]
reader.dmLevel = frames
writer.dmLevel = winframes
// nameAppend = 
copyInputName = 1
processArrayFields = 1
gain = 1
offset = 0
winFunc = Gau
sigma = 0.400000
 
[transformFFT:cTransformFFT]
reader.dmLevel = winframes
writer.dmLevel = fftc
// nameAppend = 
copyInputName = 1
processArrayFields = 1
inverse = 0
 
[fFTmagphase:cFFTmagphase]
reader.dmLevel = fftc
writer.dmLevel = fftmag
// nameAppend = 
copyInputName = 1
processArrayFields = 1
inverse = 0
magnitude = 1
phase = 0
 
[tonespec:cTonespec]
reader.dmLevel = fftmag
writer.dmLevel = tonespec
nameAppend = note
copyInputName = 1
processArrayFields = 0
nOctaves = 6
firstNote = 55
filterType = gau
usePower = 1
dbA = 1
 
[chroma:cChroma]
reader.dmLevel = tonespec
writer.dmLevel = chroma
nameAppend = chroma
copyInputName = 0
processArrayFields = 0
octaveSize = 12
 
[csvSink:cCsvSink]
reader.dmLevel = chroma
filename = \cm[outputfile(O){chroma.csv}:name of output CSV file for chroma features]
delimChar = ;
append = 0
timestamp = 0
number = 0
printHeader = 0


// ################### END OF openSMILE CONFIG FILE ######################

