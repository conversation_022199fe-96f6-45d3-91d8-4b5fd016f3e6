///////////////////////////////////////////////////////////////////////////////////////
///////// > openSMILE configuration file for Chroma features <       //////////////////
/////////   Chroma features computed via sine tone filter bank       //////////////////
/////////   6 octaves from 55.0 Hz (first note)                      //////////////////
/////////   Output: 12 chroma features in a CSV file                 //////////////////
/////////                                                            //////////////////
///////// (c) audEERING UG (haftungsbeschränkt),                     //////////////////
/////////     All rights reserved.                                  //////////////////
///////////////////////////////////////////////////////////////////////////////////////

 ;===== component manager configuration (= list of enabled components!) =====

[componentInstances:cComponentManager]
instance[dataMemory].type = cDataMemory
instance[waveSource].type = cWaveSource
instance[tonefilt].type = cTonefilt
instance[chroma].type = cChroma
instance[csvSink].type = cCsvSink
 // Here you can control the amount of detail displayed for the data memory
 // level configuration. 0 is no information at all, 5 is maximum detail.
printLevelStats = 0
 // You can set the number of parallel threads (experimental):
nThreads = 1

// ============= component configuration sections begin here ==============

[waveSource:cWaveSource]
writer.dmLevel = wave
filename = \cm[inputfile(I){input.wav}:name of wave input file]
monoMixdown = 1
start = 0
end = -1
endrel = 0
noHeader = 0
 
[tonefilt:cTonefilt]
reader.dmLevel = wave
writer.dmLevel = tonespec
nameAppend = tonefilt
copyInputName = 1
nNotes = 72
firstNote = 55
decayF0 = 0.999900
decayFN = 0.999000
outputPeriod = 0.0100
 
[chroma:cChroma]
reader.dmLevel = tonespec
writer.dmLevel = chroma
nameAppend = chroma
copyInputName = 0
processArrayFields = 1
octaveSize = 12
 
[csvSink:cCsvSink]
reader.dmLevel = chroma
filename = \cm[outputfile(O){chroma.csv}:output CSV file for chroma features]
delimChar = ;
append = 0
timestamp = 0
number = 0
printHeader = 0
 
// ################### END OF openSMILE CONFIG FILE ######################

