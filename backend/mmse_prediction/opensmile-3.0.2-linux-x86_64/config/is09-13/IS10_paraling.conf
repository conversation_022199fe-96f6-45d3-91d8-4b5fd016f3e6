
///////////////////////////////////////////////////////////////////////////////////////
///////// > openSMILE configuration file for IS10 features <         //////////////////
/////////                                                            //////////////////
///////// (c) 2014 by audEERING                                      //////////////////
/////////     All rights reserved. See file COPYING for details.     //////////////////
///////////////////////////////////////////////////////////////////////////////////////


// NOTE: This file is not compatible with old versions of openSMILE (< 2.1)
// and will not give the same features (numerically) 
// as the original IS10 features.
// It should be preferred in new studies / designes, however, as it contains
// some minor fixes.
// For compatible features, use the IS10_paraling_compat.conf

//
// Usage:
// SMILExtract -C thisconfig.conf -I input.wav -O output.arff 
//
 
[componentInstances:cComponentManager]
instance[dataMemory].type=cDataMemory
;; run single threaded (nThreads=1)
; NOTE: a single thread is more efficient for processing small files, since multi-threaded processing involves more 
;       overhead during startup, which will make the system slower in the end
nThreads=1
;; do not show any internal dataMemory level settings 
; (if you want to see them set the value to 1, 2, 3, or 4, depending on the amount of detail you wish)
printLevelStats=0


\{../shared/standard_wave_input.conf.inc}
\{IS10_paraling_core.lld.conf.inc}
\{IS10_paraling_core.func.conf.inc}

;;;;;;;;; prepare features for standard output module

[componentInstances:cComponentManager]
instance[lldconcat].type=cVectorConcat
instance[llddeconcat].type=cVectorConcat
instance[funcconcat].type=cVectorConcat

[lldconcat:cVectorConcat]
reader.dmLevel = is10_lld1;is10_lld2
writer.dmLevel = lld
includeSingleElementFields = 1

[llddeconcat:cVectorConcat]
reader.dmLevel = is10_lld1_de;is10_lld2_de
writer.dmLevel = lld_de
includeSingleElementFields = 1

[funcconcat:cVectorConcat]
reader.dmLevel = is10_funct;is10_functNz;is10_functOnsets
writer.dmLevel = func
includeSingleElementFields = 1

\{../shared/standard_data_output.conf.inc}

