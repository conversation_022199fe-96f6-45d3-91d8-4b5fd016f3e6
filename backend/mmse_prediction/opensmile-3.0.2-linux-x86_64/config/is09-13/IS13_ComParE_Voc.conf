///////////////////////////////////////////////////////////////////////////////////////
///////// > openSMILE configuration file for ComParE vocalistions <  //////////////////
/////////                                                            //////////////////
///////// (c) 2014 by audEERING                                      //////////////////
/////////     All rights reserved. See file COPYING for details.     //////////////////
///////////////////////////////////////////////////////////////////////////////////////



[componentInstances:cComponentManager]
instance[dataMemory].type=cDataMemory
instance[waveIn].type=cWaveSource
instance[fr1].type=cFramer
instance[pe2].type=cVectorPreemphasis
instance[w1].type=cWindower
instance[fft1].type=cTransformFFT
instance[fftmp1].type=cFFTmagphase
instance[acf].type=cAcf
instance[cepstrum].type=cAcf
instance[pitchACF].type=cPitchACF
instance[mspec].type=cMelspec
instance[mfcc].type=cMfcc
instance[energy].type=cEnergy
instance[mzcr].type=cMZcr
instance[cms].type=cFullinputMean
instance[cmsD].type=cFullinputMean
instance[cmsA].type=cFullinputMean
instance[vc1].type=cVectorConcat
instance[delta1].type=cDeltaRegression
instance[delta2].type=cDeltaRegression
instance[delta1e].type=cDeltaRegression
instance[delta2e].type=cDeltaRegression
instance[deltapitch].type=cDeltaRegression
instance[deltazcr].type=cDeltaRegression
instance[framestacking].type=cFunctionals
instance[arffout].type=cArffSink
nThreads=1
printLevelStats=0

[waveIn:cWaveSource]
writer.dmLevel=wave
filename=\cm[inputfile(I){test.wav}:name of input file]
;buffersize=16000
monoMixdown=1

[fr1:cFramer]
reader.dmLevel=wave
writer.dmLevel=outp
frameSize = 0.0250
frameStep = 0.010
frameCenterSpecial = left

[pe2:cVectorPreemphasis]
reader.dmLevel=outp
writer.dmLevel=output
k=0.97


[w1:cWindower]
reader.dmLevel=output
writer.dmLevel=winoutput
winFunc = ham
gain = 1.0

[fft1:cTransformFFT]
reader.dmLevel=winoutput
writer.dmLevel=fftc1

[fftmp1:cFFTmagphase]
reader.dmLevel=fftc1
writer.dmLevel=fft1

[acf:cAcf]
reader.dmLevel=fft1
writer.dmLevel=acf
nameAppend = acf
copyInputName = 1
processArrayFields = 1
usePower = 1
cepstrum = 0

[cepstrum:cAcf]
reader.dmLevel=fft1
writer.dmLevel=cepstrum
nameAppend = acf
copyInputName = 1
processArrayFields = 1
usePower = 1
cepstrum = 1

[pitchACF:cPitchACF]
  ; the pitchACF component must ALWAYS read from acf AND cepstrum in the given order!
reader.dmLevel=acf;cepstrum
writer.dmLevel=pitch
writer.levelconf.growDyn=1
writer.levelconf.isRb=0
copyInputName = 1
processArrayFields = 0
maxPitch = 500
voiceProb = 1
voiceQual = 0
HNR = 1
F0 = 1
F0raw = 0
F0env = 0
voicingCutoff = 0.550000

[mspec:cMelspec]
reader.dmLevel=fft1
writer.dmLevel=mspec1
htkcompatible = 1
nBands = 26
usePower = 1
lofreq = 0
hifreq = 8000

[mfcc:cMfcc]
reader.dmLevel=mspec1
writer.dmLevel=mfcc1
writer.levelconf.growDyn=1
writer.levelconf.isRb=0
buffersize=1000
firstMfcc = 1
lastMfcc =  12
htkcompatible = 1

[cms:cFullinputMean]
reader.dmLevel=mfcc1
writer.dmLevel=mfcc1m
multiLoopMode=1

[cmsD:cFullinputMean]
reader.dmLevel=mfcc1de
writer.dmLevel=mfcc1dem
multiLoopMode=1

[cmsA:cFullinputMean]
reader.dmLevel=mfcc1dede
writer.dmLevel=mfcc1dedem
multiLoopMode=1


[vc1:cVectorConcat]
reader.dmLevel=mfcc1m;energy;mfcc1dem;energyDe;mfcc1dedem;energyDede
writer.dmLevel=ft0
processArrayFields=0

[delta1:cDeltaRegression]
reader.dmLevel=mfcc1
writer.dmLevel=mfcc1de
deltawin=2
blocksize=1

[delta2:cDeltaRegression]
reader.dmLevel=mfcc1de
writer.dmLevel=mfcc1dede
deltawin=2
blocksize=1

[delta1e:cDeltaRegression]
reader.dmLevel=energy
writer.dmLevel=energyDe
deltawin=2
blocksize=1

[delta2e:cDeltaRegression]
reader.dmLevel=energyDe
writer.dmLevel=energyDede
deltawin=2
blocksize=1

[deltapitch:cDeltaRegression]
reader.dmLevel=pitch
writer.dmLevel=pitchde
deltawin=2
blocksize=1
onlyInSegments=1
zeroSegBound=1

[deltazcr:cDeltaRegression]
reader.dmLevel=zcr
writer.dmLevel=zcrde
deltawin=2
blocksize=1

[energy:cEnergy]
reader.dmLevel=outp
writer.dmLevel=energy
writer.levelconf.growDyn=1
writer.levelconf.isRb=0
buffersize=1000
htkcompatible=1

[mzcr:cMZcr]
reader.dmLevel=outp
writer.dmLevel=zcr
writer.levelconf.growDyn=1
writer.levelconf.isRb=0
copyInputName = 1
processArrayFields = 1
zcr = 1
amax = 0
mcr = 0
maxmin = 0
dc = 0

[framestacking:cFunctionals]
reader.dmLevel = mfcc1m;energy;mfcc1dem;energyDe;mfcc1dedem;energyDede;pitch;pitchde;zcr;zcrde
;ft0
writer.dmLevel = fts
;frameCenterSpecial = mid
frameCenterFrames = \cm[stackC{2}:frame center]
frameSizeFrames = \cm[stack(S){5}:number of frames to stack]
frameStepFrames = 1
functionalsEnabled = Moments
Moments.variance = 0
Moments.stddev = 1
Moments.skewness = 0
Moments.kurtosis = 0
Moments.amean = 1
noPostEOIprocessing = 0

[arffout:cArffSink]
relation = COMPARE2013_Vocalisations
;instanceBase=\cm[instName{file}:instance name]
instanceName=\cm[instName{file}:instance name]
reader.dmLevel=mfcc1m;energy;mfcc1dem;energyDe;mfcc1dedem;energyDede;pitch;pitchde;zcr;zcrde;fts
;reader.dmLevel=fts
filename=\cm[output(O){mfcc.arff}:name of MFCC output filename (ARFF format)]
frameIndex = 1
frameTime = 0
append = 1
class[0].type = { garbage, laughter, filler }
; default class
target[0].all = garbage
target[0].instance = \cm[frameClasses(T){garbage}:list of class labels for frames]

