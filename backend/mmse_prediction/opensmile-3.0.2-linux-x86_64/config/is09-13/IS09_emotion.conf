///////////////////////////////////////////////////////////////////////////////////////
///////// > openSMILE configuration file for IS09 emotion challenge< //////////////////
/////////                                                            //////////////////
///////// (c) 2014 by audEERING                                      //////////////////
/////////     All rights reserved. See file COPYING for details.     //////////////////
///////////////////////////////////////////////////////////////////////////////////////

///////////////////////////////////////////////////////////////////////////////////////
;
; This section is always required in openSMILE configuration files
;   it configures the componentManager and gives a list of all components which are to be loaded
; The order in which the components are listed should match 
;   the order of the data flow for most efficient processing
;
///////////////////////////////////////////////////////////////////////////////////////
[componentInstances:cComponentManager]
 ; this line configures the default data memory:
instance[dataMemory].type=cDataMemory
printLevelStats=0
nThreads=1

/////////////////////////////////////////////////////////////////////////////////////////////
/////////////////////////   component configuration  ////////////////////////////////////////
/////////////////////////////////////////////////////////////////////////////////////////////
; the following sections configure the components listed above
; a help on configuration parameters can be obtained with 
;  SMILExtract -H
; or
;  SMILExtract -H configTypeName (= componentTypeName)
/////////////////////////////////////////////////////////////////////////////////////////////

\{../shared/standard_wave_input.conf.inc}
\{IS09_emotion_core.lld.conf.inc}
\{IS09_emotion_core.func.conf.inc}

; TODO: alias levels in code: cVectorConcat (if 1:1 input (single level) to output mapping) 
;   registers an alias level instead of actually copying
;   this maintains compatibility with old config files, but is more efficient in new code

;;;;;;;;; prepare features for standard output module

[componentInstances:cComponentManager]
instance[lldconcat].type=cVectorConcat
instance[llddeconcat].type=cVectorConcat
instance[funcconcat].type=cVectorConcat

[lldconcat:cVectorConcat]
reader.dmLevel = is09_lld
writer.dmLevel = lld
includeSingleElementFields = 1

[llddeconcat:cVectorConcat]
reader.dmLevel = is09_lld_de
writer.dmLevel = lld_de
includeSingleElementFields = 1

[funcconcat:cVectorConcat]
reader.dmLevel = is09_func
writer.dmLevel = func
includeSingleElementFields = 1

\{../shared/standard_data_output.conf.inc}

