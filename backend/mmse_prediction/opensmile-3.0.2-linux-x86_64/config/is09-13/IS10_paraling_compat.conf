
///////////////////////////////////////////////////////////////////////////////////////
///////// > openSMILE configuration file for IS10 features <         //////////////////
/////////                                                            //////////////////
///////// (c) 2014 by audEERING                                      //////////////////
/////////     All rights reserved. See file COPYING for details.     //////////////////
///////////////////////////////////////////////////////////////////////////////////////


// NOTE: This file is for version 2.1 and above and produces 
// numerically compatible output to the original IS10 set,
// EXCEPT for Jitter/Shimmer, as some bugs have been fixed there 
// w/o maintaining backwards compatibility.

// For new designs one should however use the IS10_paraling.conf which 
// returns incompatible, yet enhanced and fixed features.


//
// Usage:
// SMILExtract -C thisconfig.conf -I input.wav -O output.arff 
//
 
///////////////////////////////////////////////////////////////////////////////////////
;
; This section is always required in openSMILE configuration files
;   it configures the componentManager and gives a list of all components which are to be loaded
; The order in which the components are listed should match 
;   the order of the data flow for most efficient processing
;
///////////////////////////////////////////////////////////////////////////////////////
[componentInstances:cComponentManager]
 ; this line configures the default data memory:
instance[dataMemory].type=cDataMemory
 ;;; wave file input
instance[waveIn].type=cWaveSource
 ;;; 40 ms frames features:
instance[fr40].type=cFramer
instance[w40].type=cWindower
instance[fft40].type=cTransformFFT
instance[fftmagphase40].type=cFFTmagphase
 ; SHS Pitch:
instance[scale].type=cSpecScale
instance[pitchShs].type=cPitchShs
instance[pitchSmooth].type=cPitchSmoother
instance[pitchJitter].type=cPitchJitter
instance[pitchSmooth2].type=cPitchSmoother
instance[res].type=cSpecResample

 ;;; 25 ms frames features:
instance[fr25].type=cFramer
instance[pe].type=cVectorPreemphasis
instance[win].type=cWindower
instance[fft].type=cTransformFFT
instance[fftmagphase].type=cFFTmagphase
 ; mfcc
instance[mspec].type=cMelspec
instance[mfcc].type=cMfcc
 ; log mel frequency bands (mfb)
instance[mspec2].type=cMelspec
instance[vo].type=cVectorOperation
instance[lpc].type=cLpc
 ; Line Spectral Frequencies
instance[lsp].type=cLsp
 ; Loudness (narrow-band approximation)
instance[intens].type=cIntensity
 ;;; all LLD concattenated and smoothed using a moving average filter
instance[lld].type=cContourSmoother
instance[lld2].type=cContourSmoother
 ; delta coefficients of LLD
instance[delta1].type=cDeltaRegression
instance[delta2].type=cDeltaRegression
 ;;; functionals over FULL input (e.g. turns)
instance[functL1].type=cFunctionals
instance[functL1nz].type=cFunctionals
instance[functOnsets].type=cFunctionals

;; run single threaded (nThreads=1)
; NOTE: a single thread is more efficient for processing small files, since multi-threaded processing involves more 
;       overhead during startup, which will make the system slower in the end
nThreads=1
;; do not show any internal dataMemory level settings 
; (if you want to see them set the value to 1, 2, 3, or 4, depending on the amount of detail you wish)
printLevelStats=0



/////////////////////////////////////////////////////////////////////////////////////////////
/////////////////////////   component configuration  ////////////////////////////////////////
/////////////////////////////////////////////////////////////////////////////////////////////
; the following sections configure the components listed above
; a help on configuration parameters can be obtained with 
;  SMILExtract -H
; or
;  SMILExtract -H configTypeName (= componentTypeName)
/////////////////////////////////////////////////////////////////////////////////////////////

[waveIn:cWaveSource]
 ; this sets the level this component writes to
 ; the level will be created by this component
 ; no other components may write to a level having the same name
writer.dmLevel=wave
 ; this defines a new commandline option "-I" or "-inputfile", which can be used to specify 
 ; the filename on the commandline instead of having it "hard-coded" in the config file
filename=\cm[inputfile(I){test.wav}:name of input file]
 ; mix stereo files down to mono for analysis
monoMixdown=1

[fr40:cFramer]
reader.dmLevel=wave
writer.dmLevel=frames40
frameMode = fixed
frameSize = 0.060
frameStep = 0.010
frameCenterSpecial = left
noPostEOIprocessing = 1

[w40:cWindower]
reader.dmLevel=frames40
writer.dmLevel=win40frame
winFunc = gauss
sigma = 0.25
gain = 1.0


[fft40:cTransformFFT]
reader.dmLevel=win40frame
writer.dmLevel=fftc40
 ; for compatibility with 2.2.0 and older versions
zeroPadSymmetric = 0

[fftmagphase40:cFFTmagphase]
reader.dmLevel=fftc40
writer.dmLevel=fftmag40
magnitude = 1
phase = 0

[scale:cSpecScale]
reader.dmLevel=fftmag40
writer.dmLevel=hps
scale=log
 ; octave scale
logScaleBase=2
specSmooth = 0
auditoryWeighting = 0
specEnhance = 0
minF = 25
maxF = -1
interpMethod = spline

[pitchShs:cPitchShs]
reader.dmLevel=hps
writer.dmLevel=pitchShs
F0raw = 0
voicingClip = 0
voicingC1=0
scores=1
voicing=1
nCandidates = 3
octaveCorrection = 0
greedyPeakAlgo = 0
compressionFactor = 0.85
nHarmonics = 15
voicingCutoff = 0.75
maxPitch = 620
minPitch = 52

[pitchSmooth:cPitchSmoother]
reader.dmLevel=pitchShs
writer.dmLevel=pitch
F0raw = 0
F0final = 0
F0finalEnv = 1
voicingFinalUnclipped = 1
medianFilter0 = 0
postSmoothingMethod = simple
;simple
octaveCorrection = 0
writer.levelconf.nT=10
;writer.levelconf.noHang=2
writer.levelconf.isRb=0
writer.levelconf.growDyn=1

[pitchSmooth2:cPitchSmoother]
reader.dmLevel=pitchShs
writer.dmLevel=pitchF
F0raw = 0
F0final = 1
F0finalEnv = 0
voicingFinalUnclipped = 0
medianFilter0 = 0
postSmoothingMethod = simple
octaveCorrection = 0
writer.levelconf.nT=10
;writer.levelconf.noHang=2
writer.levelconf.isRb=0
writer.levelconf.growDyn=1

 ;;;; default (template) configuration section for component 'cPitchJitter' ;;;;
[pitchJitter:cPitchJitter]
reader.dmLevel = wave
writer.dmLevel = jitter
// nameAppend =
copyInputName = 1
F0reader.dmLevel = pitchF
F0field = F0final
searchRangeRel = 0.250000
jitterLocal = 1
jitterDDP = 1
jitterLocalEnv = 0
jitterDDPEnv = 0
shimmerLocal = 1
shimmerLocalEnv = 0
onlyVoiced = 0
;periodLengths = 0
;periodStarts = 0
inputMaxDelaySec = 1
usePeakToPeakPeriodLength = 0
shimmerUseRmsAmplitude = 0
minCC = 0.5
minNumPeriods = 2
useBrokenJitterThresh = 1

[fr25:cFramer]
reader.dmLevel=wave
writer.dmLevel=frames
frameSize = 0.025
frameStep = 0.010
frameCenterSpecial = left

[pe:cVectorPreemphasis]
reader.dmLevel=frames
writer.dmLevel=framespe
k=0.97

[win:cWindower]
reader.dmLevel=framespe
writer.dmLevel=winframe
winFunc = ham
gain = 1.0

[fft:cTransformFFT]
reader.dmLevel=winframe
writer.dmLevel=fftc
 ; for compatibility with 2.2.0 and older versions
zeroPadSymmetric = 0

[fftmagphase:cFFTmagphase]
reader.dmLevel=fftc
writer.dmLevel=fftmag
magnitude = 1
phase = 0

[mspec:cMelspec]
reader.dmLevel=fftmag
writer.dmLevel=mspec1
htkcompatible = 0
usePower = 1
lofreq = 20
hifreq = 8000
nBands=26
specScale = mel
bwMethod = lr

[mfcc:cMfcc]
reader.dmLevel = mspec1
writer.dmLevel = mfcc
htkcompatible = 0
firstMfcc=0
lastMfcc=14
cepLifter=22
copyInputName = 0

[mspec2:cMelspec]
reader.dmLevel=fftmag
writer.dmLevel=mspec2
htkcompatible = 0
usePower = 1
lofreq = 20
hifreq = 6500
nBands=8
specScale = mel
bwMethod = lr

[vo:cVectorOperation]
reader.dmLevel=mspec2
writer.dmLevel=mspec2log
operation = log
copyInputName = 0
nameAppend=logMelFreqBand

[res:cSpecResample]
reader.dmLevel=fftc
writer.dmLevel=outpR
targetFs = 11000

[lpc:cLpc]
;reader.dmLevel=framespe
reader.dmLevel=outpR
writer.dmLevel=lpc
p=8
method = acf
saveLPCoeff = 1
lpGain = 0
saveRefCoeff = 0
residual = 0
forwardFilter = 0
lpSpectrum = 0

[lsp:cLsp]
reader.dmLevel=lpc
writer.dmLevel=lsp

[intens:cIntensity]
reader.dmLevel=frames
writer.dmLevel=intens
intensity=0
loudness=1

[lld:cContourSmoother]
reader.dmLevel=intens;mfcc;mspec2log;lsp;pitch
writer.dmLevel=lld1
buffersize=1000
writer.levelconf.isRb=0
writer.levelconf.growDyn=1
smaWin = 3
; this level must grow to hold ALL the LLD of the full input

// ---- delta regression of LLD ----
[delta1:cDeltaRegression]
reader.dmLevel=lld1
writer.dmLevel=lld1_de
buffersize=1000
writer.levelconf.isRb=0
writer.levelconf.growDyn=1
deltawin=2
blocksize=1

[lld2:cContourSmoother]
reader.dmLevel=pitchF;jitter
writer.dmLevel=lld2
buffersize=1000
writer.levelconf.isRb=0
writer.levelconf.growDyn=1
; this level must grow to hold ALL the LLD of the full input
smaWin = 3
noZeroSma = 0

// ---- delta regression of LLD ----
[delta2:cDeltaRegression]
reader.dmLevel=lld2
writer.dmLevel=lld2_de
buffersize=1000
writer.levelconf.isRb=0
writer.levelconf.growDyn=1
deltawin=2
blocksize=1

[functOnsets:cFunctionals]
reader.dmLevel=pitchF
writer.dmLevel=functOnsets
 ; frameSize and frameStep = 0 => functionals over complete input
 ; (NOTE: buffersize of lld and lld_de levels must be large enough!!)
frameMode = full
frameSize = 0
frameStep = 0
copyInputName=0
functNameAppend=Turn
functionalsEnabled=Onset;Times
//noPostEOIprocessing = 0
Onset.threshold = 0
;Onset.thresholdOnset = 0
;Onset.thresholdOffset = 0
Onset.useAbsVal = 0
Onset.onsetPos = 0
Onset.offsetPos = 0
Onset.numOnsets = 1
Onset.numOffsets = 0
Onset.norm = segment
Times.upleveltime25 = 0
Times.downleveltime25 = 0
Times.upleveltime50 = 0
Times.downleveltime50 = 0
Times.upleveltime75 = 0
Times.downleveltime75 = 0
Times.upleveltime90 = 0
Times.downleveltime90 = 0
Times.risetime = 0
Times.falltime = 0
Times.leftctime = 0
Times.rightctime = 0
Times.duration = 1
Times.norm = second


// statistical functionals
[functL1:cFunctionals]
reader.dmLevel=lld1;lld1_de
writer.dmLevel=funct
 ; frameSize and frameStep = 0 => functionals over complete input
 ; (NOTE: buffersize of lld and lld_de levels must be large enough!!)
frameMode = full
frameSize=0
frameStep=0
functionalsEnabled=Extremes;Regression;Moments;Percentiles;Times
Extremes.max = 0
Extremes.min = 0
Extremes.range = 0
Extremes.maxpos = 1
Extremes.minpos = 1
Extremes.amean = 1
Extremes.maxameandist=0
Extremes.minameandist=0
Extremes.norm = frame
Regression.linregc1 = 1
Regression.linregc2 = 1
Regression.linregerrA = 1
Regression.linregerrQ = 1
Regression.qregc1 = 0
Regression.qregc2 = 0
Regression.qregc3 = 0
Regression.qregerrA = 0
Regression.qregerrQ = 0
Regression.centroid = 0
Regression.oldBuggyQerr = 1
Regression.normInputs = 0
Regression.normRegCoeff = 0
Regression.centroidRatioLimit = 0
Regression.doRatioLimit = 0
Moments.doRatioLimit = 0
Moments.variance = 0
Moments.stddev = 1
Moments.skewness = 1
Moments.kurtosis = 1
Moments.amean = 0
Percentiles.quartiles = 1
Percentiles.quartile1 = 0
Percentiles.quartile2 = 0
Percentiles.quartile3 = 0
Percentiles.iqr = 1
Percentiles.iqr12 = 0
Percentiles.iqr23 = 0
Percentiles.iqr13 = 0
Percentiles.interp = 1
Percentiles.percentile = 0.01;0.99
Percentiles.pctlrange=0-1
Times.upleveltime25 = 0
Times.downleveltime25 = 0
Times.upleveltime50 = 0
Times.downleveltime50 = 0
Times.upleveltime75 = 1
Times.downleveltime75 = 0
Times.upleveltime90 = 1
Times.downleveltime90 = 0
Times.risetime = 0
Times.falltime = 0
Times.leftctime = 0
Times.rightctime = 0
Times.duration = 0
Times.norm = segment
nonZeroFuncts = 0

// statistical functionals
[functL1nz:cFunctionals]
reader.dmLevel=lld2;lld2_de
writer.dmLevel=functNz
 ; frameSize and frameStep = 0 => functionals over complete input
 ; (NOTE: buffersize of lld and lld_de levels must be large enough!!)
frameMode=full
frameSize=0
frameStep=0
functionalsEnabled=Extremes;Regression;Moments;Percentiles;Times
Extremes.max = 0
Extremes.min = 0
Extremes.range = 0
Extremes.maxpos = 1
Extremes.minpos = 1
Extremes.amean = 1
Extremes.maxameandist=0
Extremes.minameandist=0
Extremes.norm = frame
Regression.linregc1 = 1
Regression.linregc2 = 1
Regression.linregerrA = 1
Regression.linregerrQ = 1
Regression.qregc1 = 0
Regression.qregc2 = 0
Regression.qregc3 = 0
Regression.qregerrA = 0
Regression.qregerrQ = 0
Regression.centroid = 0
Regression.oldBuggyQerr = 1
Regression.normInputs = 0
Regression.normRegCoeff = 0
Regression.centroidRatioLimit = 0
Regression.doRatioLimit = 0
Moments.doRatioLimit = 0
Moments.variance = 0
Moments.stddev = 1
Moments.skewness = 1
Moments.kurtosis = 1
Moments.amean = 0
Percentiles.quartiles = 1
Percentiles.quartile1 = 0
Percentiles.quartile2 = 0
Percentiles.quartile3 = 0
Percentiles.iqr = 1
Percentiles.iqr12 = 0
Percentiles.iqr23 = 0
Percentiles.iqr13 = 0
Percentiles.interp = 1
Percentiles.percentile = 0.99
;Percentiles.pctlrange=0-1
Times.upleveltime25 = 0
Times.downleveltime25 = 0
Times.upleveltime50 = 0
Times.downleveltime50 = 0
Times.upleveltime75 = 1
Times.downleveltime75 = 0
Times.upleveltime90 = 1
Times.downleveltime90 = 0
Times.risetime = 0
Times.falltime = 0
Times.leftctime = 0
Times.rightctime = 0
Times.duration = 0
Times.norm = segment
nonZeroFuncts=1

;;;;;;;;; prepare features for standard output module

[componentInstances:cComponentManager]
instance[lldconcat].type=cVectorConcat
instance[llddeconcat].type=cVectorConcat
instance[funcconcat].type=cVectorConcat

[lldconcat:cVectorConcat]
reader.dmLevel = lld1;lld2
writer.dmLevel = lld
includeSingleElementFields = 1

[llddeconcat:cVectorConcat]
reader.dmLevel = lld1_de;lld2_de
writer.dmLevel = lld_de
includeSingleElementFields = 1

[funcconcat:cVectorConcat]
reader.dmLevel = funct;functNz;functOnsets
writer.dmLevel = func
includeSingleElementFields = 1

\{../shared/standard_data_output.conf.inc}



//////---------------------- END -------------------------///////
