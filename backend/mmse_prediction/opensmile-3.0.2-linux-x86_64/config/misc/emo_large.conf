///////////////////////////////////////////////////////////////////////////////////////
///////// > openSMILE configuration file for emotion features <      //////////////////
/////////   large set of 6552 features, 1st level functionals        //////////////////
/////////   of low-level descriptors such as MFCC, Pitch, LSP, ...   //////////////////
/////////                                                            //////////////////
/////////   (56 LLD + 56 delta) * 39 functionals                     //////////////////
/////////                                                            //////////////////
/////////  * written 2009 by <PERSON><PERSON><PERSON> *                         //////////////////
/////////                                                            //////////////////
///////// (c) audEERING UG (haftungsbeschränkt),                     //////////////////
/////////     All rights reserved.                                  //////////////////
///////////////////////////////////////////////////////////////////////////////////////

// NOTE: This file is no longer compatible with the old openEAR version.
// The ISxx sets should be preferred or the emobase set.

///////////////////////////////////////////////////////////////////////////////////////
;
; This section is always required in openSMILE configuration files
;   it configures the componentManager and gives a list of all components which are to be loaded
; The order in which the components are listed should match
;   the order of the data flow for most efficient processing
;
///////////////////////////////////////////////////////////////////////////////////////
[componentInstances:cComponentManager]
instance[dataMemory].type=cDataMemory
instance[waveIn].type=cWaveSource
instance[fr1].type=cFramer
instance[pe2].type=cVectorPreemphasis
instance[w1].type=cWindower
instance[fft1].type=cTransformFFT
instance[fftmp1].type=cFFTmagphase
instance[mspec].type=cMelspec
instance[mfcc].type=cMfcc
instance[mzcr].type=cMZcr
instance[acf].type=cAcf
instance[cepstrum].type=cAcf
instance[pitchACF].type=cPitchACF
instance[energy].type=cEnergy
instance[spectral].type=cSpectral
instance[lld].type=cContourSmoother
instance[delta1].type=cDeltaRegression
instance[delta2].type=cDeltaRegression
instance[functL1].type=cFunctionals
printLevelStats=0
nThreads=1


/////////////////////////////////////////////////////////////////////////////////////////////
/////////////////////////   component configuration  ////////////////////////////////////////
/////////////////////////////////////////////////////////////////////////////////////////////
; the following sections configure the components listed above
; a help on configuration parameters can be obtained with
;  SMILExtract -H
; or
;  SMILExtract -H configTypeName (= componentTypeName)
/////////////////////////////////////////////////////////////////////////////////////////////
[waveIn:cWaveSource]
writer.dmLevel=wave
filename=\cm[inputfile(I){test.wav}:name of input file]
monoMixdown=1

[fr1:cFramer]
reader.dmLevel=wave
writer.dmLevel=frames
noPostEOIprocessing = 1
copyInputName = 1
frameSize = 0.0250
frameStep = 0.010
frameMode = fixed
frameCenterSpecial = center
buffersize = 1000

[pe2:cVectorPreemphasis]
reader.dmLevel=frames
writer.dmLevel=framespe
k = 0.97
de = 0

[w1:cWindower]
reader.dmLevel=framespe
writer.dmLevel=winframe
copyInputName = 1
processArrayFields = 1
winFunc = ham
gain = 1.0
offset = 0

  // ---- LLD -----

[fft1:cTransformFFT]
reader.dmLevel=winframe
writer.dmLevel=fftc
copyInputName = 1
processArrayFields = 1
inverse = 0
 ; for compatibility with 2.2.0 and older versions
zeroPadSymmetric = 0

[fftmp1:cFFTmagphase]
reader.dmLevel=fftc
writer.dmLevel=fftmag
copyInputName = 1
processArrayFields = 1
inverse = 0
magnitude = 1
phase = 0

[mspec:cMelspec]
nameAppend=melspec
reader.dmLevel=fftmag
writer.dmLevel=mspec1
copyInputName = 1
processArrayFields = 1
htkcompatible = 1
usePower = 0
nBands = 26
lofreq = 0
hifreq = 8000
usePower = 0
inverse = 0
specScale = mel

[mfcc:cMfcc]
reader.dmLevel=mspec1
writer.dmLevel=mfcc1
copyInputName = 0
processArrayFields = 1
firstMfcc = 0
lastMfcc =  12
cepLifter = 22.0
htkcompatible = 1


[acf:cAcf]
reader.dmLevel=fftmag
writer.dmLevel=acf
nameAppend = acf
copyInputName = 1
processArrayFields = 1
usePower = 1
cepstrum = 0
acfCepsNormOutput = 0

[cepstrum:cAcf]
reader.dmLevel=fftmag
writer.dmLevel=cepstrum
nameAppend = acf
copyInputName = 1
processArrayFields = 1
usePower = 1
cepstrum = 1
acfCepsNormOutput = 0
oldCompatCepstrum = 1
absCepstrum = 1

[pitchACF:cPitchACF]
  ; the pitchACF component must ALWAYS read from acf AND cepstrum in the given order!
reader.dmLevel=acf;cepstrum
writer.dmLevel=pitch
copyInputName = 1
processArrayFields = 0
maxPitch = 500
voiceProb = 1
voiceQual = 0
HNR = 0
F0 = 1
F0raw = 0
F0env = 1
voicingCutoff = 0.550000

[energy:cEnergy]
reader.dmLevel=winframe
writer.dmLevel=energy
nameAppend=energy
copyInputName = 1
processArrayFields = 0
htkcompatible = 0
rms=0
log=1

[mzcr:cMZcr]
reader.dmLevel=frames
writer.dmLevel=mzcr
copyInputName = 1
processArrayFields = 1
zcr = 1
amax = 0
mcr = 0
maxmin = 0
dc = 0

[spectral:cSpectral]
reader.dmLevel=fftmag
writer.dmLevel=spectral
copyInputName = 1
processArrayFields = 1
squareInput = 1
bands[0]=0-250
bands[1]=0-650
bands[2]=250-650
bands[3]=1000-4000
;;;bands[4]=3010-9123
rollOff[0] = 0.25
rollOff[1] = 0.50
rollOff[2] = 0.75
rollOff[3] = 0.90
; buggyRollOff = 1
; set the above for compatibility with older releases
flux = 1
centroid = 1
maxPos = 1
minPos = 1
entropy = 0

[lld:cContourSmoother]
reader.dmLevel=energy;mfcc1;mspec1;mzcr;pitch;spectral
writer.dmLevel=lld
writer.levelconf.nT=10
;writer.levelconf.noHang=2
writer.levelconf.isRb=0
writer.levelconf.growDyn=1
;processArrayFields=0
nameAppend = sma
copyInputName = 1
noPostEOIprocessing = 0
smaWin = 3

  // ---- delta regression of LLD ----

[delta1:cDeltaRegression]
reader.dmLevel=lld
writer.dmLevel=lld_de
writer.levelconf.isRb=0
writer.levelconf.growDyn=1
nameAppend = de
copyInputName = 1
noPostEOIprocessing = 0
deltawin=2
blocksize=1

[delta2:cDeltaRegression]
reader.dmLevel=lld_de
writer.dmLevel=lld_dede
writer.levelconf.isRb=0
writer.levelconf.growDyn=1
nameAppend = de
copyInputName = 1
noPostEOIprocessing = 0
deltawin=2
blocksize=1


[functL1:cFunctionals]
reader.dmLevel=lld;lld_de;lld_dede
writer.dmLevel=func
copyInputName = 1
frameMode = full
 ; frameSize and frameStep = 0 => functionals over complete input
 ; (NOTE: buffersize of lld and lld_de levels must be large enough!!)
frameSize=0
frameStep=0
frameCenterSpecial = left
noPostEOIprocessing = 0
functionalsEnabled=Extremes;Regression;Moments;Percentiles;Crossings;Peaks;Means
Extremes.max = 0
Extremes.min = 0
Extremes.range = 1
Extremes.maxpos = 1
Extremes.minpos = 1
Extremes.amean = 0
Extremes.maxameandist = 1
Extremes.minameandist = 1
Extremes.norm = frame
Regression.linregc1 = 1
Regression.linregc2 = 1
Regression.linregerrA = 1
Regression.linregerrQ = 1
Regression.qregc1 = 1
Regression.qregc2 = 1
Regression.qregc3 = 1
Regression.qregerrA = 1
Regression.qregerrQ = 1
Regression.centroid = 1
Regression.oldBuggyQerr = 1
Regression.normInputs = 0
Regression.normRegCoeff = 0
Regression.doRatioLimit = 0
Regression.centroidRatioLimit = 0
Moments.doRatioLimit = 0
Moments.variance = 1
Moments.stddev = 1
Moments.skewness = 1
Moments.kurtosis = 1
Moments.amean = 0
Percentiles.quartiles = 1
Percentiles.quartile1 = 0
Percentiles.quartile2 = 0
Percentiles.quartile3 = 0
Percentiles.iqr = 1
Percentiles.iqr12 = 0
Percentiles.iqr23 = 0
Percentiles.iqr13 = 0
Percentiles.interp = 1
Percentiles.percentile[0] = 0.95
Percentiles.percentile[1] = 0.98
Crossings.zcr = 1
Crossings.mcr = 0
Crossings.amean = 0
Peaks.numPeaks = 1
Peaks.meanPeakDist = 1
Peaks.peakMean = 1
Peaks.peakMeanMeanDist = 1
Peaks.overlapFlag = 1
Means.amean = 1
Means.absmean = 1
Means.qmean = 1
Means.nzamean = 0
Means.nzabsmean = 1
Means.nzqmean = 1
Means.nzgmean = 1
Means.nnz = 1




  //////////////////////////////////////////////////////////////////////
 ///////////////////  data output configuration  //////////////////////
//////////////////////////////////////////////////////////////////////

// ----- you might need to customise the arff output to suit your needs: ------

\{../shared/standard_data_output.conf.inc}


//////---------------------- END -------------------------///////


