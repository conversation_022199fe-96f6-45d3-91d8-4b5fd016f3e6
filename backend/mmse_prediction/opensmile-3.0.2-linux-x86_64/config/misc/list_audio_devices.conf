
 ///////////////////////////////////////////////////////////////////////////
  // openSMILE configuration example for listing audio devices //
 ///////////////////////////////////////////////////////////////////////////


[componentInstances:cComponentManager]
instance[dataMemory].type = cDataMemory
instance[portaudioSource].type = cPortaudioSource
printLevelStats = 0
nThreads = 1

[portaudioSource:cPortaudioSource]
writer.dmLevel = wave
monoMixdown = 0
 ; -1 is the default device
device = \cm[device{-1}:portaudio device to use for recording, see -listDevices option]
listDevices = 1
sampleRate = \cm[sampleRate{44100}:set the sampling rate in Hz for recording]
channels = \cm[channels{2}:set the number of audio channels to record]
nBits = 16
audioBuffersize_sec = 0.050000
 


