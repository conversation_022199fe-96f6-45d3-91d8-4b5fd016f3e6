///////////////////////////////////////////////////////////////////////////////////////
///////// > openSMILE configuration file for speech prosody features //////////////////
/////////   pitch and loudness                                       //////////////////
/////////                                                            //////////////////
/////////  * written 2010 by <PERSON><PERSON><PERSON> *                         //////////////////
/////////                                                            //////////////////
///////// (c) audEERING UG (haftungsbeschränkt),                     //////////////////
/////////     All rights reserved.                                  //////////////////
///////////////////////////////////////////////////////////////////////////////////////

// This file is not fully up to date and might not work properly.
// Stay tuned for updates.

;;;;;;; component list ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;

[componentInstances:cComponentManager]
instance[dataMemory].type=cDataMemory
instance[waveIn].type=cPortaudioSource
instance[frame].type=cFramer
instance[int].type=cIntensity
instance[win].type=cWindower
instance[fft].type=cTransformFFT
instance[fftmp].type=cFFTmagphase
instance[scale].type=cSpecScale
instance[shs].type=cPitchShs
instance[smooth].type=cPitchSmoother
instance[smo].type=cContourSmoother
instance[csvsink].type=cCsvSink
printLevelStats=0

;;;;;;;;;;;;;;;;;;;;;;;;;;;; main section ;;;;;;;;;;;;;;;;;;;;;;;;;;;

[waveIn:cPortaudioSource]
writer.dmLevel=wave
monoMixdown = 0
 ; -1 is the default device, set listDevices=1 to see a device list
device = -1
listDevices = 0
sampleRate = 16000
 ; if your soundcard only supports stereo (2-channel) recording, 
 ; use channels=2 and set monoMixdown=1
channels = 1
nBits = 16
audioBuffersize_sec = 0.050000
buffersize_sec=2.0

[frame:cFramer]
reader.dmLevel=wave
writer.dmLevel=outp
frameSize = 0.050
frameStep = 0.010
frameCenterSpecial = left

[int:cIntensity]
reader.dmLevel = outp
writer.dmLevel = intens
// nameAppend =
copyInputName = 1
processArrayFields = 1
intensity = 0
loudness = 1

[win:cWindower]
reader.dmLevel=outp
writer.dmLevel=win
winFunc=gauss
gain=1.0
sigma=0.4

[fft:cTransformFFT]
reader.dmLevel=win
writer.dmLevel=fftc

[fftmp:cFFTmagphase]
reader.dmLevel=fftc
writer.dmLevel=fftmag

[scale:cSpecScale]
reader.dmLevel=fftmag
writer.dmLevel=hps
// nameAppend =
copyInputName = 1
processArrayFields = 0
scale=octave
sourceScale = lin
// logScaleBase = 2
// logSourceScaleBase = 2
// firstNote = 55
interpMethod = spline
minF = 25
maxF = -1
nPointsTarget = 0
specSmooth = 1
specEnhance = 1
auditoryWeighting = 1 

[shs:cPitchShs]
reader.dmLevel=hps
writer.dmLevel=pitchShs
// nameAppend =
copyInputName = 1
processArrayFields = 0
maxPitch = 620
minPitch = 52
nCandidates = 4
scores = 1
voicing = 1
F0C1 = 0
voicingC1 = 0
F0raw = 1
voicingClip = 1
voicingCutoff = 0.700000
inputFieldSearch = Mag_logScale
octaveCorrection = 0
nHarmonics = 15
compressionFactor = 0.850000

[smooth:cPitchSmoother]
reader.dmLevel=pitchShs
writer.dmLevel=pitch
// nameAppend =
copyInputName = 1
processArrayFields = 0
medianFilter0 = 0
postSmoothing = 0
postSmoothingMethod = simple
 ; note: octave correction is too agressive, thus we disable it..
octaveCorrection = 0
F0final = 1
F0finalEnv = 0
no0f0 = 0
voicingFinalClipped = 0
voicingFinalUnclipped = 1
F0raw = 0
voicingC1 = 0
voicingClip = 0

[smo:cContourSmoother]
reader.dmLevel = pitch;intens
writer.dmLevel = smo
nameAppend = sma
copyInputName = 1
noPostEOIprocessing = 0
smaWin = 3

[csvsink:cCsvSink]
reader.dmLevel=smo
filename=\cm[output(O){prosody.csv}:output csv file for pitch data]
append=0

