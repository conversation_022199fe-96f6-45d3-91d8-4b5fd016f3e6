///////////////////////////////////////////////////////////////////////////////////////
///////// > openSMILE config for SHS viterbi smoothed pitch <        //////////////////
/////////                                                            //////////////////
///////// (c) 2013-2016 audEERING.                                   //////////////////
/////////     All rights reserved. See file COPYING for details.    //////////////////
///////////////////////////////////////////////////////////////////////////////////////


//  output frame rate: 10ms
//  analysis window length : 60ms, gaussian window

\{smileF0_base.conf.inc}

[componentInstances:cComponent<PERSON>anager]
instance[F0_lldconcat].type=cVectorConcat

[F0_lldconcat:cVectorConcat]
reader.dmLevel = F0
writer.dmLevel = lld
includeSingleElementFields = 1

\{../shared/standard_data_output_lldonly.conf.inc}

