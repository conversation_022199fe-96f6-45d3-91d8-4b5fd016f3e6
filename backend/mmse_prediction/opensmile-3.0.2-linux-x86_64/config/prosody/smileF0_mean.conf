///////////////////////////////////////////////////////////////////////////////////////
///////// > openSMILE config for SHS viterbi smoothed pitch <        //////////////////
/////////                                                            //////////////////
///////// (c) 2013-2016 audEERING.                                   //////////////////
/////////     All rights reserved. See file COPYING for details.    //////////////////
///////////////////////////////////////////////////////////////////////////////////////

//  output frame rate: 10ms
//  analysis window length : 60ms, gaussian window

\{smileF0_base.conf.inc}

[componentInstances:cComponentManager]
instance[mean].type=cFunctionals

[mean:cFunctionals]
reader.dmLevel = F0
writer.dmLevel = F0mean
copyInputName = 1
frameMode = full
frameSize = 0
frameStep = 0
frameCenterSpecial = left
functionalsEnabled = Means
Means.amean = 1
Means.posamean = 0
Means.absmean = 0
Means.rqmean = 0
Means.flatness = 0
Means.qmean = 0
Means.nzamean = 0
Means.nzabsmean = 0
Means.nzqmean = 0
Means.nzgmean = 0
Means.nnz = 0
Means.norm = frames
nonZeroFuncts = 1

;;;;;;;;; output all features....

[componentInstances:cComponentManager]
instance[csv].type=cCsvSink

[csv:cCsvSink]
reader.dmLevel=F0mean
filename=\cm[output(O){pitch_mean.csv}:name of F0 output text file]
append=0
printHeader=0
number=0
timestamp=0


