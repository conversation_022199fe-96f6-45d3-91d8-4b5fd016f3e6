///////////////////////////////////////////////////////////////////////////////////////
///////// > openSMILE configuration file for speech prosody features //////////////////
/////////   pitch and loudness                                       //////////////////
/////////                                                            //////////////////
///////// (c) 2014-2016 audEERING.                                   //////////////////
/////////     All rights reserved. See file COPYING for details.    //////////////////
///////////////////////////////////////////////////////////////////////////////////////

// === Newest version of prosody features: === 
//
// Includes viterbi-smoothed SHS pitch
// Loudness via simple auditory band model
// HNR based on F0 harmonics and other spectral peaks

// Supports both summarised features (over full input) with -O option (ARFF format)
// and LLDs with -lld option  (disabled by default if option is not given)

[componentInstances:cComponentManager]
instance[dataMemory].type=cDataMemory
instance[waveIn].type=cWaveSource
printLevelStats=0

;;;;;;;;;;;;;;;;;;;;;;;;;;;; Wave input ;;;;;;;;;;;;;;;;;;;;;;;;;;;

[waveIn:cWaveSource]
writer.dmLevel=wave
filename=\cm[inputfile(I){test.wav}:name of input file]
monoMixdown=1

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; LOUDNESS ;;;;;;;;;;;;;;;;;;;;;;;;;

[componentInstances:cComponentManager]
instance[frame25].type=cFramer
instance[win25].type=cWindower
instance[fft25].type=cTransformFFT
instance[fftmp25].type=cFFTmagphase

[frame25:cFramer]
reader.dmLevel=wave
writer.dmLevel=frame25
frameSize = 0.020
frameStep = 0.010
frameCenterSpecial = left

[win25:cWindower]
reader.dmLevel=frame25
writer.dmLevel=winH25
winFunc=hamming

[fft25:cTransformFFT]
reader.dmLevel=winH25
writer.dmLevel=fftcH25
 ; for compatibility with 2.2.0 and older versions
zeroPadSymmetric = 0

[fftmp25:cFFTmagphase]
reader.dmLevel=fftcH25
writer.dmLevel=fftmagH25

[componentInstances:cComponentManager]
instance[melspec1].type=cMelspec
instance[audspec].type=cPlp
instance[audspecSum].type=cVectorOperation

[melspec1:cMelspec]
reader.dmLevel=fftmagH25
writer.dmLevel=melspec1
; htk compatible sample value scaling
htkcompatible = 0
nBands = 26
; use power spectrum instead of magnitude spectrum
usePower = 1
lofreq = 20
hifreq = 8000
specScale = mel
showFbank = 0

; perform auditory weighting of spectrum
[audspec:cPlp]
reader.dmLevel=melspec1
writer.dmLevel=audspec
firstCC = 0
lpOrder = 5
cepLifter = 22
compression = 0.33
htkcompatible = 0 
doIDFT = 0
doLpToCeps = 0
doLP = 0
doInvLog = 0
doAud = 1
doLog = 0
newRASTA=0
RASTA=0

[audspecSum:cVectorOperation]
reader.dmLevel = audspec
writer.dmLevel = loudness
writer.levelconf.growDyn = 0
writer.levelconf.isRb = 1
; This must be > than buffersize of viterbi smoother
writer.levelconf.nT = 200
nameAppend = loudness
copyInputName = 0
processArrayFields = 0
operation = ll1
nameBase = loudness

;;;;;;;;;;;;;;;;;; F0, SHS, Viterbi smoothed ;;;;;;;;;;;;;;;;;;;;;;;;;;
;;;;;;;;;;;;;;;;;; From 55 ms windows at 10 ms rate ;;;;;;;;;;;;;;;;;;;

[componentInstances:cComponentManager]
instance[frame].type=cFramer
instance[win].type=cWindower
instance[fft].type=cTransformFFT
instance[fftmp].type=cFFTmagphase
instance[scale].type=cSpecScale
instance[shs].type=cPitchShs
instance[pitchSmooth].type=cPitchSmootherViterbi
instance[harmonics].type = cHarmonics

[frame:cFramer]
reader.dmLevel=wave
writer.dmLevel=outp
frameSize = 0.055
frameStep = 0.010
frameCenterSpecial = left

[win:cWindower]
reader.dmLevel=outp
writer.dmLevel=win
winFunc=gauss
gain=1.0
sigma=0.4

[fft:cTransformFFT]
reader.dmLevel=win
writer.dmLevel=fftc
 ; for compatibility with 2.2.0 and older versions
zeroPadSymmetric = 0

[fftmp:cFFTmagphase]
reader.dmLevel=fftc
writer.dmLevel=fftmag
; greater than pitch smoother max. lag
writer.levelconf.nT = 200

[scale:cSpecScale]
reader.dmLevel=fftmag
writer.levelconf.nT = 3
writer.dmLevel=hps
// nameAppend =
copyInputName = 1
processArrayFields = 0
scale=octave
sourceScale = lin
// logScaleBase = 2
// logSourceScaleBase = 2
// firstNote = 55
interpMethod = spline
minF = 25
maxF = -1
nPointsTarget = 0
specSmooth = 1
specEnhance = 1
auditoryWeighting = 1 

[shs:cPitchShs]
reader.dmLevel=hps
writer.dmLevel=pitchShs
// nameAppend =
copyInputName = 1
processArrayFields = 0
maxPitch = 620
minPitch = 52
nCandidates = 4
scores = 1
voicing = 1
F0C1 = 0
voicingC1 = 0
F0raw = 1
voicingClip = 0
voicingCutoff = 0.700000
greedyPeakAlgo = 1
inputFieldSearch = Mag_octScale
octaveCorrection = 0
nHarmonics = 15
compressionFactor = 0.850000
lfCut = 0

[pitchSmooth:cPitchSmootherViterbi]
reader.dmLevel=pitchShs
reader2.dmLevel=pitchShs
writer.dmLevel=pitchG60
copyInputName = 1
bufferLength=90
F0final = 1
F0finalLog = 1
F0finalEnv = 0
voicingFinalClipped = 0
voicingFinalUnclipped = 1
F0raw = 0
voicingC1 = 0
voicingClip = 0
wTvv =10.0
wTvvd= 5.0
wTvuv=10.0
wThr = 4.0
wTuu = 0.0
wLocal=2.0
wRange=1.0

;;;;;;;;;;;; NEW HNR
[harmonics:cHarmonics]
reader.dmLevel = fftmag;pitchG60
writer.dmLevel = harmonics
writer.levelconf.growDyn = 0
writer.levelconf.isRb = 1
; This must be > than buffersize of viterbi smoother
writer.levelconf.nT = 200
nHarmonics = 10
f0ElementName = F0final
magSpecFieldName = pcm_fftMag
computeAcfHnrLogdB = 1

;;;;;;;;;;;;;;; PITCH POST PROCCESSING
;;;;;;;;;;;;;;; AND SMOOTHING OF LLD
[componentInstances:cComponentManager]
instance[energy60].type=cEnergy
instance[volmerge].type = cValbasedSelector
instance[smo].type = cContourSmoother
instance[smoNz].type = cContourSmoother

[energy60:cEnergy]
reader.dmLevel=win
writer.dmLevel=e60
rms=1
log=0
writer.levelconf.nT=200

[volmerge:cValbasedSelector]
reader.dmLevel = e60;pitchG60
writer.dmLevel = pitch
idx=0
threshold=0.0008
removeIdx=1
zeroVec=1
outputVal=0.0

[smo:cContourSmoother]
reader.dmLevel = loudness
writer.dmLevel = smo
writer.levelconf.growDyn = 1
writer.levelconf.isRb = 0
writer.levelconf.nT = 1000
nameAppend = sma
copyInputName = 1
noPostEOIprocessing = 0
smaWin = 3
noZeroSma = 0

[smoNz:cContourSmoother]
reader.dmLevel = pitch;harmonics
writer.dmLevel = smoNz
writer.levelconf.growDyn = 1
writer.levelconf.isRb = 0
writer.levelconf.nT = 1000
nameAppend = sma
copyInputName = 1
noPostEOIprocessing = 0
smaWin = 3
noZeroSma = 1

;;;;;;;;;;;;;;;;;; SUMMARIES over full input ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;

[componentInstances:cComponentManager]
instance[functionalsNz].type = cFunctionals
instance[functionals].type = cFunctionals

[functionals:cFunctionals]
reader.dmLevel = smo
writer.dmLevel = stats
frameMode = full
functionalsEnabled = Moments;Regression;Percentiles
Moments.variance = 0
Moments.stddev = 1
Moments.skewness = 0
Moments.kurtosis = 0
Moments.amean = 1
Moments.stddevNorm = 0
Moments.doRatioLimit = 0
Regression.linregc1 = 1
Regression.linregc2 = 0
Regression.linregerrA = 0
Regression.linregerrQ = 0
Regression.qregc1 = 0
Regression.qregc2 = 0
Regression.qregc3 = 0
Regression.qregerrA = 0
Regression.qregerrQ = 0
Regression.centroid = 1
Regression.centroidNorm = segment
Regression.oldBuggyQerr = 0
Regression.centroidUseAbsValues = 0
Regression.centroidRatioLimit = 0
Regression.doRatioLimit = 0
Percentiles.quartiles = 0
Percentiles.iqr = 0
Percentiles.percentile[0] = 0.1
Percentiles.percentile[1] = 0.9
Percentiles.pctlrange[0] = 0-1
Percentiles.interp = 1
nonZeroFuncts = 0

[functionalsNz:cFunctionals]
reader.dmLevel = smoNz
writer.dmLevel = statsNz
frameMode = full
functionalsEnabled = Moments;Regression;Percentiles
Moments.variance = 0
Moments.stddev = 1
Moments.skewness = 0
Moments.kurtosis = 0
Moments.amean = 1
Moments.stddevNorm = 0
Moments.doRatioLimit = 0
Regression.linregc1 = 1
Regression.linregc2 = 0
Regression.linregerrA = 0
Regression.linregerrQ = 0
Regression.qregc1 = 0
Regression.qregc2 = 0
Regression.qregc3 = 0
Regression.qregerrA = 0
Regression.qregerrQ = 0
Regression.centroid = 1
Regression.centroidNorm = segment
Regression.oldBuggyQerr = 0
Regression.centroidUseAbsValues = 0
Regression.centroidRatioLimit = 0
Regression.doRatioLimit = 0
Percentiles.quartiles = 0
Percentiles.iqr = 0
Percentiles.percentile[0] = 0.1
Percentiles.percentile[1] = 0.9
Percentiles.pctlrange[0] = 0-1
Percentiles.interp = 1
nonZeroFuncts = 1


;;;;;;;;;;;;;;;; DATA OUTPUT ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;
[componentInstances:cComponentManager]
instance[prosody_lldconcat].type=cVectorConcat
instance[prosody_llddeconcat].type=cVectorConcat
instance[prosody_funcconcat].type=cVectorConcat

[prosody_lldconcat:cVectorConcat]
reader.dmLevel = smo
writer.dmLevel = lld
includeSingleElementFields = 1

[prosody_llddeconcat:cVectorConcat]
reader.dmLevel = smoNz
writer.dmLevel = lld_de
includeSingleElementFields = 1

[prosody_funcconcat:cVectorConcat]
reader.dmLevel = statsNz;stats
writer.dmLevel = func
includeSingleElementFields = 1

\{../shared/standard_data_output.conf.inc}


