///////////////////////////////////////////////////////////////////////////////////////
/////////     openSMILE arff targets generic config file             //////////////////
///////// (c) audEERING UG (haftungsbeschränkt),                     //////////////////
/////////     All rights reserved.                                  //////////////////
///////////////////////////////////////////////////////////////////////////////////////

// IMPORTANT: This file is obsoleted by arff_targets.conf.inc !!
// It contains old examples, but should only be used as reference

//
// configuration of commandline options for target classes in an ARFF file
// (cArffSink)
//


// this is an example of a numeric class attribute
// you specify the numeric class label on the command-line by:
//  -label1 1.2345
// if you do not have labels, you can omit the -label1 option
// the default value "0.0" will be assigned to all instances
class[0].name = class
class[0].type = numeric
target[0].all = \cm[class{0.0}:instance numeric class label]

// this is an example of a nominal class attribute
// you specify the class label on the command-line by:
//  -label1 class1
// if you do not have labels, you can omit the -label2 option
// the default value "0.0" will be assigned to all instances.
// NOTE: if you are using only a nominal class label (i.e. no
// numeric class attribute), change class[1] to class[0], etc.
/*
class[1].name = class
class[1].type = {class1,class2,class3,unknown}
target[1].all = \cm[label2{unknown}:instance nominal class label]
*/

// This is an example of adding a string, e.g. lyrics or transcribed spoken text
// Use on the command-line as:
// -words "this is a sentence..."
// OR: -w "this is a sentence..."
/*
class[2].name = words
class[2].type = string
target[2].all = \cm[words(w){empty}:transcribed text]
*/

