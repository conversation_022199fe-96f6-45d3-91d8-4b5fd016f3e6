all # Lexical features go on the first line. Everything after the '#' is ignored.
all # Pragmatic features go on the second line
all # Semantic features on the third line. If you write 'all', then all semantic features will be extracted.
all # Syntactic features on the fourth line. If you write nothing, then no syntactic features will be extracted.

# Extra lines are ignored.
# Possible values:
# Lexical features: wordnet, cosine_distance, fillers, vocab_richness, mpqa, readability, stanford_sentiment, pos_counts, pos_ratios, freq_norms, image_norms, anew_norms, warringer_norms, density
# Pragmatic features: lda, rst
# Semantic features: wordnet
# Syntactic features: lu_complexity, parsetrees
