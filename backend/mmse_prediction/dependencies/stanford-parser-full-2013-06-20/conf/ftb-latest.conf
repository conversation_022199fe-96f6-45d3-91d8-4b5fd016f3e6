###########################
# Baseline FTB Datasets
#
# IMPORTANT: All paths should reference the base Arabic data directory
#
#       /u/nlp/data/FrenchTreebank/versionJune2010
#
###########################

NAME=FTB All
TYPE=edu.stanford.nlp.international.french.pipeline.FTBDataset
PATH=/u/nlp/data/FrenchTreebank/versionJune2010/corpus-fonctions
OUTPUT_ENCODING=UTF8
TVISITOR=edu.stanford.nlp.international.french.pipeline.FTBCorrectorVisitor
FLAT=true

;;

NAME=FTB Train
TYPE=edu.stanford.nlp.international.french.pipeline.FTBDataset
PATH=/u/nlp/data/FrenchTreebank/versionJune2010/corpus-fonctions
SPLIT=$JAVANLP_HOME/projects/core/src/edu/stanford/nlp/international/french/pipeline/splits/candito.train
OUTPUT_ENCODING=UTF8
TVISITOR=edu.stanford.nlp.international.french.pipeline.FTBCorrectorVisitor

;;

NAME=FTB Dev
TYPE=edu.stanford.nlp.international.french.pipeline.FTBDataset
PATH=/u/nlp/data/FrenchTreebank/versionJune2010/corpus-fonctions
SPLIT=$JAVANLP_HOME/projects/core/src/edu/stanford/nlp/international/french/pipeline/splits/candito.dev
OUTPUT_ENCODING=UTF8
TVISITOR=edu.stanford.nlp.international.french.pipeline.FTBCorrectorVisitor

;;

NAME=FTB Test
TYPE=edu.stanford.nlp.international.french.pipeline.FTBDataset
PATH=/u/nlp/data/FrenchTreebank/versionJune2010/corpus-fonctions
SPLIT=$JAVANLP_HOME/projects/core/src/edu/stanford/nlp/international/french/pipeline/splits/candito.test
OUTPUT_ENCODING=UTF8
TVISITOR=edu.stanford.nlp.international.french.pipeline.FTBCorrectorVisitor

;;
