<!-- build.xml file for ant for JavaNLP -->

<!-- A "project" describes a set of targets that may be requested
     when An<PERSON> is executed.  The "default" attribute defines the
     target which is executed if no specific target is requested,
     and the "basedir" attribute defines the current working directory
     from which <PERSON><PERSON> executes the requested task.  This is normally
     set to the current working directory.
-->

<project name="JavaNLP" default="compile" basedir=".">

  <property name="build.home"    value="${basedir}/classes"/>
  <property name="build.tests"    value="${basedir}/classes"/>
  <property name="docs.home"     value="${basedir}/docs"/>
  <property name="src.home"      value="${basedir}/src"/>
  <property name="javadoc.home"  value="${basedir}/javadoc"/>


<!--  ==================== Compilation Control Options ==================== -->

<!--

  These properties control option settings on the Javac compiler when it
  is invoked using the <javac> task.

  compile.debug        Should compilation include the debug option?

  compile.deprecation  Should compilation include the deprecation option?

  compile.optimize     Should compilation include the optimize option?

  compile.source       Source version compatibility

  compile.target       Target class version compatibility

-->

  <property name="compile.debug"       value="true"/>
  <property name="compile.deprecation" value="false"/>
  <property name="compile.optimize"    value="true"/>
  <property name="compile.source"      value="1.6" />
  <property name="compile.target"      value="1.6" />




<!-- ==================== All Target ====================================== -->

<!--

  The "all" target is a shortcut for running the "clean" target followed
  by the "compile" target, to force a complete recompile.

-->

  <target name="all" depends="clean,compile"
   description="Clean build and dist directories, then compile"/>



<!-- ==================== Clean Target ==================================== -->

<!--

  The "clean" target deletes any previous "build" and "dist" directory,
  so that you can be ensured the application can be built from scratch.

-->

  <target name="clean" description="Delete old classes">
    <delete dir="${build.home}/edu"/>
  </target>


<!-- ==================== Classpath Targets ==================================== -->

<!--

  Sets the classpath for this project properly. We now always use the
  lib dir within javanlp.

-->

  <target name="classpath" description="Sets the classpath">
      <path id="compile.classpath">
        <fileset dir="${basedir}">
          <include name="*.jar"/>
          <exclude name="stanford-parser*"/>
        </fileset>
      </path>
  </target>





<!-- ==================== Compile Target ================================== -->

<!--

  The "compile" target transforms source files (from your "src" directory)
  into object files in the appropriate location in the build directory.
  This example assumes that you will be including your classes in an
  unpacked directory hierarchy under "/WEB-INF/classes".

-->

  <target name="compile" depends="prepare,classpath"
   description="Compile Java sources">

    <!-- Compile Java classes as necessary -->
    <mkdir    dir="${build.home}"/>
    <javac srcdir="${src.home}"
          destdir="${build.home}"
            debug="${compile.debug}"
         encoding="utf-8" 
      deprecation="${compile.deprecation}"
         optimize="${compile.optimize}"
	   source="${compile.source}"
           target="${compile.target}"
includeantruntime="false">
        <classpath refid="compile.classpath"/>
      <compilerarg value="-Xmaxerrs"/>
      <compilerarg value="20"/>
      <!-- <compilerarg value="-Xlint"/> -->
    </javac>

    <!-- Copy application resources -->
<!--
    <copy  todir="${build.home}/WEB-INF/classes">
      <fileset dir="${src.home}" excludes="**/*.java"/>
    </copy>
-->

  </target>


<!-- ==================== Javadoc Target ================================== -->

<!--

  The "javadoc" target creates Javadoc API documentation for the Java
  classes included in your application.  Normally, this is only required
  when preparing a distribution release, but is available as a separate
  target in case the developer wants to create Javadocs independently.

-->

  <target name="javadoc" depends="compile"
   description="Create Javadoc API documentation">

    <mkdir          dir="${javadoc.home}"/>
    <javadoc sourcepath="${src.home}"
                destdir="${javadoc.home}"
              maxmemory="768m"
                 author="true"
                 source="1.6"
                Overview="${src.home}/edu/stanford/nlp/overview.html"
                Doctitle="Stanford JavaNLP API Documentation"
             Windowtitle="Stanford JavaNLP API"
           packagenames="*">
      <bottom><![CDATA[<FONT SIZE=2><A HREF=\"http://nlp.stanford.edu\">Stanford NLP Group</A></FONT>]]></bottom>
      <link href="http://java.sun.com/j2se/1.6.0/docs/api/"/>
    </javadoc>

  </target>


<!-- ==================== Prepare Target ================================== -->

<!--

  The "prepare" target is used to create the "build" destination directory,
  and copy the static contents of your web application to it.  If you need
  to copy static files from external dependencies, you can customize the
  contents of this task.

  Normally, this task is executed indirectly when needed.

-->

  <target name="prepare">

    <!-- Create build directories as needed -->
    <mkdir  dir="${build.home}"/>

  </target>

</project>
