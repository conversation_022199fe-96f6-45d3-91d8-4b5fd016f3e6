## tagger training invoked at Thu Dec 15 01:17:21 PST 2011 with arguments:
                   model = english-left3words-distsim.tagger
                    arch = left3words,naacl2003unknowns,wordshapes(-1,1),distsim(/u/nlp/data/pos_tags_are_useless/egw4-reut.512.clusters,-1,1),distsimconjunction(/u/nlp/data/pos_tags_are_useless/egw4-reut.512.clusters,-1,1)
               trainFile = /u/nlp/data/pos-tagger/english/train-wsj-0-18;/u/nlp/data/pos-tagger/english/train-extra-english
         closedClassTags = 
 closedClassTagThreshold = 40
 curWordMinFeatureThresh = 2
                   debug = false
             debugPrefix = 
            tagSeparator = _
                encoding = UTF-8
              iterations = 100
                    lang = english
    learnClosedClassTags = false
        minFeatureThresh = 2
           openClassTags = 
rareWordMinFeatureThresh = 10
          rareWordThresh = 5
                  search = owlqn
                    sgml = false
            sigmaSquared = 0.0
                   regL1 = 0.75
               tagInside = 
                tokenize = true
        tokenizerFactory = 
        tokenizerOptions = 
                 verbose = false
          verboseResults = true
    veryCommonWordThresh = 250
                xmlInput = 
              outputFile = 
            outputFormat = slashTags
     outputFormatOptions = 
