<!-- build.xml file for ant for Stanford CoreNLP -->

<!-- Before using this, unjar the sources' jar file into the src/ directory! -->

<!-- A "project" describes a set of targets that may be requested
     when Ant is executed.  The "default" attribute defines the
     target which is executed if no specific target is requested,
     and the "basedir" attribute defines the current working directory
     from which <PERSON><PERSON> executes the requested task.  This is normally
     set to the current working directory.
-->

<project name="StanfordCoreNLP" default="compile" basedir=".">

  <property name="build.home"    value="${basedir}/classes"/>
  <property name="build.tests"    value="${basedir}/classes"/>
  <property name="src.home"      value="${basedir}/src"/>


<!--  ==================== Compilation Control Options ==================== -->

<!--

  These properties control option settings on the Javac compiler when it
  is invoked using the <javac> task.

  compile.debug        Should compilation include the debug option?

  compile.deprecation  Should compilation include the deprecation option?

  compile.optimize     Should compilation include the optimize option?

  compile.source       Source version compatibility

  compile.target       Target class version compatibility

-->

  <property name="compile.debug"       value="true"/>
  <property name="compile.deprecation" value="false"/>
  <property name="compile.encoding"    value="utf-8" />
  <property name="compile.optimize"    value="true"/>
  <property name="compile.source"      value="1.8" />
  <property name="compile.target"      value="1.8" />




<!-- ==================== All Target ====================================== -->

<!--

  The "all" target is a shortcut for running the "clean" target followed
  by the "compile" target, to force a complete recompile.

-->

  <target name="all" depends="clean,compile"
   description="Clean build and dist directories, then compile"/>



<!-- ==================== Clean Target ==================================== -->

<!--

  The "clean" target deletes any previous "build" and "dist" directory,
  so that you can be ensured the application can be built from scratch.

-->

  <target name="clean" description="Delete old classes">
    <delete dir="${build.home}/edu"/>
  </target>



<!-- ==================== Compile Target ================================== -->

<!--

  The "compile" target transforms source files (from your "src" directory)
  into object files in the appropriate location in the build directory.
  This example assumes that you will be including your classes in an
  unpacked directory hierarchy under "/WEB-INF/classes".

-->

  <target name="compile" depends="prepare"
   description="Compile Java sources">

    <!-- Compile Java classes as necessary -->
    <mkdir    dir="${build.home}"/>
    <javac srcdir="${src.home}"
          destdir="${build.home}"
            debug="${compile.debug}"
      deprecation="${compile.deprecation}"
         encoding="${compile.encoding}" 
         optimize="${compile.optimize}"
	   source="${compile.source}"
           target="${compile.target}">
      <compilerarg value="-Xmaxerrs"/>
      <compilerarg value="20"/>
      <classpath>
        <fileset dir="${basedir}">
          <include name="*.jar"/>
          <exclude name="stanford-corenlp*"/>
        </fileset>
      </classpath>
      <!-- <compilerarg value="-Xlint"/> -->
    </javac>
  </target>


<!-- ==================== Prepare Target ================================== -->

<!--

  The "prepare" target is used to create the "build" destination directory,
  and copy the static contents of your web application to it.  If you need
  to copy static files from external dependencies, you can customize the
  contents of this task.

  Normally, this task is executed indirectly when needed.

-->

  <target name="prepare">

    <!-- Create build directories as needed -->
    <mkdir  dir="${build.home}"/>

  </target>

</project>
