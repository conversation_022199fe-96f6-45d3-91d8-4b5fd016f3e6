<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <groupId>edu.stanford.nlp</groupId>
  <artifactId>stanford-corenlp</artifactId>
  <version>3.7.0</version>
  <packaging>jar</packaging>
  <name>Stanford CoreNLP</name>
  <description>Stanford CoreNLP provides a set of natural language analysis tools which can take raw English language text input and give the base forms of words, their parts of speech, whether they are names of companies, people, etc., normalize dates, times, and numeric quantities, mark up the structure of sentences in terms of phrases and word dependencies, and indicate which noun phrases refer to the same entities. It provides the foundational building blocks for higher level text understanding applications.</description>
  <url>http://nlp.stanford.edu/software/corenlp.shtml</url>
  <licenses>
    <license>
      <name>GNU General Public License Version 3</name>
      <url>http://www.gnu.org/licenses/gpl-3.0.txt</url>
    </license>
  </licenses>
  <scm>
    <url>http://nlp.stanford.edu/software/stanford-corenlp-2016-10-31.zip</url>
    <connection>http://nlp.stanford.edu/software/stanford-corenlp-2016-10-31.zip</connection>
  </scm>
  <developers>
    <developer>
      <id>christopher.manning</id>
      <name>Christopher Manning</name>
      <email><EMAIL></email>
    </developer>
    <developer>
      <id>jason.bolton</id>
      <name>Jason Bolton</name>
      <email><EMAIL></email>
    </developer>
  </developers>
  <properties>
    <maven.compiler.source>1.8</maven.compiler.source>
    <maven.compiler.target>1.8</maven.compiler.target>
    <encoding>UTF-8</encoding>
  </properties>
  <dependencies>

    <dependency>
      <groupId>com.apple</groupId>
      <artifactId>AppleJavaExtensions</artifactId>
      <version>1.4</version>
    </dependency>

    <dependency>
      <groupId>de.jollyday</groupId>
      <artifactId>jollyday</artifactId>
      <version>0.4.9</version>
    </dependency>

    <dependency>
      <groupId>org.apache.commons</groupId>
      <artifactId>commons-lang3</artifactId>
      <version>3.3.1</version>
    </dependency>

    <dependency>
      <groupId>org.apache.lucene</groupId>
      <artifactId>lucene-queryparser</artifactId>
      <version>4.10.3</version>
    </dependency>

    <dependency>
      <groupId>org.apache.lucene</groupId>
      <artifactId>lucene-analyzers-common</artifactId>
      <version>4.10.3</version>
    </dependency>

    <dependency>
      <groupId>org.apache.lucene</groupId>
      <artifactId>lucene-queries</artifactId>
      <version>4.10.3</version>
    </dependency>

    <dependency>
      <groupId>org.apache.lucene</groupId>
      <artifactId>lucene-core</artifactId>
      <version>4.10.3</version>
    </dependency>

    <dependency>
      <groupId>javax.servlet</groupId>
      <artifactId>javax.servlet-api</artifactId>
      <version>3.0.1</version>
    </dependency>

    <dependency>
      <groupId>com.io7m.xom</groupId>
      <artifactId>xom</artifactId>
      <version>1.2.10</version>
    </dependency>

    <dependency>
      <groupId>joda-time</groupId>
      <artifactId>joda-time</artifactId>
      <version>2.9</version>
    </dependency>

    <dependency>
      <groupId>com.googlecode.efficient-java-matrix-library</groupId>
      <artifactId>ejml</artifactId>
      <version>0.23</version>
    </dependency>

    <dependency>
      <groupId>org.glassfish</groupId>
      <artifactId>javax.json</artifactId>
      <version>1.0.4</version>
    </dependency>

    <dependency>
      <groupId>org.slf4j</groupId>
      <artifactId>slf4j-api</artifactId>
      <version>1.7.12</version>
    </dependency>

    <dependency>
      <groupId>com.google.protobuf</groupId>
      <artifactId>protobuf-java</artifactId>
      <version>2.6.1</version>
    </dependency>

    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <version>4.12</version>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>com.pholser</groupId>
      <artifactId>junit-quickcheck-core</artifactId>
      <version>0.5</version>
      <scope>test</scope>
    </dependency>
    
    <dependency>
      <groupId>com.pholser</groupId>
      <artifactId>junit-quickcheck-generators</artifactId>
      <version>0.5</version>
      <scope>test</scope>
    </dependency>

  </dependencies>
  <build>
    <sourceDirectory>src</sourceDirectory>
    <testSourceDirectory>test/src</testSourceDirectory>
    <plugins>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>build-helper-maven-plugin</artifactId>
        <version>1.7</version>
        <executions>
          <execution>
            <id>attach-models</id>
            <phase>package</phase>
            <goals>
              <goal>attach-artifact</goal>
            </goals>
            <configuration>
              <artifacts>
                <artifact>
                  <file>${project.basedir}/stanford-corenlp-3.7.0-models.jar</file>
                  <type>jar</type>
                  <classifier>models</classifier>
                </artifact>
              </artifacts>
            </configuration>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>
</project>
