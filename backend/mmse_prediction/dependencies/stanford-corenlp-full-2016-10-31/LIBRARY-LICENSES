These are the 3rd party libraries which we currently distribute with Stanford CoreNLP.
All of them have licenses that are compatible with GPL v3.
All but the JSON library allow commercial use.
A compatible JSON library is a component of Java EE (JSR 374).

-----------------------------------------------------------------

ejml-0.23.jar

URL: http://ejml.org/

License: Gnu Lesser General Public License v.3 (through v 0.23; version 0.24 forward uses Apache 2.0)

-----------------------------------------------------------------

javax.json.jar

URL: http://central.maven.org/maven2/javax/json/javax.json-api/1.0/javax.json-api-1.0.pom
     https://json-processing-spec.java.net/

License: GNU GPLv2 with Classpath Exception (or CDDL)

-----------------------------------------------------------------

joda-time-2.9.4.jar

URL: http://www.joda.org/joda-time/

License: Apache License 2.0
    http://www.joda.org/joda-time/license.html

-----------------------------------------

jollyday-0.4.9.jar

URL: http://jollyday.sourceforge.net/

License: Apache License 2.0
    http://jollyday.sourceforge.net/license.html

-----------------------------------------

protobuf.jar

URL: https://github.com/google/protobuf/
     https://developers.google.com/protocol-buffers/

License: BSD 3-clause license
    https://github.com/google/protobuf/blob/master/LICENSE
    http://opensource.org/licenses/BSD-3-Clause

-----------------------------------------

xom-1.2.10.jar

Url: http://www.xom.nu/
    OR  http://www.cafeconleche.org/XOM/

License: LGPL v2.1 Gnu lesser general public license
    http://www.xom.nu/license.xhtml

-----------------------------------------

