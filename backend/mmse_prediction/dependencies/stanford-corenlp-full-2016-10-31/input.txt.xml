<?xml version="1.0" encoding="UTF-8"?>
<?xml-stylesheet href="CoreNLP-to-HTML.xsl" type="text/xsl"?>
<root>
  <document>
    <sentences>
      <sentence id="1">
        <tokens>
          <token id="1">
            <word>Stanford</word>
            <lemma>Stanford</lemma>
            <CharacterOffsetBegin>0</CharacterOffsetBegin>
            <CharacterOffsetEnd>8</CharacterOffsetEnd>
            <POS>NNP</POS>
            <NER>ORGANIZATION</NER>
            <Speaker>PER0</Speaker>
          </token>
          <token id="2">
            <word>University</word>
            <lemma>University</lemma>
            <CharacterOffsetBegin>9</CharacterOffsetBegin>
            <CharacterOffsetEnd>19</CharacterOffsetEnd>
            <POS>NNP</POS>
            <NER>ORGANIZATION</NER>
            <Speaker>PER0</Speaker>
          </token>
          <token id="3">
            <word>is</word>
            <lemma>be</lemma>
            <CharacterOffsetBegin>20</CharacterOffsetBegin>
            <CharacterOffsetEnd>22</CharacterOffsetEnd>
            <POS>VBZ</POS>
            <NER>O</NER>
            <Speaker>PER0</Speaker>
          </token>
          <token id="4">
            <word>located</word>
            <lemma>located</lemma>
            <CharacterOffsetBegin>23</CharacterOffsetBegin>
            <CharacterOffsetEnd>30</CharacterOffsetEnd>
            <POS>JJ</POS>
            <NER>O</NER>
            <Speaker>PER0</Speaker>
          </token>
          <token id="5">
            <word>in</word>
            <lemma>in</lemma>
            <CharacterOffsetBegin>31</CharacterOffsetBegin>
            <CharacterOffsetEnd>33</CharacterOffsetEnd>
            <POS>IN</POS>
            <NER>O</NER>
            <Speaker>PER0</Speaker>
          </token>
          <token id="6">
            <word>California</word>
            <lemma>California</lemma>
            <CharacterOffsetBegin>34</CharacterOffsetBegin>
            <CharacterOffsetEnd>44</CharacterOffsetEnd>
            <POS>NNP</POS>
            <NER>LOCATION</NER>
            <Speaker>PER0</Speaker>
          </token>
          <token id="7">
            <word>.</word>
            <lemma>.</lemma>
            <CharacterOffsetBegin>44</CharacterOffsetBegin>
            <CharacterOffsetEnd>45</CharacterOffsetEnd>
            <POS>.</POS>
            <NER>O</NER>
            <Speaker>PER0</Speaker>
          </token>
        </tokens>
        <dependencies type="basic-dependencies">
          <dep type="root">
            <governor idx="0">ROOT</governor>
            <dependent idx="4">located</dependent>
          </dep>
          <dep type="compound">
            <governor idx="2">University</governor>
            <dependent idx="1">Stanford</dependent>
          </dep>
          <dep type="nsubjpass">
            <governor idx="4">located</governor>
            <dependent idx="2">University</dependent>
          </dep>
          <dep type="auxpass">
            <governor idx="4">located</governor>
            <dependent idx="3">is</dependent>
          </dep>
          <dep type="case">
            <governor idx="6">California</governor>
            <dependent idx="5">in</dependent>
          </dep>
          <dep type="nmod">
            <governor idx="4">located</governor>
            <dependent idx="6">California</dependent>
          </dep>
          <dep type="punct">
            <governor idx="4">located</governor>
            <dependent idx="7">.</dependent>
          </dep>
        </dependencies>
        <dependencies type="collapsed-dependencies">
          <dep type="root">
            <governor idx="0">ROOT</governor>
            <dependent idx="4">located</dependent>
          </dep>
          <dep type="compound">
            <governor idx="2">University</governor>
            <dependent idx="1">Stanford</dependent>
          </dep>
          <dep type="nsubjpass">
            <governor idx="4">located</governor>
            <dependent idx="2">University</dependent>
          </dep>
          <dep type="auxpass">
            <governor idx="4">located</governor>
            <dependent idx="3">is</dependent>
          </dep>
          <dep type="case">
            <governor idx="6">California</governor>
            <dependent idx="5">in</dependent>
          </dep>
          <dep type="nmod:in">
            <governor idx="4">located</governor>
            <dependent idx="6">California</dependent>
          </dep>
          <dep type="punct">
            <governor idx="4">located</governor>
            <dependent idx="7">.</dependent>
          </dep>
        </dependencies>
        <dependencies type="collapsed-ccprocessed-dependencies">
          <dep type="root">
            <governor idx="0">ROOT</governor>
            <dependent idx="4">located</dependent>
          </dep>
          <dep type="compound">
            <governor idx="2">University</governor>
            <dependent idx="1">Stanford</dependent>
          </dep>
          <dep type="nsubjpass">
            <governor idx="4">located</governor>
            <dependent idx="2">University</dependent>
          </dep>
          <dep type="auxpass">
            <governor idx="4">located</governor>
            <dependent idx="3">is</dependent>
          </dep>
          <dep type="case">
            <governor idx="6">California</governor>
            <dependent idx="5">in</dependent>
          </dep>
          <dep type="nmod:in">
            <governor idx="4">located</governor>
            <dependent idx="6">California</dependent>
          </dep>
          <dep type="punct">
            <governor idx="4">located</governor>
            <dependent idx="7">.</dependent>
          </dep>
        </dependencies>
        <dependencies type="enhanced-dependencies">
          <dep type="root">
            <governor idx="0">ROOT</governor>
            <dependent idx="4">located</dependent>
          </dep>
          <dep type="compound">
            <governor idx="2">University</governor>
            <dependent idx="1">Stanford</dependent>
          </dep>
          <dep type="nsubjpass">
            <governor idx="4">located</governor>
            <dependent idx="2">University</dependent>
          </dep>
          <dep type="auxpass">
            <governor idx="4">located</governor>
            <dependent idx="3">is</dependent>
          </dep>
          <dep type="case">
            <governor idx="6">California</governor>
            <dependent idx="5">in</dependent>
          </dep>
          <dep type="nmod:in">
            <governor idx="4">located</governor>
            <dependent idx="6">California</dependent>
          </dep>
          <dep type="punct">
            <governor idx="4">located</governor>
            <dependent idx="7">.</dependent>
          </dep>
        </dependencies>
        <dependencies type="enhanced-plus-plus-dependencies">
          <dep type="root">
            <governor idx="0">ROOT</governor>
            <dependent idx="4">located</dependent>
          </dep>
          <dep type="compound">
            <governor idx="2">University</governor>
            <dependent idx="1">Stanford</dependent>
          </dep>
          <dep type="nsubjpass">
            <governor idx="4">located</governor>
            <dependent idx="2">University</dependent>
          </dep>
          <dep type="auxpass">
            <governor idx="4">located</governor>
            <dependent idx="3">is</dependent>
          </dep>
          <dep type="case">
            <governor idx="6">California</governor>
            <dependent idx="5">in</dependent>
          </dep>
          <dep type="nmod:in">
            <governor idx="4">located</governor>
            <dependent idx="6">California</dependent>
          </dep>
          <dep type="punct">
            <governor idx="4">located</governor>
            <dependent idx="7">.</dependent>
          </dep>
        </dependencies>
      </sentence>
      <sentence id="2">
        <tokens>
          <token id="1">
            <word>It</word>
            <lemma>it</lemma>
            <CharacterOffsetBegin>46</CharacterOffsetBegin>
            <CharacterOffsetEnd>48</CharacterOffsetEnd>
            <POS>PRP</POS>
            <NER>O</NER>
            <Speaker>PER0</Speaker>
          </token>
          <token id="2">
            <word>is</word>
            <lemma>be</lemma>
            <CharacterOffsetBegin>49</CharacterOffsetBegin>
            <CharacterOffsetEnd>51</CharacterOffsetEnd>
            <POS>VBZ</POS>
            <NER>O</NER>
            <Speaker>PER0</Speaker>
          </token>
          <token id="3">
            <word>a</word>
            <lemma>a</lemma>
            <CharacterOffsetBegin>52</CharacterOffsetBegin>
            <CharacterOffsetEnd>53</CharacterOffsetEnd>
            <POS>DT</POS>
            <NER>O</NER>
            <Speaker>PER0</Speaker>
          </token>
          <token id="4">
            <word>great</word>
            <lemma>great</lemma>
            <CharacterOffsetBegin>54</CharacterOffsetBegin>
            <CharacterOffsetEnd>59</CharacterOffsetEnd>
            <POS>JJ</POS>
            <NER>O</NER>
            <Speaker>PER0</Speaker>
          </token>
          <token id="5">
            <word>university</word>
            <lemma>university</lemma>
            <CharacterOffsetBegin>60</CharacterOffsetBegin>
            <CharacterOffsetEnd>70</CharacterOffsetEnd>
            <POS>NN</POS>
            <NER>O</NER>
            <Speaker>PER0</Speaker>
          </token>
          <token id="6">
            <word>,</word>
            <lemma>,</lemma>
            <CharacterOffsetBegin>70</CharacterOffsetBegin>
            <CharacterOffsetEnd>71</CharacterOffsetEnd>
            <POS>,</POS>
            <NER>O</NER>
            <Speaker>PER0</Speaker>
          </token>
          <token id="7">
            <word>founded</word>
            <lemma>found</lemma>
            <CharacterOffsetBegin>72</CharacterOffsetBegin>
            <CharacterOffsetEnd>79</CharacterOffsetEnd>
            <POS>VBN</POS>
            <NER>O</NER>
            <Speaker>PER0</Speaker>
          </token>
          <token id="8">
            <word>in</word>
            <lemma>in</lemma>
            <CharacterOffsetBegin>80</CharacterOffsetBegin>
            <CharacterOffsetEnd>82</CharacterOffsetEnd>
            <POS>IN</POS>
            <NER>O</NER>
            <Speaker>PER0</Speaker>
          </token>
          <token id="9">
            <word>1891</word>
            <lemma>1891</lemma>
            <CharacterOffsetBegin>83</CharacterOffsetBegin>
            <CharacterOffsetEnd>87</CharacterOffsetEnd>
            <POS>CD</POS>
            <NER>DATE</NER>
            <NormalizedNER>1891</NormalizedNER>
            <Speaker>PER0</Speaker>
            <Timex tid="t1" type="DATE">1891</Timex>
          </token>
          <token id="10">
            <word>.</word>
            <lemma>.</lemma>
            <CharacterOffsetBegin>87</CharacterOffsetBegin>
            <CharacterOffsetEnd>88</CharacterOffsetEnd>
            <POS>.</POS>
            <NER>O</NER>
            <Speaker>PER0</Speaker>
          </token>
        </tokens>
        <dependencies type="basic-dependencies">
          <dep type="root">
            <governor idx="0">ROOT</governor>
            <dependent idx="5">university</dependent>
          </dep>
          <dep type="nsubj">
            <governor idx="5">university</governor>
            <dependent idx="1">It</dependent>
          </dep>
          <dep type="cop">
            <governor idx="5">university</governor>
            <dependent idx="2">is</dependent>
          </dep>
          <dep type="det">
            <governor idx="5">university</governor>
            <dependent idx="3">a</dependent>
          </dep>
          <dep type="amod">
            <governor idx="5">university</governor>
            <dependent idx="4">great</dependent>
          </dep>
          <dep type="punct">
            <governor idx="5">university</governor>
            <dependent idx="6">,</dependent>
          </dep>
          <dep type="acl">
            <governor idx="5">university</governor>
            <dependent idx="7">founded</dependent>
          </dep>
          <dep type="case">
            <governor idx="9">1891</governor>
            <dependent idx="8">in</dependent>
          </dep>
          <dep type="nmod">
            <governor idx="7">founded</governor>
            <dependent idx="9">1891</dependent>
          </dep>
          <dep type="punct">
            <governor idx="5">university</governor>
            <dependent idx="10">.</dependent>
          </dep>
        </dependencies>
        <dependencies type="collapsed-dependencies">
          <dep type="root">
            <governor idx="0">ROOT</governor>
            <dependent idx="5">university</dependent>
          </dep>
          <dep type="nsubj">
            <governor idx="5">university</governor>
            <dependent idx="1">It</dependent>
          </dep>
          <dep type="cop">
            <governor idx="5">university</governor>
            <dependent idx="2">is</dependent>
          </dep>
          <dep type="det">
            <governor idx="5">university</governor>
            <dependent idx="3">a</dependent>
          </dep>
          <dep type="amod">
            <governor idx="5">university</governor>
            <dependent idx="4">great</dependent>
          </dep>
          <dep type="punct">
            <governor idx="5">university</governor>
            <dependent idx="6">,</dependent>
          </dep>
          <dep type="acl">
            <governor idx="5">university</governor>
            <dependent idx="7">founded</dependent>
          </dep>
          <dep type="case">
            <governor idx="9">1891</governor>
            <dependent idx="8">in</dependent>
          </dep>
          <dep type="nmod:in">
            <governor idx="7">founded</governor>
            <dependent idx="9">1891</dependent>
          </dep>
          <dep type="punct">
            <governor idx="5">university</governor>
            <dependent idx="10">.</dependent>
          </dep>
        </dependencies>
        <dependencies type="collapsed-ccprocessed-dependencies">
          <dep type="root">
            <governor idx="0">ROOT</governor>
            <dependent idx="5">university</dependent>
          </dep>
          <dep type="nsubj">
            <governor idx="5">university</governor>
            <dependent idx="1">It</dependent>
          </dep>
          <dep type="cop">
            <governor idx="5">university</governor>
            <dependent idx="2">is</dependent>
          </dep>
          <dep type="det">
            <governor idx="5">university</governor>
            <dependent idx="3">a</dependent>
          </dep>
          <dep type="amod">
            <governor idx="5">university</governor>
            <dependent idx="4">great</dependent>
          </dep>
          <dep type="punct">
            <governor idx="5">university</governor>
            <dependent idx="6">,</dependent>
          </dep>
          <dep type="acl">
            <governor idx="5">university</governor>
            <dependent idx="7">founded</dependent>
          </dep>
          <dep type="case">
            <governor idx="9">1891</governor>
            <dependent idx="8">in</dependent>
          </dep>
          <dep type="nmod:in">
            <governor idx="7">founded</governor>
            <dependent idx="9">1891</dependent>
          </dep>
          <dep type="punct">
            <governor idx="5">university</governor>
            <dependent idx="10">.</dependent>
          </dep>
        </dependencies>
        <dependencies type="enhanced-dependencies">
          <dep type="root">
            <governor idx="0">ROOT</governor>
            <dependent idx="5">university</dependent>
          </dep>
          <dep type="nsubj">
            <governor idx="5">university</governor>
            <dependent idx="1">It</dependent>
          </dep>
          <dep type="cop">
            <governor idx="5">university</governor>
            <dependent idx="2">is</dependent>
          </dep>
          <dep type="det">
            <governor idx="5">university</governor>
            <dependent idx="3">a</dependent>
          </dep>
          <dep type="amod">
            <governor idx="5">university</governor>
            <dependent idx="4">great</dependent>
          </dep>
          <dep type="punct">
            <governor idx="5">university</governor>
            <dependent idx="6">,</dependent>
          </dep>
          <dep type="acl">
            <governor idx="5">university</governor>
            <dependent idx="7">founded</dependent>
          </dep>
          <dep type="case">
            <governor idx="9">1891</governor>
            <dependent idx="8">in</dependent>
          </dep>
          <dep type="nmod:in">
            <governor idx="7">founded</governor>
            <dependent idx="9">1891</dependent>
          </dep>
          <dep type="punct">
            <governor idx="5">university</governor>
            <dependent idx="10">.</dependent>
          </dep>
        </dependencies>
        <dependencies type="enhanced-plus-plus-dependencies">
          <dep type="root">
            <governor idx="0">ROOT</governor>
            <dependent idx="5">university</dependent>
          </dep>
          <dep type="nsubj">
            <governor idx="5">university</governor>
            <dependent idx="1">It</dependent>
          </dep>
          <dep type="cop">
            <governor idx="5">university</governor>
            <dependent idx="2">is</dependent>
          </dep>
          <dep type="det">
            <governor idx="5">university</governor>
            <dependent idx="3">a</dependent>
          </dep>
          <dep type="amod">
            <governor idx="5">university</governor>
            <dependent idx="4">great</dependent>
          </dep>
          <dep type="punct">
            <governor idx="5">university</governor>
            <dependent idx="6">,</dependent>
          </dep>
          <dep type="acl">
            <governor idx="5">university</governor>
            <dependent idx="7">founded</dependent>
          </dep>
          <dep type="case">
            <governor idx="9">1891</governor>
            <dependent idx="8">in</dependent>
          </dep>
          <dep type="nmod:in">
            <governor idx="7">founded</governor>
            <dependent idx="9">1891</dependent>
          </dep>
          <dep type="punct">
            <governor idx="5">university</governor>
            <dependent idx="10">.</dependent>
          </dep>
        </dependencies>
      </sentence>
    </sentences>
    <coreference>
      <coreference>
        <mention representative="true">
          <sentence>1</sentence>
          <start>1</start>
          <end>3</end>
          <head>2</head>
          <text>Stanford University</text>
        </mention>
        <mention>
          <sentence>2</sentence>
          <start>1</start>
          <end>2</end>
          <head>1</head>
          <text>It</text>
        </mention>
      </coreference>
    </coreference>
  </document>
</root>
