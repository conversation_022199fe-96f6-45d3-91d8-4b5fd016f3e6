from mmse_prediction.pipeline_functions.nodes import whisper_en_features
from mmse_prediction.pipeline_functions.nodes import xlm_roberta_base_features
from mmse_prediction.pipeline_functions.nodes import gpt2_xl_features
from mmse_prediction.pipeline_functions.nodes import roberta_base_features
from mmse_prediction.pipeline_functions.nodes import is10_features
from mmse_prediction.pipeline_functions.nodes import digipsych_prosody_features
from mmse_prediction.pipeline_functions.nodes import vggish_features
from mmse_prediction.pipeline_functions.nodes import lexicosyntactic

class feature_extractor():

    def __init__(self, ):

        self.whisper_en_extractor = whisper_en_features.feature_extractor()
        self.xlm_roberta_base_extractor = xlm_roberta_base_features.feature_extractor()
        self.gpt2_xl_feats_extractor = gpt2_xl_features.feature_extractor()
        self.roberta_base_feats_extractor = roberta_base_features.feature_extractor()
        self.is10_extractor = is10_features.is10_OpenSmileRunner()
        self.digipsych_prosody_feats_extractor = digipsych_prosody_features.feature_extractor()
        self.vggish_feats_extractor = vggish_features.feature_extractor()
        self.lexicosyntactic_extractor = lexicosyntactic.Lexicosyntactic(cfg_file='default.conf')

    def extract(self, audio_path, trans_path, forced_align_path):

        # audio_path
        whisper_en = self.whisper_en_extractor.run(audio_path)
        is10 = self.is10_extractor.run(audio_path)
        digipsych_prosody_feats = self.digipsych_prosody_feats_extractor.run(audio_path)
        vggish_feats = self.vggish_feats_extractor.run(audio_path)

        # trans_path
        xlm_roberta_base = self.xlm_roberta_base_extractor.run(trans_path)
        gpt2_xl_feats = self.gpt2_xl_feats_extractor.run(trans_path)
        roberta_base_feats = self.roberta_base_feats_extractor.run(trans_path)

        # forced_align_path
        lexicosyntactic = self.lexicosyntactic_extractor.run(forced_align_path)

        feat_dict =  {}
        feat_dict['whisper_en'] = whisper_en
        feat_dict['is10'] = is10
        feat_dict['digipsych_prosody_feats'] = digipsych_prosody_feats
        feat_dict['vggish_feats'] = vggish_feats
        feat_dict['xlm_roberta_base'] = xlm_roberta_base
        feat_dict['gpt2_xl_feats'] = gpt2_xl_feats
        feat_dict['roberta_base_feats'] = roberta_base_feats
        feat_dict['lexicosyntactic'] = lexicosyntactic

        return feat_dict




