import os
import csv
import numpy as np
import pandas as pd
from scipy import stats
import pickle

from mmse_prediction.pipeline_functions.inferencer_functions import load_selected_index

def load_forced_aligment_trans(path):
    with open(path, 'rb') as f:
        tmp_dict = pickle.load(f)
        text = ' '.join([segment['text'] for segment in tmp_dict['segments']])
    return text

def is_dir_empty(dir_path):
    if len(os.listdir(dir_path)) == 0:
        return True
    else:
        return False

def ffmpeg_normalize(audio_file, out_path):
    os.system("ffmpeg-normalize %s -c:a pcm_s16le -ar 16000 -b:a 256k -ac 1 -ext wav -f -nt peak -t 0 -o %s" % (audio_file, out_path))

def get_readable_feature_name_and_description(feature_name, feature_type):
    """
    Convert technical feature names to readable names with descriptions
    """

    # IS10 feature mappings (based on openSMILE IS10_paraling configuration analysis)
    is10_mappings = {
        # Fundamental frequency features - SHS pitch detection with 6 candidates, 15 harmonics, 0.85 compression
        'F0final_sma_amean': ('Average Fundamental Frequency', 'Mean F0 from Sub-Harmonic Summation (52-620Hz range, 15 harmonics, 0.7 voicing cutoff)'),
        'F0final_sma_stddev': ('F0 Standard Deviation', 'Standard deviation of SHS F0 with Viterbi smoothing (3-frame moving average)'),
        'F0final_sma_range': ('F0 Range', 'Difference between max and min F0 values extracted by SHS algorithm'),
        'F0final_sma_maxPos': ('F0 Peak Position', 'Relative temporal position (0-1) of maximum F0 in utterance'),
        'F0final_sma_minPos': ('F0 Valley Position', 'Relative temporal position (0-1) of minimum F0 in utterance'),
        'F0final_sma_linregc1': ('F0 Linear Trend', 'Linear regression slope of F0 contour (positive=rising, negative=falling)'),
        'F0final_sma_linregerrQ': ('F0 Trend Stability', 'Quadratic error of F0 linear regression (lower=more linear contour)'),

        # Voicing probability from SHS pitch detector
        'voicingFinalUnclipped_sma_amean': ('Average Voicing Probability', 'Mean voicing probability from SHS (0=unvoiced, 1=voiced, 0.7 threshold)'),
        'voicingFinalUnclipped_sma_stddev': ('Voicing Probability Variation', 'Standard deviation of SHS voicing probability'),

        # Jitter and shimmer computed from pitch periods with 0.5 minimum cross-correlation
        'jitterLocal_sma_amean': ('Average Local Jitter', 'Mean local jitter: relative F0 period variation (min 2 periods, 0.5 min CC)'),
        'jitterLocal_sma_stddev': ('Jitter Variability', 'Standard deviation of local jitter measurements'),
        'shimmerLocal_sma_amean': ('Average Local Shimmer', 'Mean local shimmer: relative amplitude variation between periods'),
        'shimmerLocal_sma_stddev': ('Shimmer Variability', 'Standard deviation of local shimmer measurements'),

        # RMS energy from 25ms Hamming windows with 10ms step
        'pcm_RMSenergy_sma_amean': ('Average RMS Energy', 'Mean RMS energy from 25ms Hamming windows (10ms step, 3-frame smoothing)'),
        'pcm_RMSenergy_sma_stddev': ('RMS Energy Variation', 'Standard deviation of smoothed RMS energy'),
        'pcm_RMSenergy_sma_range': ('RMS Energy Range', 'Difference between max and min RMS energy values'),
        'pcm_RMSenergy_sma_maxPos': ('Energy Peak Position', 'Relative temporal position of maximum RMS energy'),
        'pcm_RMSenergy_sma_minPos': ('Energy Valley Position', 'Relative temporal position of minimum RMS energy'),

        # Auditory spectrum L1 norm from octave-scale FFT with auditory weighting
        'audspec_lengthL1norm_sma_amean': ('Average Spectral Magnitude', 'Mean L1 norm of auditory-weighted octave-scale spectrum'),
        'audspec_lengthL1norm_sma_stddev': ('Spectral Magnitude Variation', 'Standard deviation of auditory spectrum L1 norm'),

        # Perceptual loudness with auditory weighting
        'loudness_sma_amean': ('Average Perceptual Loudness', 'Mean perceptual loudness with auditory weighting (not intensity)'),
        'loudness_sma_stddev': ('Loudness Variation', 'Standard deviation of perceptual loudness'),

        # MFCC from 26-band mel-spectrum (20-8000Hz, cepLifter=22, coefficients 0-14)
        'mfcc_sma[0]_amean': ('MFCC-0 Mean', 'Mean of 0th MFCC (log energy, from 26-band mel-spectrum 20-8000Hz)'),
        'mfcc_sma[1]_amean': ('MFCC-1 Mean', 'Mean of 1st MFCC (spectral slope, cepstral liftering=22)'),
        'mfcc_sma[2]_amean': ('MFCC-2 Mean', 'Mean of 2nd MFCC (spectral shape from mel-frequency analysis)'),
        'mfcc_sma[3]_amean': ('MFCC-3 Mean', 'Mean of 3rd MFCC (spectral fine structure)'),
        'mfcc_sma[4]_amean': ('MFCC-4 Mean', 'Mean of 4th MFCC (higher-order spectral characteristics)'),
        'mfcc_sma[5]_amean': ('MFCC-5 Mean', 'Mean of 5th MFCC coefficient'),
        'mfcc_sma[6]_amean': ('MFCC-6 Mean', 'Mean of 6th MFCC coefficient'),
        'mfcc_sma[7]_amean': ('MFCC-7 Mean', 'Mean of 7th MFCC coefficient'),
        'mfcc_sma[8]_amean': ('MFCC-8 Mean', 'Mean of 8th MFCC coefficient'),
        'mfcc_sma[9]_amean': ('MFCC-9 Mean', 'Mean of 9th MFCC coefficient'),
        'mfcc_sma[10]_amean': ('MFCC-10 Mean', 'Mean of 10th MFCC coefficient'),
        'mfcc_sma[11]_amean': ('MFCC-11 Mean', 'Mean of 11th MFCC coefficient'),
        'mfcc_sma[12]_amean': ('MFCC-12 Mean', 'Mean of 12th MFCC coefficient'),
        'mfcc_sma[1]_stddev': ('MFCC-1 Variation', 'Standard deviation of 1st MFCC (spectral slope variability)'),
        'mfcc_sma[2]_stddev': ('MFCC-2 Variation', 'Standard deviation of 2nd MFCC (spectral shape variability)'),

        # Zero crossing rate from 25ms frames
        'pcm_zcr_sma_amean': ('Average Zero Crossing Rate', 'Mean zero crossing rate from 25ms frames (indicates voicing/noise)'),
        'pcm_zcr_sma_stddev': ('ZCR Variation', 'Standard deviation of zero crossing rate'),

        # Log mel-frequency bands from 8-band mel-spectrum (20-6500Hz)
        'logMelFreqBand_sma[0]_amean': ('Mel-Band 0 Energy', 'Mean log energy in mel-band 0 (lowest frequencies, 20-6500Hz range)'),
        'logMelFreqBand_sma[1]_amean': ('Mel-Band 1 Energy', 'Mean log energy in mel-band 1 (8-band mel-spectrum)'),
        'logMelFreqBand_sma[2]_amean': ('Mel-Band 2 Energy', 'Mean log energy in mel-band 2'),
        'logMelFreqBand_sma[3]_amean': ('Mel-Band 3 Energy', 'Mean log energy in mel-band 3'),
        'logMelFreqBand_sma[4]_amean': ('Mel-Band 4 Energy', 'Mean log energy in mel-band 4'),
        'logMelFreqBand_sma[5]_amean': ('Mel-Band 5 Energy', 'Mean log energy in mel-band 5'),
        'logMelFreqBand_sma[6]_amean': ('Mel-Band 6 Energy', 'Mean log energy in mel-band 6'),
        'logMelFreqBand_sma[7]_amean': ('Mel-Band 7 Energy', 'Mean log energy in mel-band 7 (highest frequencies)'),

        # Linear Predictive Coding features (8th order, autocorrelation method)
        'lspFreq_sma[0]_amean': ('LSP Frequency 0', 'Mean of 1st Line Spectral Pair frequency (from 8th-order LPC)'),
        'lspFreq_sma[1]_amean': ('LSP Frequency 1', 'Mean of 2nd Line Spectral Pair frequency'),
        'lspFreq_sma[2]_amean': ('LSP Frequency 2', 'Mean of 3rd Line Spectral Pair frequency'),
        'lspFreq_sma[3]_amean': ('LSP Frequency 3', 'Mean of 4th Line Spectral Pair frequency'),
        'lspFreq_sma[4]_amean': ('LSP Frequency 4', 'Mean of 5th Line Spectral Pair frequency'),
        'lspFreq_sma[5]_amean': ('LSP Frequency 5', 'Mean of 6th Line Spectral Pair frequency'),
        'lspFreq_sma[6]_amean': ('LSP Frequency 6', 'Mean of 7th Line Spectral Pair frequency'),
        'lspFreq_sma[7]_amean': ('LSP Frequency 7', 'Mean of 8th Line Spectral Pair frequency'),

        # Delta (first derivative) features
        'F0final_sma_de_amean': ('F0 Delta Mean', 'Mean of F0 first derivative (delta regression, 2-frame window)'),
        'mfcc_sma_de[1]_amean': ('MFCC-1 Delta Mean', 'Mean of 1st MFCC first derivative'),
        'pcm_RMSenergy_sma_de_amean': ('RMS Energy Delta Mean', 'Mean of RMS energy first derivative'),

        # Additional statistical functionals for all features
        'pcm_loudness_sma_maxPos': ('Loudness Peak Position', 'Relative temporal position of maximum loudness'),
        'pcm_loudness_sma_minPos': ('Loudness Valley Position', 'Relative temporal position of minimum loudness'),
        'pcm_loudness_sma_amean': ('Average Loudness', 'Mean perceptual loudness from intensity'),
        'pcm_loudness_sma_linregc1': ('Loudness Linear Trend', 'Linear regression slope of loudness contour'),
        'pcm_loudness_sma_stddev': ('Loudness Standard Deviation', 'Standard deviation of perceptual loudness'),
        'pcm_loudness_sma_skewness': ('Loudness Skewness', 'Skewness of loudness distribution'),
        'pcm_loudness_sma_kurtosis': ('Loudness Kurtosis', 'Kurtosis of loudness distribution'),
        'pcm_loudness_sma_quartile1': ('Loudness Q1', 'First quartile of loudness values'),
        'pcm_loudness_sma_quartile2': ('Loudness Median', 'Median (second quartile) of loudness values'),
        'pcm_loudness_sma_quartile3': ('Loudness Q3', 'Third quartile of loudness values'),
        'pcm_loudness_sma_iqr1-2': ('Loudness IQR 1-2', 'Interquartile range between Q1 and Q2'),
        'pcm_loudness_sma_iqr2-3': ('Loudness IQR 2-3', 'Interquartile range between Q2 and Q3'),
        'pcm_loudness_sma_iqr1-3': ('Loudness IQR 1-3', 'Interquartile range between Q1 and Q3'),
        'pcm_loudness_sma_percentile1.0': ('Loudness 1st Percentile', '1st percentile of loudness values'),
        'pcm_loudness_sma_percentile99.0': ('Loudness 99th Percentile', '99th percentile of loudness values'),
        'pcm_loudness_sma_pctlrange0-1': ('Loudness Percentile Range', 'Range between 1st and 99th percentiles'),
        'pcm_loudness_sma_upleveltime75': ('Loudness Uplevel Time 75%', 'Percentage of time above 75th percentile'),
        'pcm_loudness_sma_upleveltime90': ('Loudness Uplevel Time 90%', 'Percentage of time above 90th percentile'),

        # F0 envelope features
        'F0finEnv_sma_amean': ('F0 Envelope Mean', 'Mean of F0 envelope (smoothed F0 contour)'),
        'F0finEnv_sma_linregc1': ('F0 Envelope Trend', 'Linear regression slope of F0 envelope'),
        'F0finEnv_sma_linregerrQ': ('F0 Envelope Stability', 'Quadratic error of F0 envelope regression'),
        'F0finEnv_sma_stddev': ('F0 Envelope Variation', 'Standard deviation of F0 envelope'),

        # Jitter DDP (Directional Perturbation Quotient)
        'jitterDDP_sma_amean': ('Average DDP Jitter', 'Mean directional perturbation quotient jitter'),
        'jitterDDP_sma_stddev': ('DDP Jitter Variation', 'Standard deviation of DDP jitter'),

        # Turn-based features
        'F0final__Turn_onsetRate': ('F0 Turn Onset Rate', 'Rate of F0 turn onsets per time unit'),
        'F0final__Turn_duration': ('F0 Turn Duration', 'Average duration of F0 turns'),
    }

    # DigiPsych Prosody feature mappings
    prosody_mappings = {
        'Speech_Time_VADInt_1': ('Speech Duration (Level 1)', 'Total duration of speech activity at intensity level 1'),
        'Total_Time_VADInt_1': ('Total Duration (Level 1)', 'Total recording duration at intensity level 1'),
        'Pause_Time_VADInt_1': ('Pause Duration (Level 1)', 'Total duration of pauses at intensity level 1'),
        'Pause_Percentage_VADInt_1': ('Pause Percentage (Level 1)', 'Percentage of time spent in pauses at level 1'),
        'Pause_Speech_Ratio_VADInt_1': ('Pause-to-Speech Ratio (Level 1)', 'Ratio of pause time to speech time at level 1'),
        'Mean_Pause_Length_VADInt_1': ('Average Pause Length (Level 1)', 'Mean duration of individual pauses at level 1'),
        'Pause_Variability_VADInt_1': ('Pause Variability (Level 1)', 'Standard deviation of pause durations at level 1'),
        'Speech_Time_VADInt_2': ('Speech Duration (Level 2)', 'Total duration of speech activity at intensity level 2'),
        'Total_Time_VADInt_2': ('Total Duration (Level 2)', 'Total recording duration at intensity level 2'),
        'Pause_Time_VADInt_2': ('Pause Duration (Level 2)', 'Total duration of pauses at intensity level 2'),
        'Pause_Percentage_VADInt_2': ('Pause Percentage (Level 2)', 'Percentage of time spent in pauses at level 2'),
        'Pause_Speech_Ratio_VADInt_2': ('Pause-to-Speech Ratio (Level 2)', 'Ratio of pause time to speech time at level 2'),
        'Mean_Pause_Length_VADInt_2': ('Average Pause Length (Level 2)', 'Mean duration of individual pauses at level 2'),
        'Pause_Variability_VADInt_2': ('Pause Variability (Level 2)', 'Standard deviation of pause durations at level 2'),
        'Speech_Time_VADInt_3': ('Speech Duration (Level 3)', 'Total duration of speech activity at intensity level 3'),
        'Total_Time_VADInt_3': ('Total Duration (Level 3)', 'Total recording duration at intensity level 3'),
        'Pause_Time_VADInt_3': ('Pause Duration (Level 3)', 'Total duration of pauses at intensity level 3'),
        'Pause_Percentage_VADInt_3': ('Pause Percentage (Level 3)', 'Percentage of time spent in pauses at level 3'),
        'Pause_Speech_Ratio_VADInt_3': ('Pause-to-Speech Ratio (Level 3)', 'Ratio of pause time to speech time at level 3'),
        'Mean_Pause_Length_VADInt_3': ('Average Pause Length (Level 3)', 'Mean duration of individual pauses at level 3'),
        'Pause_Variability_VADInt_3': ('Pause Variability (Level 3)', 'Standard deviation of pause durations at level 3'),
    }

    # Lexicosyntactic feature mappings (based on actual extraction code in feature.py)
    lexicosyntactic_mappings = {
        # Basic lexical statistics computed from POS-tagged tokens
        'word_length': ('Average Word Length', 'Mean character count per word (computed from non-punctuation POS tokens)'),
        'NID': ('Noun Inflection Diversity', 'Count of words not in dictionary (excluding exceptions) indicating morphological complexity'),

        # Vocabulary richness measures using FreqDist
        'TTR': ('Type-Token Ratio', 'Ratio of unique words to total words (word_types/total_words)'),
        'brunet': ('Brunet Index', 'Vocabulary richness: total_words^(word_types^(-0.165))'),
        'honore': ('Honore Statistic', 'Vocabulary richness: 100*log(total_words)/(1-once_words/word_types)'),

        # POS count features from NLTK POS tagging
        'nouns': ('Noun Count', 'Count of NN, NNS, NNP, NNPS tags from NLTK POS tagger'),
        'verbs': ('Verb Count', 'Count of VB, VBD, VBG, VBN, VBP, VBZ tags'),
        'inflected_verbs': ('Inflected Verb Count', 'Count of VBD, VBG, VBN, VBP, VBZ (excluding base form VB)'),
        'light': ('Light Verb Count', 'Count of high-frequency verbs with reduced semantic content (predefined list)'),
        'function': ('Function Word Count', 'Count of function word POS tags (determiners, prepositions, conjunctions)'),
        'pronouns': ('Pronoun Count', 'Count of PRP, PRP$, WP, WP$ tags'),
        'determiners': ('Determiner Count', 'Count of DT, PDT, WDT tags'),
        'adverbs': ('Adverb Count', 'Count of RB, RBR, RBS, WRB tags'),
        'adjectives': ('Adjective Count', 'Count of JJ, JJR, JJS tags'),
        'prepositions': ('Preposition Count', 'Count of IN, TO tags'),
        'coordinate': ('Coordinate Conjunction Count', 'Count of CC tags (and, but, or, etc.)'),
        'subordinate': ('Subordinate Conjunction Count', 'Count of subordinating conjunctions from predefined word list'),
        'demonstratives': ('Demonstrative Count', 'Count of this, that, these, those tokens'),

        # POS ratio features normalized by total word count
        'nvratio': ('Noun-to-Verb Ratio', 'Ratio of noun count to verb count (nouns/verbs)'),
        'prp_ratio': ('Pronoun Ratio', 'Pronoun count divided by total words'),
        'noun_ratio': ('Noun Ratio', 'Noun count divided by total words'),
        'sub_coord_ratio': ('Subordinate-to-Coordinate Ratio', 'Ratio of subordinate to coordinate conjunctions'),

        # Frequency norms from large corpus databases
        'frequency': ('Average Word Frequency', 'Mean log frequency from corpus frequency norms (lemmatized words)'),
        'noun_frequency': ('Average Noun Frequency', 'Mean log frequency for noun tokens only'),
        'verb_frequency': ('Average Verb Frequency', 'Mean log frequency for verb tokens only'),

        # Psycholinguistic norms (age of acquisition, imageability, familiarity)
        'aoa': ('Age of Acquisition', 'Mean age of acquisition ratings from psycholinguistic databases'),
        'imageability': ('Imageability Rating', 'Mean imageability ratings (concreteness vs abstractness)'),
        'familiarity': ('Familiarity Rating', 'Mean familiarity ratings from psycholinguistic norms'),
        'noun_aoa': ('Noun Age of Acquisition', 'Mean age of acquisition for noun tokens only'),
        'noun_imageability': ('Noun Imageability', 'Mean imageability ratings for noun tokens only'),
        'noun_familiarity': ('Noun Familiarity', 'Mean familiarity ratings for noun tokens only'),
        'verb_aoa': ('Verb Age of Acquisition', 'Mean age of acquisition for verb tokens only'),
        'verb_imageability': ('Verb Imageability', 'Mean imageability ratings for verb tokens only'),
        'verb_familiarity': ('Verb Familiarity', 'Mean familiarity ratings for verb tokens only'),

        # ANEW emotional norms (Affective Norms for English Words database)
        'noun_anew_val_mean': ('Noun Valence Mean', 'Mean ANEW valence ratings for nouns (1=unpleasant, 9=pleasant)'),
        'noun_anew_val_std': ('Noun Valence Variability', 'Standard deviation of ANEW valence ratings for nouns'),
        'noun_anew_aro_mean': ('Noun Arousal Mean', 'Mean ANEW arousal ratings for nouns (1=calm, 9=excited)'),
        'noun_anew_aro_std': ('Noun Arousal Variability', 'Standard deviation of ANEW arousal ratings for nouns'),
        'noun_anew_dom_mean': ('Noun Dominance Mean', 'Mean ANEW dominance ratings for nouns (1=controlled, 9=controlling)'),
        'noun_anew_dom_std': ('Noun Dominance Variability', 'Standard deviation of ANEW dominance ratings for nouns'),

        # Warringer emotional norms (extended ANEW database)
        'verb_warringer_val_mean': ('Verb Valence Mean', 'Mean Warringer valence ratings for verbs'),
        'verb_warringer_aro_mean': ('Verb Arousal Mean', 'Mean Warringer arousal ratings for verbs'),
        'verb_warringer_dom_mean': ('Verb Dominance Mean', 'Mean Warringer dominance ratings for verbs'),

        # Lu syntactic complexity analyzer features
        'maxdepth': ('Maximum Parse Depth', 'Maximum depth of constituency parse trees (Lu complexity analyzer)'),
        'totaldepth': ('Total Parse Depth', 'Sum of all node depths in parse trees'),
        'meandepth': ('Mean Parse Depth', 'Average depth of all nodes in constituency parse trees'),
        'treeheight': ('Parse Tree Height', 'Average height of constituency parse trees'),

        # CFG phrase structure features from Stanford parser
        'PP_type_prop': ('PP Proportion', 'Proportion of prepositional phrases in CFG parse trees'),
        'VP_type_prop': ('VP Proportion', 'Proportion of verb phrases in CFG parse trees'),
        'NP_type_prop': ('NP Proportion', 'Proportion of noun phrases in CFG parse trees'),
        'PP_type_rate': ('PP Rate', 'Prepositional phrases per total words'),
        'VP_type_rate': ('VP Rate', 'Verb phrases per total words'),
        'NP_type_rate': ('NP Rate', 'Noun phrases per total words'),
        'average_PP_length': ('Average PP Length', 'Mean word count in prepositional phrases'),
        'average_VP_length': ('Average VP Length', 'Mean word count in verb phrases'),
        'average_NP_length': ('Average NP Length', 'Mean word count in noun phrases'),

        # Readability measures using syllable counting and sentence parsing
        'flesch_kincaid': ('Flesch-Kincaid Grade', 'Reading grade level: 0.39*(words/sentences) + 11.8*(syllables/words) - 15.59'),

        # Semantic coherence using word vectors
        'cosine_distance': ('Semantic Coherence', 'Average cosine distance between consecutive sentences using word embeddings'),

        # MPQA sentiment lexicon features
        'mpqa_positive': ('Positive Sentiment Count', 'Count of words with positive polarity in MPQA subjectivity lexicon'),
        'mpqa_negative': ('Negative Sentiment Count', 'Count of words with negative polarity in MPQA subjectivity lexicon'),
        'mpqa_subjectivity': ('Subjective Word Count', 'Count of subjective words from MPQA subjectivity lexicon'),

        # WordNet semantic features
        'wordnet_noun_hypernyms': ('Noun Hypernym Count', 'Count of noun hypernyms from WordNet semantic hierarchy'),
        'wordnet_verb_hypernyms': ('Verb Hypernym Count', 'Count of verb hypernyms from WordNet semantic hierarchy'),

        # Content density measures
        'content_density': ('Content Density', 'Ratio of content words (nouns, verbs, adjectives, adverbs) to function words'),

        # WordNet semantic depth and ambiguity features
        'avg_max_wn_depth_nn': ('Average Max WordNet Depth (Nouns)', 'Mean maximum depth of nouns in WordNet semantic hierarchy'),
        'sd_max_wn_depth_nn': ('WordNet Depth Std (Nouns)', 'Standard deviation of maximum WordNet depth for nouns'),
        'avg_min_wn_depth_nn': ('Average Min WordNet Depth (Nouns)', 'Mean minimum depth of nouns in WordNet hierarchy'),
        'sd_min_wn_depth_nn': ('WordNet Min Depth Std (Nouns)', 'Standard deviation of minimum WordNet depth for nouns'),
        'avg_wn_ambig_nn': ('Average WordNet Ambiguity (Nouns)', 'Mean number of WordNet senses per noun'),
        'sd_wn_ambig_nn': ('WordNet Ambiguity Std (Nouns)', 'Standard deviation of WordNet sense counts for nouns'),
        'kurt_wn_ambig_nn': ('WordNet Ambiguity Kurtosis (Nouns)', 'Kurtosis of WordNet ambiguity distribution for nouns'),
        'skew_wn_ambig_nn': ('WordNet Ambiguity Skewness (Nouns)', 'Skewness of WordNet ambiguity distribution for nouns'),

        'avg_max_wn_depth_vb': ('Average Max WordNet Depth (Verbs)', 'Mean maximum depth of verbs in WordNet semantic hierarchy'),
        'sd_max_wn_depth_vb': ('WordNet Depth Std (Verbs)', 'Standard deviation of maximum WordNet depth for verbs'),
        'avg_min_wn_depth_vb': ('Average Min WordNet Depth (Verbs)', 'Mean minimum depth of verbs in WordNet hierarchy'),
        'sd_min_wn_depth_vb': ('WordNet Min Depth Std (Verbs)', 'Standard deviation of minimum WordNet depth for verbs'),
        'avg_wn_ambig_vb': ('Average WordNet Ambiguity (Verbs)', 'Mean number of WordNet senses per verb'),
        'sd_wn_ambig_vb': ('WordNet Ambiguity Std (Verbs)', 'Standard deviation of WordNet sense counts for verbs'),
        'kurt_wn_ambig_vb': ('WordNet Ambiguity Kurtosis (Verbs)', 'Kurtosis of WordNet ambiguity distribution for verbs'),
        'skew_wn_ambig_vb': ('WordNet Ambiguity Skewness (Verbs)', 'Skewness of WordNet ambiguity distribution for verbs'),

        'avg_max_wn_depth': ('Average Max WordNet Depth (All)', 'Mean maximum depth of all words in WordNet semantic hierarchy'),
        'sd_max_wn_depth': ('WordNet Depth Std (All)', 'Standard deviation of maximum WordNet depth for all words'),
        'avg_min_wn_depth': ('Average Min WordNet Depth (All)', 'Mean minimum depth of all words in WordNet hierarchy'),
        'sd_min_wn_depth': ('WordNet Min Depth Std (All)', 'Standard deviation of minimum WordNet depth for all words'),
        'avg_wn_ambig': ('Average WordNet Ambiguity (All)', 'Mean number of WordNet senses per word'),
        'sd_wn_ambig': ('WordNet Ambiguity Std (All)', 'Standard deviation of WordNet sense counts for all words'),
        'kurt_wn_ambig': ('WordNet Ambiguity Kurtosis (All)', 'Kurtosis of WordNet ambiguity distribution for all words'),
        'skew_wn_ambig': ('WordNet Ambiguity Skewness (All)', 'Skewness of WordNet ambiguity distribution for all words'),

        # Semantic coherence features using cosine distance
        'ave_cos_dist': ('Average Cosine Distance', 'Mean cosine distance between consecutive sentences using word embeddings'),
        'min_cos_dist': ('Minimum Cosine Distance', 'Minimum cosine distance between consecutive sentences'),
        'cos_cutoff_00': ('Cosine Cutoff 0.0', 'Proportion of sentence pairs with cosine distance > 0.0'),
        'cos_cutoff_03': ('Cosine Cutoff 0.3', 'Proportion of sentence pairs with cosine distance > 0.3'),
        'cos_cutoff_05': ('Cosine Cutoff 0.5', 'Proportion of sentence pairs with cosine distance > 0.5'),

        # Filler words and disfluencies
        'fillers': ('Filler Word Count', 'Total count of filler words (um, uh, etc.)'),
        'um': ('Um Count', 'Count of "um" filler words'),
        'uh': ('Uh Count', 'Count of "uh" filler words'),

        # Moving Average Type-Token Ratio (MATTR)
        'MATTR_10': ('MATTR-10', 'Moving Average Type-Token Ratio with window size 10'),
        'MATTR_20': ('MATTR-20', 'Moving Average Type-Token Ratio with window size 20'),
        'MATTR_30': ('MATTR-30', 'Moving Average Type-Token Ratio with window size 30'),
        'MATTR_40': ('MATTR-40', 'Moving Average Type-Token Ratio with window size 40'),
        'MATTR_50': ('MATTR-50', 'Moving Average Type-Token Ratio with window size 50'),

        # MPQA sentiment features (counts)
        'mpqa_strong_positive': ('Strong Positive Words', 'Count of words with strong positive sentiment (MPQA lexicon)'),
        'mpqa_strong_negative': ('Strong Negative Words', 'Count of words with strong negative sentiment (MPQA lexicon)'),
        'mpqa_weak_positive': ('Weak Positive Words', 'Count of words with weak positive sentiment (MPQA lexicon)'),
        'mpqa_weak_negative': ('Weak Negative Words', 'Count of words with weak negative sentiment (MPQA lexicon)'),
        'mpqa_num': ('MPQA Word Count', 'Total count of words found in MPQA subjectivity lexicon'),

        # Readability measures
        'flesch_kinkaid': ('Flesch-Kincaid Grade', 'Flesch-Kincaid Grade Level (years of education needed)'),

        # Syllable statistics
        'avg_syll_per_word': ('Average Syllables per Word', 'Mean number of syllables per word'),
        'std_syll_per_word': ('Syllable Count Std', 'Standard deviation of syllables per word'),
        'kurt_syll_per_word': ('Syllable Count Kurtosis', 'Kurtosis of syllable count distribution'),
        'skew_syll_per_word': ('Syllable Count Skewness', 'Skewness of syllable count distribution'),

        # Stanford sentiment features
        'mean_stanford_sentiment_veryneg': ('Mean Very Negative Sentiment', 'Mean Stanford sentiment score for very negative class'),
        'mean_stanford_sentiment_neg': ('Mean Negative Sentiment', 'Mean Stanford sentiment score for negative class'),
        'mean_stanford_sentiment_neutral': ('Mean Neutral Sentiment', 'Mean Stanford sentiment score for neutral class'),
        'mean_stanford_sentiment_pos': ('Mean Positive Sentiment', 'Mean Stanford sentiment score for positive class'),
        'mean_stanford_sentiment_verypos': ('Mean Very Positive Sentiment', 'Mean Stanford sentiment score for very positive class'),
        'std_stanford_sentiment_veryneg': ('Std Very Negative Sentiment', 'Standard deviation of Stanford very negative sentiment scores'),
        'std_stanford_sentiment_neg': ('Std Negative Sentiment', 'Standard deviation of Stanford negative sentiment scores'),
        'std_stanford_sentiment_neutral': ('Std Neutral Sentiment', 'Standard deviation of Stanford neutral sentiment scores'),
        'std_stanford_sentiment_pos': ('Std Positive Sentiment', 'Standard deviation of Stanford positive sentiment scores'),
        'std_stanford_sentiment_verypos': ('Std Very Positive Sentiment', 'Standard deviation of Stanford very positive sentiment scores'),
    }

    # Select appropriate mapping based on feature type
    if feature_type == 'is10':
        mappings = is10_mappings
    elif feature_type == 'digipsych_prosody_feats':
        mappings = prosody_mappings
    elif feature_type == 'lexicosyntactic':
        mappings = lexicosyntactic_mappings
    else:
        # Return None for unknown feature types
        return None, None

    # Get readable name and description only if feature exists in mapping
    if feature_name in mappings:
        return mappings[feature_name]
    else:
        # Return None for unmapped features - they will be excluded
        return None, None

def obtain_per_subject_selected_feats(subject_feats, selected_fre, selected_index, features_type, features_name,
                                      feats_clsi_lower, feats_clsi_upper,
                                      feats_effect_size
                                      ):

    subject_feats_concated = np.concatenate((subject_feats), axis=1).flatten()
    whisper_en_list = []
    xlm_roberta_base_list = []
    gpt2_xl_feats_list = []
    roberta_base_feats_list = []
    is10_list = []
    digipsych_prosody_feats_list = []
    lexicosyntactic_list = []
    vggish_feats_list = []

    for top_i in range(len(selected_fre)):

        tmp_index = selected_index[top_i]

        fre_ = selected_fre[top_i]
        value_ = subject_feats_concated[tmp_index]
        type_ = features_type[tmp_index]
        name_ = features_name[tmp_index]
        lower_ = feats_clsi_lower[tmp_index]
        upper_ = feats_clsi_upper[tmp_index]

        effect_size_ = feats_effect_size[tmp_index]

        # Get readable feature name and description
        readable_name, description = get_readable_feature_name_and_description(name_, type_)

        # Only create dict and add to list if feature has proper description
        if readable_name is not None and description is not None and readable_name != 'Flesch Reading Ease':
            dict_ = {'selection frequency': fre_, 'value': value_,
                     'feature type': type_, 'feature name': readable_name,
                     'brief introduction': description,
                     'clsi lower': lower_, 'clsi upper': upper_,
                     'effect size': effect_size_
                     }
            
            if type_ == 'whisper_en':
                whisper_en_list.append(dict_)
            elif type_ == 'xlm_roberta_base':
                xlm_roberta_base_list.append(dict_)
            elif type_ == 'gpt2_xl_feats':
                gpt2_xl_feats_list.append(dict_)
            elif type_ == 'roberta_base_feats':
                roberta_base_feats_list.append(dict_)
            elif type_ == 'is10' and fre_ == 50:
                is10_list.append(dict_)
            elif type_ == 'digipsych_prosody_feats':
                digipsych_prosody_feats_list.append(dict_)
            elif type_ == 'lexicosyntactic' and fre_>= 10:
                lexicosyntactic_list.append(dict_)
            elif type_ == 'vggish_feats':
                vggish_feats_list.append(dict_)
        else:
            # Skip features without proper descriptions
            print(f"Skipping feature '{name_}' of type '{type_}' - no description found")

    selected_feats = {}
    selected_feats['is10'] = sorted(is10_list, key=lambda is10_list: is10_list['effect size'], reverse=True)
    selected_feats['digipsych_prosody_feats'] = sorted(digipsych_prosody_feats_list, key=lambda digipsych_prosody_feats_list: digipsych_prosody_feats_list['effect size'], reverse=True)
    selected_feats['lexicosyntactic'] = sorted(lexicosyntactic_list, key=lambda lexicosyntactic_list: lexicosyntactic_list['effect size'], reverse=True)

    return selected_feats

def load_feats_name(BASE_DIR, feat_names, shift):

    feats_name = []
    feats_type = []
    for feat_type, feat_shift in zip(feat_names, shift):

        feat_pt = os.path.join(BASE_DIR, 'mmse_prediction', 'feats_name', feat_type, 'S001.csv')

        if np.array(pd.read_csv(feat_pt, sep=';')).shape[1] > np.array(pd.read_csv(feat_pt, sep=',')).shape[1]:
            sep = ';'
        else:
            sep = ','

        feat_data = pd.read_csv(feat_pt, sep=sep)

        name_ = np.array(feat_data.columns)[feat_shift:]
        type_ = np.array([feat_type] * name_.shape[0])

        feats_name.append(name_)
        feats_type.append(type_)

    feats_name = np.concatenate(feats_name)
    feats_type = np.concatenate(feats_type)

    return feats_name, feats_type

def load_selection_fre(model_dir):

    all_fold_selected_index = []
    for fold in range(10):
        tmp_index = load_selected_index(model_dir, fold, 1234)
        all_fold_selected_index += tmp_index
    selection_fre = pd.Series(np.concatenate((all_fold_selected_index))).value_counts()
    selected_index = np.array(selection_fre.index)
    selected_fre = np.array(selection_fre.values)

    return selected_index, selected_fre

def load_tst_dict(meta_file, audio_dir, format):

    with open(meta_file, 'r') as file:

        reader = csv.reader(file, delimiter=',')
        header = next(reader)  # jump first row

        if 'ID' in header:
            tkdname_index = header.index('ID')
        elif 'tkdname' in header:
            tkdname_index = header.index('tkdname')

        mmse_index = header.index('mmse')

        mmses = []
        audio_paths = []
        for row in reader:

            tkdname = row[tkdname_index]

            if format not in tkdname:
                tkdname = tkdname + format

            audio_paths.append(os.path.join(audio_dir, tkdname))

            mmse = row[mmse_index]
            mmses.append(mmse)

    dict_ = {}
    dict_['audio_paths'] = np.array(audio_paths)
    dict_['mmses'] = np.array(mmses).astype(np.float32)

    return dict_

def detect_outliers_tukey(data):
    """
    使用Tukey方法检测异常值
    """
    Q1 = np.percentile(data, 25)
    Q3 = np.percentile(data, 75)
    IQR = Q3 - Q1
    lower_bound = Q1 - 1.5 * IQR
    upper_bound = Q3 + 1.5 * IQR
    return (data >= lower_bound) & (data <= upper_bound)

def clsi_reference_interval_complete(data, confidence=0.95):

    """
    完整的CLSI参考区间计算
    """
    # 1. 检查样本量
    if len(data) < 120:
        print("警告：样本量小于CLSI推荐的最小样本量120")

    # 2. 异常值处理
    mask = detect_outliers_tukey(data)
    clean_data = data[mask]

    # 3. 计算参考区间
    lower_percentile = ((1 - confidence) / 2) * 100
    upper_percentile = (1 - (1 - confidence) / 2) * 100

    lower = np.percentile(clean_data, lower_percentile)
    upper = np.percentile(clean_data, upper_percentile)

    # 4. 计算90%置信区间
    n = len(clean_data)
    rank_lower = int(lower_percentile / 100 * n)
    rank_upper = int(upper_percentile / 100 * n)

    sorted_data = np.sort(clean_data)

    # return {
    #     'reference_interval': (lower, upper),
    #     'sample_size': n,
    #     'original_sample_size': len(data),
    #     'outliers_removed': len(data) - n,
    #     'method': 'CLSI非参数法',
    #     'confidence_level': confidence,
    #     'data_range': (np.min(clean_data), np.max(clean_data))
    # }

    return lower, upper

import json
class NpEncoder(json.JSONEncoder):
    """
    Custom JSON encoder for NumPy data types.
    Converts NumPy integers, floats, and arrays to their native Python equivalents.
    """
    def default(self, obj):
        if isinstance(obj, np.integer):
            return int(obj)
        elif isinstance(obj, np.floating):
            return float(obj)
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        return super(NpEncoder, self).default(obj)

def build_prediction_result_dict(scores1, subject_text, subject_feats, selected_fre, selected_index,
                                features_type, features_name, feats_clsi_lower, feats_clsi_upper,
                                feats_effect_size, training_inv_mmses, trans_color):
    """
    Build the final prediction result dictionary

    Args:
        scores1: Predicted MMSE score
        subject_text: Transcribed text
        subject_feats: Subject features for selected features calculation
        selected_fre: Selected feature frequencies
        selected_index: Selected feature indices
        features_type: Feature types
        features_name: Feature names
        feats_clsi_lower: CLSI lower bounds
        feats_clsi_upper: CLSI upper bounds
        feats_effect_size: Effect sizes
        training_inv_mmses: Training inverse MMSE scores for percentile calculation
        trans_color: trans_color

    Returns:
        dict_: Complete prediction result dictionary
    """

    ## selected features
    selected_feats = obtain_per_subject_selected_feats(subject_feats, selected_fre, selected_index,
                                                       features_type, features_name,
                                                       feats_clsi_lower, feats_clsi_upper,
                                                       feats_effect_size)

    ## calculate percentile
    percentile = stats.percentileofscore(training_inv_mmses, scores1)

    # result dict
    dict_ = {}
    dict_['Predicted mmse score'] = np.round(scores1, 1)
    dict_['Transcribed'] = subject_text
    dict_['Selected features'] = selected_feats
    dict_['Percentile'] = np.round(percentile, 1)
    dict_['Model performance'] = {'RMSE': 3.79, 'Pearson correlation coefficient': 0.83}
    dict_['Model'] = 'HiSage-MMScore-en-1.0'
    dict_['Transcribed with color'] = trans_color

    return dict_