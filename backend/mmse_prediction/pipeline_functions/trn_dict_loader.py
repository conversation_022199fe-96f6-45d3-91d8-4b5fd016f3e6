import pickle
import numpy as np
from mmse_prediction.utilize import bin_interval, inv_bin_interval

import os
from core.settings import BASE_DIR

def sub_ids_dict(dict_, idx):

	trn_idx_ = dict_['picture_idxs']

	valid1 = np.ones(shape=trn_idx_.shape, dtype=int)
	valid1[np.where(trn_idx_ != idx)[0]] = 0
	valid_index = np.where(valid1 == 1)[0]

	for key in dict_.keys():
		dict_[key] = dict_[key][valid_index]

	return dict_

def sub_lang_dict(dict_, lang_):

	trn_lang_ = dict_['languages']

	valid1 = np.ones(shape=trn_lang_.shape, dtype=int)
	valid1[np.where(trn_lang_ != lang_)[0]] = 0
	valid_index = np.where(valid1 == 1)[0]

	for key in dict_.keys():
		dict_[key] = dict_[key][valid_index]

	return dict_

def load_ADReSS2020_subject_only():

	trn_dict_save_path = os.path.join(BASE_DIR, 'mmse_prediction', 'data/ADReSS2020_train_subject_only.pkl')

	with open(trn_dict_save_path, 'rb') as f:
		trn_datadict = pickle.load(f)

	trn_datadict['subject_ids'] = trn_datadict['subject_ids'] + 10000

	return trn_datadict

def load_TAUKADIAL_v2_subject_only():

	trn_dict_save_path = os.path.join(BASE_DIR, 'mmse_prediction', "data/TAUKADIAL_trn_dict_subject_only.pkl")

	with open(trn_dict_save_path, 'rb') as f:
		trn_datadict = pickle.load(f)

	trn_datadict = sub_lang_dict(trn_datadict, 'en')
	trn_datadict = sub_ids_dict(trn_datadict, 3)
	trn_datadict['subject_ids'] = trn_datadict['subject_ids'] + 40000

	return trn_datadict

def load_ADReSSo21_subject_only():

	trn_dict_save_path = os.path.join(BASE_DIR, 'mmse_prediction', 'data/ADReSSo21_train_subject_only.pkl')

	with open(trn_dict_save_path, 'rb') as f:
		trn_datadict = pickle.load(f)

	trn_datadict['subject_ids'] = trn_datadict['subject_ids'] + 20000

	return trn_datadict

def load_TAUKADIAL_v2():

	trn_dict_save_path = os.path.join(BASE_DIR, 'mmse_prediction', 'data/TAUKADIAL_trn_dict.pkl')

	with open(trn_dict_save_path, 'rb') as f:
		trn_datadict = pickle.load(f)

	trn_datadict = sub_lang_dict(trn_datadict, 'en')
	trn_datadict = sub_ids_dict(trn_datadict, 3)
	trn_datadict['subject_ids'] = trn_datadict['subject_ids'] + 40000

	return trn_datadict

def load_ADReSS2020():

	trn_dict_save_path = os.path.join(BASE_DIR, 'mmse_prediction', 'data/ADReSS2020_train.pkl')

	with open(trn_dict_save_path, 'rb') as f:
		trn_datadict = pickle.load(f)

	trn_datadict['subject_ids'] = trn_datadict['subject_ids'] + 10000

	return trn_datadict

def load_ADReSSo21():

	trn_dict_save_path = os.path.join(BASE_DIR, 'mmse_prediction', 'data/ADReSSo21_train.pkl')

	with open(trn_dict_save_path, 'rb') as f:
		trn_datadict = pickle.load(f)

	trn_datadict['subject_ids'] = trn_datadict['subject_ids'] + 20000

	return trn_datadict

def load_trn_datadict():

	### subjects and accessors
	ADReSS2020_trn_data_dict1 = load_ADReSS2020()
	ADReSSo21_trn_data_dict1 = load_ADReSSo21()
	TAUKADIAL_trn_data_dict1 = load_TAUKADIAL_v2()

	## subjects only
	ADReSS2020_trn_data_dict2 = load_ADReSS2020_subject_only()
	ADReSSo21_trn_data_dict2 = load_ADReSSo21_subject_only()
	TAUKADIAL_trn_data_dict2 = load_TAUKADIAL_v2_subject_only()

	trn_data_dict = {}
	for key in ADReSS2020_trn_data_dict1.keys():
		trn_data_dict[key] = np.concatenate((
			ADReSS2020_trn_data_dict1[key],
			ADReSSo21_trn_data_dict1[key],
			TAUKADIAL_trn_data_dict1[key],
			ADReSS2020_trn_data_dict2[key],
			ADReSSo21_trn_data_dict2[key],
			TAUKADIAL_trn_data_dict2[key],
		))

	trn_data_dict['lexicosyntactic'] = np.nan_to_num(trn_data_dict['lexicosyntactic'])
	trn_data_dict['mmses_bined'] = bin_interval(trn_data_dict['mmses'], 10)
	trn_data_dict['inv_mmses'] = inv_bin_interval(trn_data_dict['mmses_bined'], 10)

	return trn_data_dict


