import torch
import pandas as pd
from transformers import AutoModelForSpeechSeq2Seq, AutoProcessor, pipeline

import os
from core.settings import BASE_DIR

class transcribe_model():

    def __init__(self):

        device = "cpu"
        torch_dtype = torch.float32

        model_id = os.path.join(BASE_DIR, 'mmse_prediction', "transformers/huggingface_models/openai-whisper-large-v3")

        model = AutoModelForSpeechSeq2Seq.from_pretrained(
            model_id, torch_dtype=torch_dtype, low_cpu_mem_usage=True, use_safetensors=True, local_files_only=True
        )
        model.to(device)

        processor = AutoProcessor.from_pretrained(model_id)

        self.pipe = pipeline(
            "automatic-speech-recognition",
            model=model,
            tokenizer=processor.tokenizer,
            feature_extractor=processor.feature_extractor,
            # max_new_tokens=128,
            chunk_length_s=30,
            batch_size=1,
            # return_timestamps=False,
            return_timestamps=True,
            # return_language=True,
            torch_dtype=torch_dtype,
            device=device,
        )

    def transcribe(self, audio_file, out_path):

        result = self.pipe(audio_file, generate_kwargs={"language": 'english'})

        subject_texts = []
        subject_timestamps = []
        for chunk in result['chunks']:
            subject_texts.append(chunk['text'])
            subject_timestamps.append(chunk['timestamp'])

        subject_data = pd.DataFrame({'text': subject_texts, 'timestamp': subject_timestamps})
        subject_data.to_csv(out_path, index=False)

        torch.cuda.empty_cache()

        return ''.join(subject_texts)