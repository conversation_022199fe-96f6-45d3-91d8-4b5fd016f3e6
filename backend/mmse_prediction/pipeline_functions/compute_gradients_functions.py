"""
Gradient computation functions for saliency map calculation
Contains classes and functions for two-stage gradient computation:
1. Text -> features
2. Features -> NN inference -> MMSE scores
"""

import torch
import torch.nn as nn
import numpy as np
import pandas as pd
from torch.utils.data import DataLoader
from mmse_prediction.Interpret.integrated_gradient import IntegratedGradient

class SimpleTextDataset:
    """
    Simple dataset for saliency interpretation
    """
    def __init__(self, text):

        self.text = text
        self.device = 'cpu'

    def __len__(self):
        return 1

    def __getitem__(self, idx):
        batch = {
            'text': self.text,
        }
        return batch

class TextToModel(nn.Module):
    """
    First stage: Extract xlm_roberta_base features from text input
    """
    def __init__(self, feature_extractor):
        super(TextToModel, self).__init__()
        self.feature_extractor = feature_extractor

        # Determine the device to use
        # self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.device = 'cpu'

        # Ensure model is on the correct device
        if hasattr(self.feature_extractor, 'model'):
            self.feature_extractor.model = self.feature_extractor.model.to(self.device)

    def forward(self, text=None, tokenizer_params=None):
        """
        Forward pass: text -> base features
        :param text: raw text input (string)
        :param tokenizer_params: dict of tokenizer parameters
        :return: features tensor
        """

        assert tokenizer_params is not None, "Tokenizer parameters must be provided."

        # Use provided tokenizer parameters or default ones
        inputs = self.feature_extractor.tokenizer(text, **tokenizer_params)

        # Get model device and ensure inputs are on the same device
        model_device = next(self.feature_extractor.model.parameters()).device

        # Move inputs to the same device as the model
        for key, value in inputs.items():
            if isinstance(value, torch.Tensor):
                inputs[key] = value.to(model_device)

        model_output = self.feature_extractor.model(
            **inputs,
            output_hidden_states=True
        )

        # Step by step to match original exactly
        hidden_states_last = model_output.hidden_states[-1]
        mean_pooled = hidden_states_last.mean(axis=1)
        flattened = mean_pooled.flatten()

        features = flattened

        return features

    def get_input_embeddings(self):
        """
        Return the embedding layer for gradient computation
        """
        return self.feature_extractor.model.get_input_embeddings()


class TwoStageFunction(torch.autograd.Function):
    """
    Custom autograd function to handle the two-stage gradient computation
    """
    @staticmethod
    def forward(ctx, features, nn_inferencer, device):
        """
        Forward pass: features -> NN inference -> logits
        """
        # Save for backward
        ctx.features = features.detach().clone()
        ctx.nn_inferencer = nn_inferencer
        ctx.device = device

        # Convert to numpy for NN inferencer
        features_numpy = features.detach().cpu().numpy().astype(np.float32)

        # Ensure features are properly shaped for NN inferencer
        if features_numpy.ndim == 1:
            features_reshaped = features_numpy.reshape(1, -1)
        else:
            features_reshaped = features_numpy

        # Get prediction from neural network
        pred_logits, _ = nn_inferencer.predict_prob(features_reshaped)

        # Convert back to tensor
        logits_tensor = torch.tensor(pred_logits, dtype=torch.float32, device=device)

        return logits_tensor

    @staticmethod
    def backward(ctx, grad_output):
        """
        Backward pass: compute gradients w.r.t. features
        """
        # For simplicity, we'll use a finite difference approximation
        # to compute gradients of NN w.r.t. features
        features = ctx.features.requires_grad_(True)
        nn_inferencer = ctx.nn_inferencer
        device = ctx.device

        # Small perturbation for finite differences
        eps = 1e-4

        # Compute gradients using finite differences
        features_numpy = features.detach().cpu().numpy().astype(np.float32)

        # Ensure proper shape for NN inferencer
        if features_numpy.ndim == 1:
            features_reshaped = features_numpy.reshape(1, -1)
        else:
            features_reshaped = features_numpy

        # Get original output
        orig_logits, _ = nn_inferencer.predict_prob(features_reshaped)

        # Compute finite difference gradients
        grad_features = torch.zeros_like(features)

        for i in range(features.shape[0]):
            # Perturb feature i
            features_pert = features_numpy.copy()
            features_pert[i] += eps  # Direct indexing for 1D array

            # Reshape for NN inferencer
            if features_pert.ndim == 1:
                features_pert_reshaped = features_pert.reshape(1, -1)
            else:
                features_pert_reshaped = features_pert

            # Get perturbed output
            pert_logits, _ = nn_inferencer.predict_prob(features_pert_reshaped)

            # Compute gradient
            grad_logits = (pert_logits - orig_logits) / eps

            # Chain rule: grad w.r.t. feature i
            grad_features[i] = torch.sum(grad_output.cpu() * torch.tensor(grad_logits, dtype=torch.float32))

        return grad_features.to(device), None, None


class TwoStageGradientCalculator(nn.Module):
    """
    Two-stage gradient calculation using custom autograd function:
    1. Text ->  features
    2. Features -> NN inference -> MMSE scores
    """
    def __init__(self, feature_extractor, nn_inferencer):
        super(TwoStageGradientCalculator, self).__init__()
        self.feature_extractor = feature_extractor
        self.nn_inferencer = nn_inferencer
        # self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.device = 'cpu'

        # Create the first stage model
        self.text_to_features_model = TextToModel(feature_extractor)

    def forward(self, text=None, tokenizer_params=None):
        """
        Forward pass using custom autograd function for two-stage computation
        """
        # Get features from first stage - KEEP GRADIENTS
        features = self.text_to_features_model(text=text, tokenizer_params=tokenizer_params)

        # Use custom autograd function for second stage
        logits = TwoStageFunction.apply(features, self.nn_inferencer, self.device)

        return logits

    def get_input_embeddings(self):
        """
        Return the embedding layer for gradient computation
        """
        return self.text_to_features_model.get_input_embeddings()

    # def get_embeddings_for_text(self, text):
    #     """
    #     Get embeddings for a given text (for gradient computation)
    #     """
    #     # Tokenize text
    #     inputs = self.feature_extractor.tokenizer(
    #         text,
    #         return_tensors="pt",
    #         padding=True,
    #         truncation=True,
    #         max_length=512
    #     )
    #
    #     # Move to device
    #     for key, value in inputs.items():
    #         if isinstance(value, torch.Tensor):
    #             inputs[key] = value.to(self.device)
    #
    #     # Get embeddings
    #     embedding_layer = self.get_input_embeddings()
    #     embeddings = embedding_layer(inputs['input_ids'])
    #
    #     return embeddings, inputs


def compute_saliency_map(feature_extractor, nn_inferencer, subject_text, file_name="", tokenizer_params=None):
    """
    Compute saliency map for text input using two-stage gradient calculation

    Args:
        feature_extractor: Feature extractor
        nn_inferencer: Neural network inferencer
        subject_text: Input text string
        file_name: File name for logging
        tokenizer_params: Dict of tokenizer parameters (e.g., {"return_tensors": "pt", "padding": True, "truncation": True, "max_length": 512})

    Returns:
        saliency_results: Results from saliency interpretation
    """
    print('Computing saliency map ...', file_name)

    # Create two-stage gradient calculator for text-to-MMSE prediction
    two_stage_calculator = TwoStageGradientCalculator(
        feature_extractor,
        nn_inferencer
    )

    # Create criterion for classification (CrossEntropy loss)
    criterion = nn.CrossEntropyLoss()

    # Get tokenizer from  extractor
    tokenizer = feature_extractor.tokenizer

    # Initialize IntegratedGradient with two-stage calculator
    integrated_grad = IntegratedGradient(
        two_stage_calculator,
        criterion,
        tokenizer,
        show_progress=False,
        encoder="test"
    )

    # Create dataset and dataloader with tokenizer parameters
    dataset = SimpleTextDataset(subject_text)
    dataloader = DataLoader(dataset, batch_size=1, shuffle=False)

    saliency_results = integrated_grad.saliency_interpret(dataloader, tokenizer_params=tokenizer_params)

    torch.cuda.empty_cache()

    return saliency_results

import matplotlib
def colorize(*args, skip_special_tokens=False, num_words_colored=10, transparency=0.3):
    """
    Colorize saliency results based on averaged gradients from multiple models

    Args:
        *args: Variable number of tuples, each containing (saliency_results, special_tokens)
        skip_special_tokens: Whether to skip special tokens
        num_words_colored: Number of words with highest gradients to color (default: 10)
        transparency: Background transparency level (0=完全不透明, 1=完全透明, default: 0.3)

    Returns:
        colored_string: HTML string with colored tokens based on averaged gradients

    Note:
        - Only the top num_words_colored words with highest gradients will be colored
        - transparency=0: 背景色完全不透明 (alpha=1.0)
        - transparency=1: 背景色完全透明 (alpha=0.0)
        - Font style: regular weight, italic, no bold
    """
    import matplotlib
    import matplotlib.cm
    import matplotlib.colors
    import numpy as np
    import re

    if len(args) == 0:
        return "Error: No saliency results provided"

    print(f"DEBUG: num_words_colored parameter = {num_words_colored}")
    print(f"DEBUG: transparency parameter = {transparency}")

    def normalize_token(token):
        """Normalize tokens from different tokenizers to comparable form"""
        # Remove tokenizer-specific prefixes
        if token.startswith('▁'):  # XLM-RoBERTa
            return token[1:].lower()
        elif token.startswith('Ġ'):  # GPT2/RoBERTa
            return token[1:].lower()
        elif token.startswith('##'):  # BERT WordPiece
            return token[2:].lower()
        else:
            return token.lower()

    def get_word_gradients(saliency_result, special_tokens_set):
        """Extract word-level gradients from tokenized results"""
        # Handle both dict and list formats
        if isinstance(saliency_result, list):
            if len(saliency_result) == 0:
                return {}
            saliency_result = saliency_result[0]

        if not isinstance(saliency_result, dict):
            return {}

        if 'tokens' not in saliency_result or 'grad' not in saliency_result:
            return {}

        tokens = saliency_result['tokens']
        grads = saliency_result['grad']

        word_grads = {}
        current_word = ""
        current_grad_sum = 0.0
        token_count = 0

        for token, grad in zip(tokens, grads):
            # Skip special tokens if requested
            if skip_special_tokens and token in special_tokens_set:
                continue

            normalized_token = normalize_token(token)

            # Handle special tokens and empty tokens
            if token in ['<s>', '</s>', '<pad>', '<unk>'] or not normalized_token:
                # For punctuation, treat as separate word
                if token in ['.', '!', '?', ',', ':', ';', "'s"]:
                    word_grads[token] = grad
                continue

            # Check if this is a continuation of previous word (subword)
            is_continuation = (
                token.startswith('##') or  # BERT wordpiece continuation
                (not token.startswith('▁') and not token.startswith('Ġ') and
                 current_word and not re.match(r'^[.!?,:;]', normalized_token))
            )

            if is_continuation:
                # Continuation of previous word
                current_word += normalized_token
                current_grad_sum += grad
                token_count += 1
            else:
                # Save previous word if exists
                if current_word and token_count > 0:
                    word_grads[current_word] = current_grad_sum / token_count

                # Start new word
                current_word = normalized_token
                current_grad_sum = grad
                token_count = 1

        # Save last word
        if current_word and token_count > 0:
            word_grads[current_word] = current_grad_sum / token_count

        return word_grads

    try:
        # Extract word gradients from all models
        all_word_grads = []
        all_special_tokens = set()

        for i, arg in enumerate(args):
            if not isinstance(arg, tuple) or len(arg) != 2:
                continue

            saliency_result, special_tokens = arg

            # Ensure special_tokens is a list or set
            if not isinstance(special_tokens, (list, set, tuple)):
                continue

            all_special_tokens.update(special_tokens)
            # Pass the original saliency_result to get_word_gradients, let it handle the format
            word_grads = get_word_gradients(saliency_result, set(special_tokens))
            if word_grads:  # Only add if we got valid word gradients
                all_word_grads.append(word_grads)

        if not all_word_grads:
            return "Error: No valid saliency results found"

        # Get all unique words
        all_words = set()
        for word_grads in all_word_grads:
            all_words.update(word_grads.keys())

        # Calculate averaged gradients
        averaged_grads = {}
        for word in all_words:
            grads_for_word = []
            for word_grads in all_word_grads:
                if word in word_grads:
                    grads_for_word.append(word_grads[word])

            if grads_for_word:
                averaged_grads[word] = np.mean(grads_for_word)

        if not averaged_grads:
            return "Error: No gradients to average"

        print(f"Averaged gradients for {len(averaged_grads)} words:")
        for word, grad in list(averaged_grads.items())[:5]:  # Show first 5
            print(f"  {word}: {grad}")

        # Normalize gradients for color mapping
        grad_values = list(averaged_grads.values())
        grad_min = np.min(grad_values)
        grad_max = np.max(grad_values)

        print(f"Gradient range: {grad_min} to {grad_max}")

        normalized_grads = {}
        if grad_max > grad_min:
            for word in averaged_grads:
                normalized_grads[word] = (averaged_grads[word] - grad_min) / (grad_max - grad_min)
        else:
            for word in averaged_grads:
                normalized_grads[word] = 0.5  # Use middle value if all gradients are the same

        # Create colored string
        word_cmap = matplotlib.cm.Blues
        # Use regular font weight with darker text color and transparency support
        template = '<span class="barcode" style="color: #000000; background-color: rgba({},{},{},{}); font-style: italic;">{}</span>'
        # Template for uncolored words with regular font weight
        uncolored_template = '<span style="color: #000000; font-style: italic;">{}</span>'
        colored_string = ''

        # Find top N words with highest gradients
        # Make sure we don't try to color more words than we have
        actual_num_words = min(num_words_colored, len(averaged_grads))

        # Sort words by gradient value (highest first)
        sorted_words = sorted(averaged_grads.items(), key=lambda x: x[1], reverse=True)
        # Take only the top N words
        top_words = set([word for word, _ in sorted_words[:actual_num_words]])

        print(f"🎨 COLORIZE: Will color top {actual_num_words} words (requested: {num_words_colored}, available: {len(averaged_grads)})")
        print(f"Top {actual_num_words} words with highest gradients (out of {len(averaged_grads)} total):")
        for word, grad in sorted_words[:actual_num_words]:
            print(f"  ✨ {word}: {grad:.4f}")

        # Sort words by their appearance order (use first model's token order as reference)
        first_result = args[0][0]

        # Handle list format for first_result
        if isinstance(first_result, list):
            if len(first_result) == 0:
                return "Error: Empty first result"
            first_result = first_result[0]

        if not isinstance(first_result, dict) or 'tokens' not in first_result:
            return "Error: Invalid first result format"

        word_order = []
        current_word = ""

        for token in first_result['tokens']:
            if token in all_special_tokens and skip_special_tokens:
                continue

            normalized_token = normalize_token(token)
            if not normalized_token or token in ['<s>', '</s>', '<pad>', '<unk>']:
                continue

            if (token.startswith('##') or
                (not token.startswith('▁') and not token.startswith('Ġ') and
                 current_word and not re.match(r'^[.!?,:;]', token))):
                current_word += normalized_token
            else:
                if current_word and current_word in averaged_grads:
                    word_order.append(current_word)
                current_word = normalized_token

        if current_word and current_word in averaged_grads:
            word_order.append(current_word)

        # Generate colored HTML
        is_sentence_start = True
        colored_count = 0
        total_words = 0

        for word in word_order:
            if word in normalized_grads:
                total_words += 1
                grad_value = normalized_grads[word]
                # Format word with proper capitalization first
                if word in ['.', '!', '?', ',', ':', ';', "'s"]:
                    display_word = word
                    # Mark next word as sentence start if it's a sentence-ending punctuation
                    if word in ['.', '!', '?']:
                        is_sentence_start = True
                else:
                    # Apply English capitalization rules
                    if is_sentence_start:
                        # Capitalize first letter if it's the start of a sentence
                        display_word = ' ' + word.capitalize()
                        is_sentence_start = False
                    elif word.lower() == 'i':
                        # Always capitalize the pronoun "I"
                        display_word = ' I'
                    else:
                        display_word = ' ' + word

                # Only color top N words with highest gradients
                if word in top_words:
                    colored_count += 1
                    # Create color mapper
                    color_mapper = matplotlib.cm.ScalarMappable(cmap=word_cmap, norm=matplotlib.colors.Normalize(vmin=0, vmax=1))
                    color = color_mapper.to_rgba(grad_value)

                    # Convert RGB to individual components for rgba
                    r = int(color[0] * 255)
                    g = int(color[1] * 255)
                    b = int(color[2] * 255)
                    # transparency: 0=不透明, 1=完全透明
                    # alpha: 1=不透明, 0=完全透明
                    alpha = 1.0 - transparency  # 转换透明度到alpha值

                    # Add colored background with transparency
                    colored_string += template.format(r, g, b, alpha, display_word)
                    print(f"COLORED Word: {word}, Grad: {grad_value:.4f}, Color: rgba({r},{g},{b},{alpha:.2f}), Transparency: {transparency}")
                else:
                    # No background color for other words, but use darker text with italic
                    colored_string += uncolored_template.format(display_word)
                    print(f"UNCOLORED Word: {word}, Grad: {grad_value:.4f}")

        print(f"📊 COLORIZE SUMMARY:")
        print(f"   Generated colored string length: {len(colored_string)}")
        print(f"   Total words processed: {total_words}")
        print(f"   Words actually colored: {colored_count}/{actual_num_words}")
        print(f"   Transparency setting: {transparency} (0=不透明, 1=完全透明)")
        print(f"   Background alpha value: {1.0 - transparency:.2f} (1=不透明, 0=透明)")
        print(f"   Font style: regular weight, italic")
        print(f"   🎨 Only the top {actual_num_words} words with highest gradients are colored!")
        print(f"   First 200 chars: {colored_string[:200]}")

        return colored_string

    except Exception as e:
        return f"Error in colorization: {str(e)}"

def compute_mean_sailency_maps(feats_extractor, inferencer, subject_text, file_name):

    # xlm_roberta_base
    tokenizer_params1 = {
        "return_tensors": "pt",
        "padding": True,
        "truncation": True,
        "max_length": 512
    }

    saliency_results1 = compute_saliency_map(
        feats_extractor.xlm_roberta_base_extractor,
        inferencer.NN_inferencers[1],
        subject_text,
        file_name=file_name,
        tokenizer_params=tokenizer_params1
    )

    # # gpt2_xl
    # tokenizer_params2 = {
    #     "return_tensors": "pt",
    #     "max_length": 512
    # }
    #
    # saliency_results2 = compute_saliency_map(
    #     feats_extractor.gpt2_xl_feats_extractor,
    #     inferencer.NN_inferencers[2],
    #     subject_text,
    #     file_name=file_name,
    #     tokenizer_params=tokenizer_params2
    # )

    # # roberta_base_features
    # tokenizer_params3 = {
    #     "return_tensors": "pt",
    #     "padding": True,
    #     "truncation": True,
    #     "max_length": 512
    # }
    #
    # saliency_results3 = compute_saliency_map(
    #     feats_extractor.roberta_base_feats_extractor,
    #     inferencer.NN_inferencers[3],
    #     subject_text,
    #     file_name=file_name,
    #     tokenizer_params=tokenizer_params3
    # )

    colored_string = colorize(
        (saliency_results1, feats_extractor.xlm_roberta_base_extractor.tokenizer.all_special_tokens),
        # (saliency_results2, feats_extractor.gpt2_xl_feats_extractor.tokenizer.all_special_tokens),
        # (saliency_results3, feats_extractor.roberta_base_feats_extractor.tokenizer.all_special_tokens),
        skip_special_tokens=True,
        num_words_colored=6,
        transparency=0.5,
    )

    return colored_string