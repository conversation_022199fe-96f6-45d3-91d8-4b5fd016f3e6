import whisperx
import pickle
import torch

import os
from core.settings import BASE_DIR

class forced_aligment_model():

    def __init__(self):

        self.device = "cuda"
        self.compute_type = "float16"
        self.batch_size = 4

        asr_model_path = os.path.join(BASE_DIR, 'mmse_prediction', 'transformers/huggingface_models/Systran-faster-whisper-large-v3')
        self.asr_model = whisperx.load_model(asr_model_path, self.device, compute_type=self.compute_type, local_files_only=True, language='en')

        eng_aligment_model_path = os.path.join(BASE_DIR, 'mmse_prediction', 'transformers/huggingface_models/english-aligment-model')
        self.en_model_a, self.en_metadata = whisperx.load_align_model(language_code='en', device=self.device, model_dir=eng_aligment_model_path)

    def align(self, audio_file, out_path):

        audio = whisperx.load_audio(audio_file)
        result = self.asr_model.transcribe(audio, batch_size=self.batch_size, language='en')

        result = whisperx.align(result["segments"], self.en_model_a, self.en_metadata, audio, self.device, return_char_alignments=True)

        with open(out_path, 'wb') as f:
            pickle.dump(result, f)

        torch.cuda.empty_cache()