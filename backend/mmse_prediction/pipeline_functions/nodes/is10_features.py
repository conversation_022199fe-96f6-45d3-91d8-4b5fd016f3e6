import os
import pandas as pd
import numpy as np
import subprocess

import os
from core.settings import BASE_DIR

def shell_run(cmd, stdout=None, stdin=None, stderr=None):
    if stderr is None:
        stderr = open(os.devnull, "w")
    elif isinstance(stderr, str):
        stderr = open(stderr, "w")

    if stdout is None:
        stdout = open(os.devnull, "w")
    elif isinstance(stdout, str):
        stdout = open(stdout, "w")

    res = subprocess.call(cmd, stdout=stdout, stderr=stderr, stdin=stdin)

    stderr.close()
    stdout.close()

    return res

def locate_file(file_name, possible_paths=[], use_path=False):
    # Is file_name an absolute path?
    if os.path.isfile(file_name):
        return file_name

    if use_path:
        possible_paths += os.environ["PATH"].split(":")

    for possible_path in possible_paths:
        p = os.path.join(possible_path, file_name)
        if os.path.isfile(p):
            return p

    cwd = os.getcwd()
    raise ValueError("File %s does not exist in any of [%s]" % (file_name, ", ".join([cwd] + possible_paths)))

class is10_OpenSmileRunner():

    def __init__(self, out_flag="-csvoutput", extra_flags="-nologfile -noconsoleoutput -appendcsv 0", out_ext="csv"):

        OPENSMILE_HOME = os.path.join(BASE_DIR, 'mmse_prediction', 'opensmile-3.0.2-linux-x86_64')
        self.conf_file = locate_file("is09-13/IS10_paraling.conf", [os.path.join(OPENSMILE_HOME, "config")])
        self.extra_flags = extra_flags.split(" ")
        self.out_flag = out_flag
        self.out_ext = out_ext

        opensmile_locations = [
            OPENSMILE_HOME,
            os.path.join(OPENSMILE_HOME, "bin"),
            os.path.join(OPENSMILE_HOME, "bin/linux_x64_standalone_static"),
        ]
        self.opensmile_exec = locate_file("SMILExtract", opensmile_locations, use_path=True)

    def run(self, in_file):

        out_file = os.path.join(os.path.join(BASE_DIR, 'mmse_prediction', 'processed/features_temp/'), os.path.splitext(os.path.basename(in_file))[0] + '.' + self.out_ext)

        cmd = [self.opensmile_exec, "-C", self.conf_file, "-I", in_file, self.out_flag, out_file] + self.extra_flags
        shell_run(cmd)

        feats = pd.read_csv(out_file, sep=';').values.flatten()[2:].astype(np.float32)

        os.remove(out_file)

        return feats