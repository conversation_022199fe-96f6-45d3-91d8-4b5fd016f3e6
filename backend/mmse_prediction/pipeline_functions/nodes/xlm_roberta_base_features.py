import pandas as pd
import torch
import numpy as np
from transformers import AutoTokenizer, AutoModelForMaskedLM

import os
from core.settings import BASE_DIR

class feature_extractor():

    def __init__(self,):

        self.model_pt = os.path.join(BASE_DIR, 'mmse_prediction', "transformers/huggingface_models/FacebookAI-xlm-roberta-base")
        self.tokenizer = AutoTokenizer.from_pretrained(self.model_pt, local_files_only=True)
        self.model = AutoModelForMaskedLM.from_pretrained(self.model_pt, local_files_only=True)

    def run(self, in_file):

        sentences = ''.join(pd.read_csv(in_file)['text'])
        inputs = self.tokenizer(sentences, return_tensors="pt", padding=True, truncation=True, max_length=512)
        with torch.no_grad():
            model_output = self.model(**inputs, output_hidden_states=True)
        feats = model_output.hidden_states[-1].mean(axis=1).flatten().detach().cpu().numpy().astype(np.float32)

        return feats