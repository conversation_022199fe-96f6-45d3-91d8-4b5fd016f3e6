from mmse_prediction.DigiPsych_Prosody.prosody import Voice_Prosody
import numpy as np

digipsych_prosody_feats_field = ['Speech_Time_VADInt_1', 'Total_Time_VADInt_1', 'Pause_Time_VADInt_1', 'Pause_Percentage_VADInt_1',
        'Pause_Speech_Ratio_VADInt_1', 'Mean_Pause_Length_VADInt_1', 'Pause_Variability_VADInt_1', 'Speech_Time_VADInt_2',
        'Total_Time_VADInt_2', 'Pause_Time_VADInt_2', 'Pause_Percentage_VADInt_2', 'Pause_Speech_Ratio_VADInt_2',
        'Mean_Pause_Length_VADInt_2', 'Pause_Variability_VADInt_2', 'Speech_Time_VADInt_3', 'Total_Time_VADInt_3',
        'Pause_Time_VADInt_3', 'Pause_Percentage_VADInt_3', 'Pause_Speech_Ratio_VADInt_3', 'Mean_Pause_Length_VADInt_3',
        'Pause_Variability_VADInt_3']

class feature_extractor():

    def __init__(self, ):

        self.vp = Voice_Prosody()

    def run(self, in_file):

        feat_dict = self.vp.featurize_audio(in_file, int(20))

        values = []
        for key in digipsych_prosody_feats_field:
            values.append(feat_dict[key])

        feats = np.array(values).astype(np.float32)

        return feats