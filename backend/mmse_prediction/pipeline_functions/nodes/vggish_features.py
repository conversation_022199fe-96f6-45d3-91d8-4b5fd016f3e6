import torch
import numpy as np
from torch.hub import _load_local

import os
from core.settings import BASE_DIR

class feature_extractor():

    def __init__(self, ):

        ## network connection well
        # self.model = torch.hub.load('harritaylor/torchvggish', 'vggish')
        # self.model.eval()

        ## network connection fail, load local model, also modify some codes in 'torch/hub/harritaylor_torchvggish_master/hubconf.py'
        self.model = _load_local(os.path.join(BASE_DIR, 'mmse_prediction', 'torch_hub/harritaylor_torchvggish_master'), 'vggish')
        self.model.eval()

    def run(self, in_file):

        outputs = self.model.forward(in_file).detach().cpu().numpy()
        feats = outputs.mean(axis=0).astype(np.float32)

        return feats