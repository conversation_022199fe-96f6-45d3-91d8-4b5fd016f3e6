from transformers import AutoFeatureExtractor
import librosa
from transformers import WhisperModel
import torch
import numpy as np
import os

from core.settings import BASE_DIR

class feature_extractor():

    def __init__(self):

        self.model_pt = os.path.join(BASE_DIR, 'mmse_prediction', "transformers/huggingface_models/openai-whisper-large")
        self.emb_en = WhisperModel.from_pretrained(self.model_pt, local_files_only=True)
        self.audio_processor_en = AutoFeatureExtractor.from_pretrained(self.model_pt, local_files_only=True)
        self.decoder_input_ids = torch.tensor([[1, 1]]) * self.emb_en.config.decoder_start_token_id
        self.max_len = 4019649

    def read_audio(self, path):

        audio, sr = librosa.load(path)
        audio = librosa.resample(audio, orig_sr=sr, target_sr=16000)

        return audio, 16000

    def run(self, in_file):

        audio, _ = self.read_audio(in_file)

        #### pading
        if len(audio) < self.max_len:
            pad_num = self.max_len - len(audio)
            audio = np.hstack((audio, np.zeros(pad_num)))
        elif len(audio) >= self.max_len:
            audio = audio[:self.max_len]

        audio_tensor = self.audio_processor_en(audio, sampling_rate=16000,
                                          return_tensors="pt",
                                          truncation=True,
                                          padding='longest').input_features[0]

        outputs_en = self.emb_en(audio_tensor.unsqueeze(0), decoder_input_ids=self.decoder_input_ids.to(self.emb_en.device)).last_hidden_state
        outputs = torch.mean(outputs_en, dim=1)

        feats = outputs.detach().numpy().flatten().astype(np.float32)

        return feats

