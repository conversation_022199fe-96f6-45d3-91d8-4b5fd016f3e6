import os
import pickle
from torch import nn
import torch
from sklearn.preprocessing import StandardScaler
import numpy as np
from mmse_prediction.utilize import inv_bin_interval

class NN_classifier(nn.Module):

    def __init__(self, input_shape, dense_arch, num_labels):

        super(NN_classifier, self).__init__()

        self.num_labels = num_labels

        # cls
        self.model = nn.ModuleList([nn.Linear(input_shape, dense_arch[0])])
        self.model.append(nn.ReLU())
        self.model.append(nn.BatchNorm1d(dense_arch[0]))
        self.model.append(nn.Dropout(0.5))

        for i in range(len(dense_arch) - 2):
            self.model.append(nn.Linear(dense_arch[i], dense_arch[i + 1]))
            self.model.append(nn.ReLU())
            self.model.append(nn.BatchNorm1d(dense_arch[i + 1]))
            self.model.append(nn.Dropout(0.5))

        self.cls1 = nn.Linear(dense_arch[-1], 10)
        self.cls_activate1 = nn.Softmax(dim=1)

        self.cls2 = nn.Linear(dense_arch[-1], 1)


    def forward(self, x=None):

        for layer in self.model:
            x = layer(x)
        self.last_state = x

        logits_cls = self.cls_activate1(self.cls1(self.last_state))

        logits_reg = torch.clip(self.cls2(self.last_state), 0, 30).view(-1)

        return logits_cls, logits_reg

class NN_inference():

    def __init__(self, feats_shape, nn_arch, num_labels, state_dict_path, all_trn_feats):

        self.model = NN_classifier(feats_shape, nn_arch, num_labels)
        self.model.to("cuda")
        self.model.load_state_dict(torch.load(state_dict_path))
        self.model.eval()

        self.std = StandardScaler().fit(all_trn_feats)

    def predict_prob(self, j_feat):

        j_feat = self.std.transform(j_feat)

        with torch.no_grad():
            outputs_cls, outputs_reg = self.model(torch.tensor(j_feat).cuda())
        pred_logits = outputs_cls.detach().cpu().numpy()
        pred_scores = outputs_reg.detach().cpu().numpy()

        return pred_logits, pred_scores

class main_inference():

    def __init__(self, training_data, feat_names, state_dicts, feat_shapes, nn_arch, num_labels, selected_indexs,
                 DDR_state_dicts, num_fs):

        self.feat_names = feat_names
        ## NN classification
        self.NN_inferencers = [
            NN_inference(feat_shapes[i], nn_arch, num_labels, state_dicts[i], training_data[feat_names[i]]) for i in
            range(len(feat_names))]

        ## DDR
        self.selected_indexs = selected_indexs
        self.num_fs = num_fs
        concated_trn_feats = np.concatenate(([training_data[feat_name] for feat_name in feat_names]), axis=1)
        self.DDR_inferencers = []
        for selected_index, DDR_state_dict in zip(selected_indexs, DDR_state_dicts):
            self.DDR_inferencers.append(NN_inference(num_fs, nn_arch, num_labels, DDR_state_dict, concated_trn_feats[:, selected_index[:num_fs]]))

    def inference(self, per_subject_feats):

        ## NN classification
        per_subject_logits = []
        per_subject_scores = []
        for feat, NN_inferencer in zip(per_subject_feats, self.NN_inferencers):
            pred_logits, pred_scores = NN_inferencer.predict_prob(feat)
            per_subject_logits.append(pred_logits.flatten())
            per_subject_scores.append(pred_scores[0])

        ## DDR
        DDR_pred_logits = []
        DDR_pred_scores = []
        for selected_index, DDR_inferencer in zip(self.selected_indexs, self.DDR_inferencers):
            selected_feats = np.concatenate((per_subject_feats), axis=1)[:, selected_index[:self.num_fs]]
            pred_logits, pred_scores = DDR_inferencer.predict_prob(selected_feats)
            DDR_pred_logits.append(pred_logits.flatten())
            DDR_pred_scores.append(pred_scores[0])

        # score 1
        DDR_pred_labels = np.array(DDR_pred_logits).argmax(axis=1)
        DDR_pred_scores1 = inv_bin_interval(DDR_pred_labels, 10)
        DDR_pred_scores1_mean = np.mean(DDR_pred_scores1)

        soft_vote_label = np.argmax(np.array(per_subject_logits), axis=1)
        pred_scores1 = inv_bin_interval(soft_vote_label, 10)
        all_pred_scores1 = np.insert(pred_scores1, -1, DDR_pred_scores1_mean)
        mean_pred_scores1 = np.mean(all_pred_scores1)

        # score 2
        per_subject_scores.append(np.mean(DDR_pred_scores))
        mean_pred_scores2 = np.mean(per_subject_scores)

        return mean_pred_scores1, mean_pred_scores2

def load_selected_index(model_dir, fold, random_state):

    save_pts = [os.path.join(model_dir, 'fold' + str(fold) + '_' + 'random_state' + str(tmp_state) + '.pkl') for tmp_state in [random_state] + [2345, 3456, 5678, 6789]]
    selected_indexs = []
    for save_pt in save_pts:
        with open(save_pt, 'rb') as f:
            tmp_dict = pickle.load(f)
            selected_indexs.append(tmp_dict['selected_index'])

    return selected_indexs

def load_inferencer(model_dir, feat_names, training_data, nn_arch, num_fs, random_state, fold):

    ## NN classification
    state_dicts = [os.path.join(model_dir, 'random_state_' + str(random_state) + '_fold_' + str(fold) + '_' + str(feat_name) + '.pt') for feat_name in feat_names]
    feat_shapes = [training_data[feat_name].shape[1] for feat_name in feat_names]

    ## DDR feature ranking
    selected_indexs = load_selected_index(model_dir, fold, random_state)

    DDR_state_dicts = [os.path.join(model_dir, 'random_state_' + str(tmp_state) + '_fold_' + str(fold) + '_feats' + '.pt') for tmp_state in [random_state] + [2345, 3456, 5678, 6789]]

    inferencer = main_inference(training_data, feat_names, state_dicts, feat_shapes, nn_arch, 9, selected_indexs, DDR_state_dicts, num_fs)

    return inferencer