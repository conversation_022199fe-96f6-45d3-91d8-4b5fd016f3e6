import os
import json
import numpy as np
import pandas as pd
from scipy import stats
from sklearn.metrics import roc_auc_score, roc_curve
import torch
import torch.nn as nn

from mmse_prediction.pipeline_functions.transcriber_functions import transcribe_model
from mmse_prediction.pipeline_functions.forced_aligment_functions import forced_aligment_model
from mmse_prediction.pipeline_functions.feature_extraction_functions import feature_extractor
from mmse_prediction.pipeline_functions.other_functions import ffmpeg_normalize, load_selection_fre, load_feats_name, load_forced_aligment_trans, clsi_reference_interval_complete, build_prediction_result_dict
from mmse_prediction.pipeline_functions.inferencer_functions import load_inferencer
from mmse_prediction.pipeline_functions.trn_dict_loader import load_trn_datadict

from mmse_prediction.pipeline_functions.compute_gradients_functions import compute_mean_sailency_maps

import os
from core.settings import BASE_DIR


class mmse_predict_pipeline():

    def __init__(self, ):

        ### temp dir
        self.ffmpeg_normalize_dir = os.path.join(BASE_DIR, 'mmse_prediction', 'processed/ffmpeg_normalize')
        self.transcribed_dir = os.path.join(BASE_DIR, 'mmse_prediction', 'processed/transcribed')
        self.forced_aligment_dir = os.path.join(BASE_DIR, 'mmse_prediction', 'processed/forced_aligment')

        ## transcriber
        self.transcriber = transcribe_model()

        ## forced aligment
        self.forced_aligner = forced_aligment_model()

        ## feature extractor
        self.feats_extractor = feature_extractor()

        ## inferencer
        model_dir = os.path.join(BASE_DIR, 'mmse_prediction', 'results/ensemble_seed1234')
        self.feat_names = ['whisper_en', 'xlm_roberta_base', 'gpt2_xl_feats', 'roberta_base_feats',
                           'is10', 'digipsych_prosody_feats', 'lexicosyntactic', 'vggish_feats']        # fixed
        training_data = load_trn_datadict()

        config_path = os.path.join(model_dir, 'best_config.json')
        with open(config_path, 'r') as f:
            config = json.load(f)

        self.inferencer = load_inferencer(model_dir, self.feat_names, training_data, config['nn_arch'], config['num_fs'], 1234,10)

        ## selected features
        self.selected_index, self.selected_fre = load_selection_fre(model_dir)

        ## feature_names
        shift = [1, 1, 1, 1, 2, 1, 0, 1]         # fixed
        self.features_name, self.features_type = load_feats_name(BASE_DIR, self.feat_names, shift)

        ## calculate clsi reference using training normal data
        training_feats_concated = np.concatenate([training_data[feat_name] for feat_name in self.feat_names], axis=1)
        control_index = np.where(training_data['labels'] == 0)[0]
        training_feats_control = training_feats_concated[control_index, :]

        self.feats_clsi_lower = []
        self.feats_clsi_upper = []
        for j in range(training_feats_control.shape[1]):
            lower, upper = clsi_reference_interval_complete(training_feats_control[:, j])
            self.feats_clsi_lower.append(lower)
            self.feats_clsi_upper.append(upper)

        ## calculate effect size for each feature
        dementia_index = np.concatenate([np.where(training_data['labels'] == 1)[0], np.where(training_data['labels'] == 2)[0]])
        training_feats_dementia = training_feats_concated[dementia_index, :]

        self.feats_effect_size = []
        for j in range(training_feats_control.shape[1]):

            normal_data = training_feats_control[:, j]
            abnormal_data = training_feats_dementia[:, j]

            mean_normal = np.mean(normal_data)
            mean_abnormal = np.mean(abnormal_data)
            std_normal = np.std(normal_data, ddof=1)
            std_abnormal = np.std(abnormal_data, ddof=1)

            # Pooled standard deviation
            n1, n2 = len(normal_data), len(abnormal_data)
            s_pooled = np.sqrt(((n1 - 1) * std_normal ** 2 + (n2 - 1) * std_abnormal ** 2) / (n1 + n2 - 2))

            cohens_d = (mean_abnormal - mean_normal) / s_pooled if s_pooled != 0 else 0

            self.feats_effect_size.append(cohens_d)

        ## for calculate percentile
        self.training_inv_mmses = training_data['inv_mmses']

    def predict(self, audio_file):

        file_name = os.path.basename(audio_file)

        # ffmpeg normalize
        print('Normalize ...', file_name)
        normalized_audio_path = os.path.join(self.ffmpeg_normalize_dir, os.path.basename(audio_file).split('.')[0] + '.wav')
        ffmpeg_normalize(audio_file, normalized_audio_path)

        # transcribe
        print('Transcribe ...', file_name)
        transcribed_path = os.path.join(self.transcribed_dir, os.path.basename(audio_file).split('.')[0] + '.csv')
        subject_text = self.transcriber.transcribe(normalized_audio_path, transcribed_path)

        # forced aligment
        print('Forced align ...', file_name)
        forced_aligment_path = os.path.join(self.forced_aligment_dir, os.path.basename(audio_file).split('.')[0] + '.pkl')
        self.forced_aligner.align(normalized_audio_path, forced_aligment_path)

        # extract features
        print('Extract features ...', file_name)
        feats_dict = self.feats_extractor.extract(normalized_audio_path, transcribed_path, forced_aligment_path)

        # inference features
        print('Inference ...', file_name)
        subject_feats = [feats_dict[feat_name].reshape(1, -1) for feat_name in self.feat_names]  # per_subject_feat
        scores1, _ = self.inferencer.inference(subject_feats)

        trans = load_forced_aligment_trans(forced_aligment_path)

        ### compute saliency map
        trans_color = compute_mean_sailency_maps(self.feats_extractor, self.inferencer, trans, file_name)

        # build result dictionary
        dict_ = build_prediction_result_dict(
            scores1, trans, subject_feats, self.selected_fre, self.selected_index,
            self.features_type, self.features_name, self.feats_clsi_lower, self.feats_clsi_upper,
            self.feats_effect_size, self.training_inv_mmses, trans_color
        )

        return dict_