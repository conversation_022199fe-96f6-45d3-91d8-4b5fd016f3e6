import numpy as np

def bin_interval(value, num_bin):

    bin_edge = np.linspace(0.5, 30.5, num_bin+1)

    bined_values = []
    for per_value in value:
        bined_values.append(np.where(per_value > bin_edge)[0][-1])

    bined_values = np.array(bined_values).astype(np.int64)

    return bined_values

def inv_bin_interval(bined_values, num_bin):

    bin_edge = np.linspace(0.5, 30.5, num_bin+1)

    center_value = ((bin_edge + np.roll(bin_edge, 1))/2)[1:]

    inv_bin_values = []
    for value in bined_values:
        inv_bin_values.append(center_value[value])
    inv_bin_values = np.array(inv_bin_values)

    return inv_bin_values