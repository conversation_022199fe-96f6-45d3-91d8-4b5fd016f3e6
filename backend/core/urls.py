"""
URL configuration for core project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/4.2/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""

from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static
# Temporarily commented out to fix coreapi issue
# from rest_framework.authtoken import views

# from rest_framework.schemas import get_schema_view
# schema_view = get_schema_view(title='API', description='xxx')

# Temporarily commented out to fix coreapi issue
# from rest_framework.documentation import include_docs_urls

urlpatterns = [

    # Temporarily commented out to fix coreapi issue
    # path('api-token-auth/', views.obtain_auth_token),
    # path('api-auth/', include('rest_framework.urls')),  # Login and logout for DRF
    path("admin/", admin.site.urls),

    # path('course/', include('course.urls')),

    # Temporarily commented out to fix coreapi issue
    # path('docs/', include_docs_urls(title='API', description='xxx')),  # docs

    path('api/', include('api.urls')),

]

# 添加媒体文件的URL配置
if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)