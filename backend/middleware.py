import geoip2.database
from django.utils import timezone
import pytz
import os
from core.settings import BASE_DIR

GEOIP_DB_PATH = os.path.join(BASE_DIR, 'geoip/GeoLite2-City.mmdb')  # Change to your actual path

def get_client_ip(request):
    x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
    if x_forwarded_for:
        ip = x_forwarded_for.split(',')[0]
    else:
        ip = request.META.get('REMOTE_ADDR')
    return ip

def get_timezone_by_ip(ip):
    try:
        reader = geoip2.database.Reader(GEOIP_DB_PATH)
        response = reader.city(ip)
        timezone_name = response.location.time_zone
        reader.close()
        return timezone_name
    except Exception:
        return None

class AutoTimezoneMiddleware:
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        try:
            ip = get_client_ip(request)
            tzname = get_timezone_by_ip(ip)
            if tzname:
                try:
                    timezone.activate(pytz.timezone(tzname))
                except Exception:
                    timezone.deactivate()
            else:
                timezone.deactivate()
        except Exception as e:
            # Prevent WSGI loading failure caused by middleware exceptions
            timezone.deactivate()
        response = self.get_response(request)
        return response

