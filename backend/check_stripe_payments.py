#!/usr/bin/env python3
"""
Check Stripe payments script

This script helps you verify payments in your Stripe account.
"""

import os
import sys
import django

# Setup Django
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
django.setup()

import stripe
from api.models import Donation
from stripe_config import STRIPE_SECRET_KEY

def check_stripe_payments():
    """Check recent payments in Stripe"""
    print("=" * 60)
    print("Stripe Payments Checker")
    print("=" * 60)
    
    # Set Stripe API key
    stripe.api_key = STRIPE_SECRET_KEY
    print(f"Using Stripe key: {STRIPE_SECRET_KEY[:12]}...")
    
    try:
        # Get recent PaymentIntents
        print("\n📋 Recent PaymentIntents from Stripe:")
        payment_intents = stripe.PaymentIntent.list(limit=10)
        
        if not payment_intents.data:
            print("❌ No PaymentIntents found in Stripe")
        else:
            for pi in payment_intents.data:
                print(f"  💳 {pi.id}")
                print(f"     Status: {pi.status}")
                print(f"     Amount: ${pi.amount / 100:.2f}")
                print(f"     Created: {pi.created}")
                if pi.metadata:
                    print(f"     Metadata: {pi.metadata}")
                print()
        
        # Get recent Charges
        print("\n💰 Recent Charges from Stripe:")
        charges = stripe.Charge.list(limit=10)
        
        if not charges.data:
            print("❌ No Charges found in Stripe")
        else:
            for charge in charges.data:
                print(f"  💵 {charge.id}")
                print(f"     Status: {charge.status}")
                print(f"     Amount: ${charge.amount / 100:.2f}")
                print(f"     Paid: {charge.paid}")
                print(f"     Payment Intent: {charge.payment_intent}")
                print()
        
        # Check local donations
        print("\n🗄️  Recent Donations from Database:")
        donations = Donation.objects.all().order_by('-created_at')[:10]
        
        if not donations:
            print("❌ No donations found in database")
        else:
            for donation in donations:
                print(f"  📝 {donation.id}")
                print(f"     Status: {donation.status}")
                print(f"     Amount: ${donation.amount}")
                print(f"     User: {donation.user.email}")
                print(f"     Stripe PI: {donation.stripe_payment_intent_id}")
                print(f"     Stripe Charge: {donation.stripe_charge_id}")
                print(f"     Created: {donation.created_at}")
                print()
        
        print("=" * 60)
        print("✅ Check completed!")
        print("If you see PaymentIntents/Charges in Stripe but no money,")
        print("make sure you're using test cards like 4242 4242 4242 4242")
        print("and check your Stripe Dashboard at:")
        print("https://dashboard.stripe.com/test/payments")
        
    except Exception as e:
        print(f"❌ Error checking Stripe: {e}")
        print("Make sure your Stripe API key is correct in .env file")

if __name__ == "__main__":
    check_stripe_payments()
