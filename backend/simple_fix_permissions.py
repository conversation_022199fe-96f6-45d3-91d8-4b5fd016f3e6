#!/usr/bin/env python3
"""
Simple Admin Permissions Fix Script
Run this in the backend directory with: python manage.py shell < simple_fix_permissions.py
"""

print("=" * 60)
print("Simple Admin Permissions Fix")
print("=" * 60)

try:
    from django.contrib.auth import get_user_model
    from django.contrib.auth.models import Group
    from api.models import Inquiry
    
    User = get_user_model()
    
    # Step 1: Create Data Analysts group
    print("\n1. CREATING DATA ANALYSTS GROUP")
    print("-" * 30)
    
    group, created = Group.objects.get_or_create(name='Data Analysts')
    if created:
        print("✅ Created 'Data Analysts' group")
    else:
        print("✅ 'Data Analysts' group already exists")
    
    # Step 2: Find and fix admin users
    print("\n2. FIXING ADMIN USERS")
    print("-" * 30)
    
    # Get all staff users
    staff_users = User.objects.filter(is_staff=True)
    print(f"Found {staff_users.count()} staff users")
    
    # Get all superusers
    superusers = User.objects.filter(is_superuser=True)
    print(f"Found {superusers.count()} superusers")
    
    # Combine and fix permissions
    all_admin_users = list(staff_users) + list(superusers)
    unique_admins = list(set(all_admin_users))  # Remove duplicates
    
    if unique_admins:
        for user in unique_admins:
            print(f"\nFixing user: {user.email}")
            
            # Ensure is_staff is True
            if not user.is_staff:
                user.is_staff = True
                user.save()
                print(f"  ✅ Set is_staff=True")
            else:
                print(f"  ✅ is_staff already True")
            
            # Add to Data Analysts group
            if not user.groups.filter(name='Data Analysts').exists():
                user.groups.add(group)
                print(f"  ✅ Added to Data Analysts group")
            else:
                print(f"  ✅ Already in Data Analysts group")
            
            # Test permission
            has_permission = user.is_staff or user.groups.filter(name='Data Analysts').exists()
            print(f"  ✅ Message access: {has_permission}")
    else:
        print("❌ No admin users found!")
        print("Create an admin user first:")
        print("  python manage.py createsuperuser")
    
    # Step 3: Create test data if needed
    print("\n3. CHECKING TEST DATA")
    print("-" * 30)
    
    inquiry_count = Inquiry.objects.count()
    print(f"Total inquiries: {inquiry_count}")
    
    if inquiry_count == 0:
        print("Creating test inquiry...")
        try:
            test_inquiry = Inquiry.objects.create(
                name="Test Admin User",
                email="<EMAIL>",
                inquiry_type=0,
                description="Test message for admin to see in message management."
            )
            print(f"✅ Created test inquiry: {test_inquiry.id}")
        except Exception as e:
            print(f"❌ Failed to create test inquiry: {e}")
    
    # Step 4: Show current status
    print("\n4. CURRENT STATUS")
    print("-" * 30)
    
    # Count users with message access
    users_with_access = 0
    for user in User.objects.all():
        if user.is_staff or user.groups.filter(name='Data Analysts').exists():
            users_with_access += 1
    
    print(f"✅ Users with message access: {users_with_access}")
    print(f"✅ Total inquiries: {Inquiry.objects.count()}")
    print(f"✅ Data Analysts group exists: {Group.objects.filter(name='Data Analysts').exists()}")
    
    print("\n" + "=" * 60)
    print("Fix completed! Next steps:")
    print("1. Log out and log back in to refresh your token")
    print("2. Visit: http://localhost:8000/contact-us/debug/")
    print("3. Click 'Test Messages API'")
    print("=" * 60)
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
