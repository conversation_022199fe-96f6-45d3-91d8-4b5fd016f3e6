from rest_framework import status, serializers
from rest_framework.response import Response
from rest_framework.parsers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Form<PERSON>arser
from rest_framework.permissions import AllowAny, IsAuthenticated
from rest_framework.views import APIView
from rest_framework.generics import ListAPIView
from rest_framework_simplejwt.tokens import RefreshToken
from django.shortcuts import redirect
from django.http import HttpResponse
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
from django.db.models import Count, Sum, Avg, Q
from datetime import timedelta

from .models import CustomUser, AudioAnalysis, EmailVerificationToken, Donation, Inquiry, InquiryReply, Notification, MessageBoardMessage, MessageBoardReply
from .serializers import (
    UserRegistrationSerializer, UserLoginSerializer, AudioAnalysisSerializer, PasswordResetRequestSerializer, PasswordResetConfirmSerializer,
    DonationSerializer, InquirySerializer, InquiryListSerializer, InquiryReplySerializer, NotificationSerializer,
    MessageBoardMessageSerializer, MessageBoardMessageListSerializer, MessageBoardReplySerializer
)
# from .email_utils import send_welcome_email
from api.tasks import mmse_predict
import stripe
from django.conf import settings
from django.utils import timezone

from core.settings import STRIPE_PUBLISHABLE_KEY, STRIPE_SECRET_KEY

@method_decorator(csrf_exempt, name='dispatch')
class UserRegistrationView(APIView):
    """
    User Registration View
    """
    permission_classes = [AllowAny]

    def post(self, request):
        serializer = UserRegistrationSerializer(data=request.data)
        if serializer.is_valid():
            try:
                user = serializer.save()
                return Response({
                    'success': True,
                    'message': 'Registration successful! A verification code has been sent to your email. Please check your inbox and enter the verification code to activate your account.',
                    'email': user.email,
                    'redirect_to': 'verify-code'  # Indicates frontend should redirect to verification code page
                }, status=status.HTTP_201_CREATED)

            except serializers.ValidationError as e:
                # Serializer validation errors (including duplicate email)
                print(f"Registration validation error: {e}")
                return Response({
                    'success': False,
                    'message': 'Registration failed',
                    'errors': e.detail if hasattr(e, 'detail') else {'non_field_errors': [str(e)]}
                }, status=status.HTTP_400_BAD_REQUEST)

            except Exception as e:
                error_msg = str(e)
                print(f"Registration unexpected error: {error_msg}")  # Log error to console

                # Handle database lock errors
                if 'database is locked' in error_msg:
                    return Response({
                        'success': False,
                        'message': 'System is busy, please try again later.',
                        'errors': {'non_field_errors': ['System is busy, please try again later']}
                    }, status=status.HTTP_503_SERVICE_UNAVAILABLE)

                # Other unexpected errors
                else:
                    return Response({
                        'success': False,
                        'message': 'Registration failed, please try again later.',
                        'errors': {'non_field_errors': ['Internal server error, please try again later']}
                    }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        # Serializer validation failed
        return Response({
            'success': False,
            'message': 'Registration failed',
            'errors': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)


@method_decorator(csrf_exempt, name='dispatch')
class UserLoginView(APIView):
    """
    User Login View
    """
    permission_classes = [AllowAny]

    def post(self, request):
        serializer = UserLoginSerializer(data=request.data)
        if serializer.is_valid():
            user = serializer.validated_data['user']

            # Double-check that user is activated
            if not user.is_active:
                return Response({
                    'success': False,
                    'message': 'Account not activated. Please enter verification code to activate your account'
                }, status=status.HTTP_400_BAD_REQUEST)

            # Update last login time
            user.last_login = timezone.now()
            user.save()

            # Generate JWT tokens
            refresh = RefreshToken.for_user(user)
            access_token = refresh.access_token

            # Get user groups for permission checking
            user_groups = [group.name for group in user.groups.all()]

            return Response({
                'success': True,
                'message': 'Login successful',
                'data': {
                    'user': {
                        'id': str(user.id),
                        'email': user.email,
                        'first_name': user.first_name,
                        'last_name': user.last_name,
                        'display_name': user.display_name,
                        'is_staff': user.is_staff,
                        'is_superuser': user.is_superuser,
                        'is_active': user.is_active,
                        'groups': user_groups,
                    },
                    'tokens': {
                        'access': str(access_token),
                        'refresh': str(refresh)
                    }
                }
            }, status=status.HTTP_200_OK)

        return Response({
            'success': False,
            'message': 'Login failed',
            'errors': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)


class UserLogoutView(APIView):
    """
    User Logout View
    """
    permission_classes = [IsAuthenticated]

    def post(self, request):
        try:
            refresh_token = request.data.get('refresh_token')
            if refresh_token:
                token = RefreshToken(refresh_token)
                token.blacklist()

            return Response({
                'success': True,
                'message': 'Logout successful'
            }, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({
                'success': False,
                'message': 'Logout failed',
                'error': str(e)
            }, status=status.HTTP_400_BAD_REQUEST)


class VerifyCodeView(APIView):
    """
    Verification Code Validation View
    """
    permission_classes = [AllowAny]

    def post(self, request):
        email = request.data.get('email')
        verification_code = request.data.get('verification_code')

        if not email or not verification_code:
            return Response({
                'success': False,
                'message': 'Please provide email and verification code'
            }, status=status.HTTP_400_BAD_REQUEST)

        try:
            # Find verification token - compatible with old and new database structure
            try:
                # Try using verification_code field
                verification_token = EmailVerificationToken.objects.get(
                    user__email=email,
                    verification_code=verification_code,
                    is_used=False
                )
            except Exception:
                # If verification_code field doesn't exist, try matching first 6 characters of token
                tokens = EmailVerificationToken.objects.filter(
                    user__email=email,
                    is_used=False
                )
                verification_token = None
                for token in tokens:
                    if token.token[:6].upper() == verification_code.upper():
                        verification_token = token
                        break

                if not verification_token:
                    raise EmailVerificationToken.DoesNotExist("Invalid verification code")

            if verification_token.is_expired():
                return Response({
                    'success': False,
                    'message': 'Verification code has expired, please request a new one'
                }, status=status.HTTP_400_BAD_REQUEST)

            # Activate user
            user = verification_token.user
            user.is_active = True
            user.save()

            # Mark token as used
            verification_token.is_used = True
            verification_token.save()

            # Send welcome email
            # send_welcome_email(user.email)

            # Generate JWT tokens for automatic login
            from rest_framework_simplejwt.tokens import RefreshToken
            refresh = RefreshToken.for_user(user)
            access_token = refresh.access_token

            return Response({
                'success': True,
                'message': 'Account activated successfully! You are now logged in.',
                'user': {
                    'id': user.id,
                    'email': user.email,
                    'first_name': user.first_name,
                    'last_name': user.last_name,
                },
                'tokens': {
                    'access': str(access_token),
                    'refresh': str(refresh)
                },
                'auto_login': True  # Flag to indicate automatic login
            }, status=status.HTTP_200_OK)

        except EmailVerificationToken.DoesNotExist:
            return Response({
                'success': False,
                'message': 'Invalid or already used verification code'
            }, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response({
                'success': False,
                'message': f'Verification failed: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class EmailVerificationView(APIView):
    """
    Email Verification View (for compatibility)
    """
    permission_classes = [AllowAny]

    def get(self, request):
        token = request.GET.get('token')
        if not token:
            return HttpResponse('Missing verification token', status=400)

        try:
            verification_token = EmailVerificationToken.objects.get(
                token=token,
                is_used=False
            )

            if verification_token.is_expired():
                return HttpResponse('Verification link has expired', status=400)

            # Activate user
            user = verification_token.user
            user.is_active = True
            user.save()

            # Mark token as used
            verification_token.is_used = True
            verification_token.save()

            # Send welcome email
            # send_welcome_email(user.email)

            # Generate JWT tokens for automatic login
            from rest_framework_simplejwt.tokens import RefreshToken
            refresh = RefreshToken.for_user(user)
            access_token = refresh.access_token

            # Redirect to frontend home page with tokens for automatic login
            redirect_url = f'{settings.LOCAL_BASE_URL}/?auto_login=true&access_token={str(access_token)}&refresh_token={str(refresh)}&message=Account activated successfully, you are now logged in'
            return redirect(redirect_url)

        except EmailVerificationToken.DoesNotExist:
            return HttpResponse('Invalid verification link', status=400)
        except Exception as e:
            return HttpResponse(f'Verification failed: {str(e)}', status=500)

class ResendVerificationView(APIView):
    """
    Resend Verification Code View
    """
    permission_classes = [AllowAny]

    def post(self, request):
        email = request.data.get('email')
        if not email:
            return Response({
                'success': False,
                'message': 'Please provide email address'
            }, status=status.HTTP_400_BAD_REQUEST)

        try:
            user = CustomUser.objects.get(email=email)
            if user.is_active:
                return Response({
                    'success': False,
                    'message': 'Account is already activated, no need to resend verification code'
                }, status=status.HTTP_400_BAD_REQUEST)

            # Delete old verification tokens
            EmailVerificationToken.objects.filter(user=user, is_used=False).delete()

            # Send new verification code
            from .email_utils import send_verification_email
            send_verification_email(email)

            return Response({
                'success': True,
                'message': 'Verification code has been resent to your email, please check'
            }, status=status.HTTP_200_OK)

        except CustomUser.DoesNotExist:
            return Response({
                'success': False,
                'message': 'This email is not registered'
            }, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response({
                'success': False,
                'message': f'Failed to send: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class UserProfileView(APIView):
    """
    User Profile View
    """
    permission_classes = [IsAuthenticated]

    def get(self, request):
        """Get user profile"""
        user = request.user

        print(f"🔍 UserProfileView GET request:")
        print(f"   User: {user.email}")
        print(f"   User ID: {user.id}")
        print(f"   Is authenticated: {user.is_authenticated}")

        try:
            # Get user statistics
            from .models import AudioAnalysis
            total_analyses = AudioAnalysis.objects.filter(user=user).count()
            successful_analyses = AudioAnalysis.objects.filter(user=user, status='completed').count()
            pending_analyses = AudioAnalysis.objects.filter(user=user, status='processing').count()

            # Get user groups for permission checking
            user_groups = [group.name for group in user.groups.all()]

            response_data = {
                'success': True,
                'user': {
                    'id': str(user.id),
                    'email': user.email,
                    'first_name': user.first_name,
                    'last_name': user.last_name,
                    'date_joined': user.date_joined.isoformat() if user.date_joined else None,
                    'avatar': user.avatar.url if user.avatar else None,
                    'is_staff': user.is_staff,
                    'is_superuser': user.is_superuser,
                    'is_active': user.is_active,
                    'groups': user_groups,
                },
                'stats': {
                    'total': total_analyses,
                    'successful': successful_analyses,
                    'pending': pending_analyses,
                }
            }

            print(f"✅ Returning user profile data:")
            print(f"   Email: {response_data['user']['email']}")
            print(f"   Name: {response_data['user']['first_name']} {response_data['user']['last_name']}")

            return Response(response_data, status=status.HTTP_200_OK)

        except Exception as e:
            print(f"❌ Error in UserProfileView: {str(e)}")
            return Response({
                'success': False,
                'message': f'Failed to load user profile: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def put(self, request):
        """Update user profile"""
        user = request.user

        print(f"🔍 UserProfileView PUT request:")
        print(f"   User: {user.email}")
        print(f"   Data: {request.data}")

        try:
            # Update basic information
            if 'first_name' in request.data:
                user.first_name = request.data.get('first_name', user.first_name)
            if 'last_name' in request.data:
                user.last_name = request.data.get('last_name', user.last_name)

            # Handle avatar upload
            if 'avatar' in request.FILES:
                user.avatar = request.FILES['avatar']

            user.save()

            response_data = {
                'success': True,
                'message': 'Profile updated successfully',
                'user': {
                    'id': str(user.id),
                    'email': user.email,
                    'first_name': user.first_name,
                    'last_name': user.last_name,
                    'date_joined': user.date_joined.isoformat() if user.date_joined else None,
                    'avatar': user.avatar.url if user.avatar else None,
                }
            }

            print(f"✅ User profile updated successfully")
            return Response(response_data, status=status.HTTP_200_OK)

        except Exception as e:
            print(f"❌ Error updating user profile: {str(e)}")
            return Response({
                'success': False,
                'message': f'Failed to update profile: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class ChangePasswordView(APIView):
    """
    Change Password View
    """
    permission_classes = [IsAuthenticated]

    def post(self, request):
        user = request.user

        print(f"🔍 ChangePasswordView POST request:")
        print(f"   User: {user.email}")
        print(f"   Request data: {request.data}")

        current_password = request.data.get('current_password')
        new_password = request.data.get('new_password')

        print(f"   Current password provided: {bool(current_password)}")
        print(f"   New password provided: {bool(new_password)}")

        if not current_password or not new_password:
            print(f"❌ Missing password fields")
            return Response({
                'success': False,
                'message': 'Please provide current password and new password'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Verify current password
        if not user.check_password(current_password):
            print(f"❌ Current password verification failed")
            return Response({
                'success': False,
                'message': 'Current password is incorrect'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Validate new password strength
        if len(new_password) < 8:
            return Response({
                'success': False,
                'message': 'New password must be at least 8 characters long'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Update password
        user.set_password(new_password)
        user.save()

        print(f"✅ Password changed successfully for user: {user.email}")

        return Response({
            'success': True,
            'message': 'Password changed successfully'
        }, status=status.HTTP_200_OK)


class PasswordResetRequestView(APIView):
    """
    Password Reset Request View
    """
    permission_classes = [AllowAny]

    def post(self, request):
        serializer = PasswordResetRequestSerializer(data=request.data)
        if serializer.is_valid():
            serializer.save()
            return Response({
                'success': True,
                'message': 'The password reset email has been sent, please check your inbox'
            }, status=status.HTTP_200_OK)

        return Response({
            'success': False,
            'message': '请求失败',
            'errors': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)


class PasswordResetConfirmView(APIView):
    """
    Password Reset Confirm View
    """
    permission_classes = [AllowAny]

    def post(self, request):
        serializer = PasswordResetConfirmSerializer(data=request.data)
        if serializer.is_valid():
            serializer.save()
            return Response({
                'success': True,
                'message': 'Password reset successful, please log in with the new password'
            }, status=status.HTTP_200_OK)

        return Response({
            'success': False,
            'message': 'Reset failed',
            'errors': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)


class CheckEmailView(APIView):
    """
    Check Email Status View - supports two-step login
    """
    permission_classes = [AllowAny]

    def get(self, request):
        email = request.GET.get('email')
        if not email:
            return Response({
                'success': False,
                'message': 'Please provide email address'
            }, status=status.HTTP_400_BAD_REQUEST)

        exists = CustomUser.objects.filter(email=email).exists()
        return Response({
            'success': True,
            'exists': exists,
            'message': 'Email is registered' if exists else 'Email is available'
        }, status=status.HTTP_200_OK)

    def post(self, request):
        """Check email status for two-step login"""
        email = request.data.get('email')
        if not email:
            return Response({
                'success': False,
                'message': 'Please provide email address'
            }, status=status.HTTP_400_BAD_REQUEST)

        try:
            user = CustomUser.objects.get(email=email)

            # Check user activation status
            if not user.is_active:
                return Response({
                    'success': True,
                    'exists': True,
                    'is_active': False,
                    'message': 'Account not activated, please activate your account first',
                    'action': 'activate'  # Frontend decides to show activation options based on this field
                }, status=status.HTTP_200_OK)
            else:
                return Response({
                    'success': True,
                    'exists': True,
                    'is_active': True,
                    'message': 'Please enter password',
                    'action': 'login'  # Frontend shows password input based on this field
                }, status=status.HTTP_200_OK)

        except CustomUser.DoesNotExist:
            return Response({
                'success': True,
                'exists': False,
                'is_active': False,
                'message': 'Email not registered, please register first',
                'action': 'register'  # Frontend shows registration options based on this field
            }, status=status.HTTP_200_OK)


class AudioUploadView(APIView):
    """
    Audio Upload View
    """
    permission_classes = [IsAuthenticated]
    parser_classes = [MultiPartParser, FormParser]

    def post(self, request):
        serializer = AudioAnalysisSerializer(data=request.data, context={'request': request})
        if serializer.is_valid():
            instance = serializer.save()
            audio_path = instance.audio_file.path

            # Debug information
            print(f"🔍 Audio upload successful:")
            print(f"   File ID: {instance.id}")
            print(f"   File path: {audio_path}")
            print(f"   User: {instance.user.email}")

            # Start Celery task for analysis
            try:
                print(f"🚀 Starting Celery task for analysis:")
                print(f"   Instance ID: {instance.id} (type: {type(instance.id)})")
                print(f"   Audio path: {audio_path}")
                print(f"   User: {instance.user.email}")

                task = mmse_predict.delay(str(instance.id), audio_path)
                print(f"✅ Celery task started: {task.id}")

                # Update status to processing
                instance.status = "processing"
                print(f"✅ Status updated to processing")

            except Exception as e:
                print(f"❌ Celery task failed to start: {str(e)}")
                import traceback
                print(f"❌ Error details: {traceback.format_exc()}")
                instance.status = "failed"

            instance.save()

            return Response({
                "success": True,
                "message": "Audio uploaded successfully, analysis in progress!",
                "data": AudioAnalysisSerializer(instance).data
            }, status=status.HTTP_201_CREATED)
        return Response({
            "success": False,
            "message": "Upload failed",
            "errors": serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)


class AudioHistoryView(ListAPIView):
    """
    Audio Analysis History View
    """
    serializer_class = AudioAnalysisSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        return AudioAnalysis.objects.filter(user=self.request.user).order_by('-upload_time')

    def list(self, request, *args, **kwargs):
        queryset = self.get_queryset()
        serializer = self.get_serializer(queryset, many=True)
        return Response({
            'success': True,
            'data': serializer.data,
            'count': queryset.count()
        }, status=status.HTTP_200_OK)


@method_decorator(csrf_exempt, name='dispatch')
class HealthCheckView(APIView):
    """
    Health Check View
    """
    permission_classes = [AllowAny]

    def get(self, request):
        return Response({
            'success': True,
            'message': 'API service is running normally',
            'timestamp': timezone.now().isoformat()
        }, status=status.HTTP_200_OK)


class UserTermsAgreementView(APIView):
    """
    User Guidelines and Privacy Agreement Management
    """
    permission_classes = [IsAuthenticated]

    def get(self, request):
        """Get user agreement status"""
        user = request.user

        print(f"🔍 UserTermsAgreementView GET request:")
        print(f"   User: {user.email}")
        print(f"   Has agreed to terms: {user.has_agreed_to_terms}")
        print(f"   Terms agreed at: {user.terms_agreed_at}")

        return Response({
            'success': True,
            'data': {
                'has_agreed_to_terms': user.has_agreed_to_terms,
                'terms_agreed_at': user.terms_agreed_at.isoformat() if user.terms_agreed_at else None
            }
        }, status=status.HTTP_200_OK)

    def post(self, request):
        """User agrees to terms"""
        user = request.user
        agreed = request.data.get('agreed', False)

        print(f"🔍 UserTermsAgreementView POST request:")
        print(f"   User: {user.email}")
        print(f"   Agreed: {agreed}")

        if agreed:
            user.has_agreed_to_terms = True
            user.terms_agreed_at = timezone.now()
            user.save()

            print(f"✅ User {user.email} agreed to terms at {user.terms_agreed_at}")

            return Response({
                'success': True,
                'message': 'Your agreement status has been successfully recorded',
                'data': {
                    'has_agreed_to_terms': user.has_agreed_to_terms,
                    'terms_agreed_at': user.terms_agreed_at.isoformat()
                }
            }, status=status.HTTP_200_OK)
        else:
            print(f"❌ User {user.email} declined terms")
            return Response({
                'success': False,
                'message': 'You need to agree to the user guidelines and privacy policy to use the audio upload feature'
            }, status=status.HTTP_400_BAD_REQUEST)

    def delete(self, request):
        """Reset user agreement status (for testing purposes)"""
        user = request.user
        user.has_agreed_to_terms = False
        user.terms_agreed_at = None
        user.save()

        print(f"🔄 Reset terms agreement for user {user.email}")

        return Response({
            'success': True,
            'message': 'User agreement status has been reset',
            'data': {
                'has_agreed_to_terms': user.has_agreed_to_terms,
                'terms_agreed_at': None
            }
        }, status=status.HTTP_200_OK)


class AudioAnalysisDetailView(APIView):
    """
    Audio Analysis Detail View
    """
    permission_classes = [IsAuthenticated]

    def get(self, request, analysis_id):
        """Get audio analysis details"""
        try:
            analysis = AudioAnalysis.objects.get(id=analysis_id, user=request.user)
            serializer = AudioAnalysisSerializer(analysis)

            # Get related donation records
            donations = Donation.objects.filter(audio_analysis=analysis)
            donation_serializer = DonationSerializer(donations, many=True)

            # Check if there are successful donation records
            has_successful_donation = donations.filter(status='completed').exists()

            return Response({
                'success': True,
                'data': {
                    'analysis': serializer.data,
                    'donations': donation_serializer.data,
                    'has_successful_donation': has_successful_donation
                }
            }, status=status.HTTP_200_OK)

        except AudioAnalysis.DoesNotExist:
            return Response({
                'success': False,
                'message': 'Audio analysis record not found'
            }, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            return Response({
                'success': False,
                'message': f'Failed to get details: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class DonationCreateView(APIView):
    """
    Create Donation View
    """
    permission_classes = [IsAuthenticated]

    def post(self, request):
        """Create donation record and handle Stripe payment"""
        try:

            # Set Stripe API key
            stripe.api_key = STRIPE_SECRET_KEY

            audio_analysis_id = request.data.get('audio_analysis_id')
            amount = request.data.get('amount', 150)

            print(f"Donation request data: audio_analysis_id={audio_analysis_id}, amount={amount}")
            print(f"Request user: {request.user}")
            print(f"Full request data: {request.data}")

            # Validate required fields
            if not audio_analysis_id:
                return Response({
                    'success': False,
                    'message': 'Audio analysis ID is required',
                    'errors': {'audio_analysis_id': ['This field is required.']}
                }, status=status.HTTP_400_BAD_REQUEST)

            # Validate UUID format
            try:
                import uuid
                uuid.UUID(str(audio_analysis_id))
            except ValueError:
                return Response({
                    'success': False,
                    'message': 'Invalid audio analysis ID format',
                    'errors': {'audio_analysis_id': ['Invalid UUID format.']}
                }, status=status.HTTP_400_BAD_REQUEST)

            # Validate audio analysis record
            try:
                audio_analysis = AudioAnalysis.objects.get(id=audio_analysis_id, user=request.user)
                print(f"Found audio analysis: {audio_analysis.id} for user: {request.user}")
            except AudioAnalysis.DoesNotExist:
                return Response({
                    'success': False,
                    'message': 'Audio analysis record not found or access denied',
                    'errors': {'audio_analysis_id': ['Invalid audio analysis ID or access denied.']}
                }, status=status.HTTP_404_NOT_FOUND)

            # Create Stripe payment intent
            try:
                print(f"Creating Stripe PaymentIntent with amount: {int(amount) * 100} cents")
                payment_intent = stripe.PaymentIntent.create(
                    amount=int(amount) * 100,  # Stripe uses cents
                    currency='usd',
                    metadata={
                        'user_id': str(request.user.id),
                        'audio_analysis_id': str(audio_analysis_id)
                    }
                )
                print(f"PaymentIntent created successfully: {payment_intent.id}")
            except stripe.error.StripeError as e:
                print(f"Stripe error: {str(e)}")
                return Response({
                    'success': False,
                    'message': f'Payment processing error: {str(e)}',
                    'errors': {'stripe': [str(e)]}
                }, status=status.HTTP_400_BAD_REQUEST)

            # Create donation record
            donation_data = {
                'audio_analysis': audio_analysis.id,
                'amount': amount,
                'stripe_payment_intent_id': payment_intent.id
            }

            print(f"Donation data for serializer: {donation_data}")

            serializer = DonationSerializer(data=donation_data, context={'request': request})
            print(f"Serializer created with data: {donation_data}")
            print(f"Serializer context: {serializer.context}")

            if serializer.is_valid():
                print("Serializer is valid, saving donation...")
                donation = serializer.save()
                print(f"Donation saved successfully: {donation.id}")

                return Response({
                    'success': True,
                    'data': {
                        'donation': DonationSerializer(donation).data,
                        'client_secret': payment_intent.client_secret
                    }
                }, status=status.HTTP_201_CREATED)
            else:
                print(f"Serializer validation failed!")
                print(f"Serializer errors: {serializer.errors}")
                print(f"Serializer error details:")
                for field, errors in serializer.errors.items():
                    print(f"  {field}: {errors}")

                return Response({
                    'success': False,
                    'message': 'Failed to create donation record',
                    'errors': serializer.errors
                }, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            print(f"Exception in DonationCreateView: {str(e)}")
            import traceback
            traceback.print_exc()
            return Response({
                'success': False,
                'message': f'Failed to create donation: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class DonationConfirmView(APIView):
    """
    Confirm Donation Payment View
    """
    permission_classes = [IsAuthenticated]

    def post(self, request):
        """Confirm payment and update donation status"""
        try:
            # Validate Stripe API key
            if not STRIPE_SECRET_KEY or STRIPE_SECRET_KEY == 'sk_test_temp_key_please_replace':
                return Response({
                    'success': False,
                    'message': 'Stripe API key is not configured. Please set STRIPE_SECRET_KEY environment variable.'
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

            stripe.api_key = STRIPE_SECRET_KEY

            payment_intent_id = request.data.get('payment_intent_id')
            print(f"Confirming donation for payment_intent_id: {payment_intent_id}")
            print(f"Request user: {request.user}")

            if not payment_intent_id:
                return Response({
                    'success': False,
                    'message': 'Payment intent ID is required'
                }, status=status.HTTP_400_BAD_REQUEST)

            # Get payment intent status from Stripe
            print(f"Retrieving PaymentIntent from Stripe: {payment_intent_id}")
            payment_intent = stripe.PaymentIntent.retrieve(payment_intent_id)
            print(f"PaymentIntent status: {payment_intent.status}")
            print(f"PaymentIntent amount: {payment_intent.amount}")
            print(f"PaymentIntent metadata: {payment_intent.metadata}")

            # Update donation record
            try:
                print(f"Looking for donation with payment_intent_id: {payment_intent_id}")
                donation = Donation.objects.get(
                    stripe_payment_intent_id=payment_intent_id,
                    user=request.user
                )
                print(f"Found donation: {donation.id}, current status: {donation.status}")

                if payment_intent.status == 'succeeded':
                    print("Payment succeeded, updating donation to completed")
                    donation.status = 'completed'
                    donation.completed_at = timezone.now()
                    donation.stripe_charge_id = payment_intent.latest_charge
                    print(f"Set stripe_charge_id: {payment_intent.latest_charge}")
                elif payment_intent.status == 'payment_failed':
                    print("Payment failed, updating donation to failed")
                    donation.status = 'failed'
                else:
                    print(f"Payment status is: {payment_intent.status}")

                donation.save()
                print(f"Donation saved with status: {donation.status}")

                return Response({
                    'success': True,
                    'data': {
                        'donation': DonationSerializer(donation).data,
                        'payment_status': payment_intent.status,
                        'stripe_payment_intent_id': payment_intent.id,
                        'stripe_charge_id': payment_intent.latest_charge,
                        'amount_received': payment_intent.amount_received
                    }
                }, status=status.HTTP_200_OK)

            except Donation.DoesNotExist:
                return Response({
                    'success': False,
                    'message': 'Donation record not found'
                }, status=status.HTTP_404_NOT_FOUND)

        except Exception as e:
            return Response({
                'success': False,
                'message': f'Payment confirmation failed: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class StripeConfigView(APIView):
    """
    Get Stripe Configuration Information
    """
    permission_classes = [AllowAny]

    def get(self, request):
        """Get Stripe public key"""
        try:
            return Response({
                'success': True,
                'data': {
                    'publishable_key': STRIPE_PUBLISHABLE_KEY
                }
            }, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({
                'success': False,
                'message': f'Failed to get configuration: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class DonationStatusView(APIView):
    """
    Check Donation Status for Specific Analysis Record
    """
    permission_classes = [IsAuthenticated]

    def get(self, request, analysis_id):
        """Check if user has donated for specific analysis record"""
        try:
            # Validate audio analysis record exists and belongs to current user
            try:
                audio_analysis = AudioAnalysis.objects.get(id=analysis_id, user=request.user)
            except AudioAnalysis.DoesNotExist:
                return Response({
                    'success': False,
                    'message': 'Audio analysis record not found'
                }, status=status.HTTP_404_NOT_FOUND)

            # Check if there are successful donation records
            successful_donation = Donation.objects.filter(
                audio_analysis=audio_analysis,
                user=request.user,
                status='completed'
            ).first()

            return Response({
                'success': True,
                'data': {
                    'analysis_id': str(analysis_id),
                    'has_donated': successful_donation is not None,
                    'donation_info': DonationSerializer(successful_donation).data if successful_donation else None
                }
            }, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({
                'success': False,
                'message': f'Failed to check donation status: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class DashboardUserStatsView(APIView):
    """
    Dashboard User Statistics View
    """
    permission_classes = [IsAuthenticated]

    def get(self, request):
        """Get user statistics for dashboard"""
        try:
            # Check if user has admin permissions
            if not (request.user.is_staff or request.user.groups.filter(name='Data Analysts').exists()):
                return Response({
                    'success': False,
                    'message': 'Access denied. Admin permissions required.'
                }, status=status.HTTP_403_FORBIDDEN)

            # Basic statistics
            total_users = CustomUser.objects.count()
            active_users = CustomUser.objects.filter(is_active=True).count()
            inactive_users = total_users - active_users

            # This month new users
            this_month_start = timezone.now().replace(day=1, hour=0, minute=0, second=0, microsecond=0)
            new_users_this_month = CustomUser.objects.filter(
                date_joined__gte=this_month_start
            ).count()

            # Monthly registration trend (past 12 months)
            monthly_stats = []
            for i in range(12):
                if i == 0:
                    end_date = timezone.now()
                    start_date = end_date.replace(day=1)
                else:
                    end_date = (timezone.now().replace(day=1) - timedelta(days=1)).replace(day=1)
                    for _ in range(i-1):
                        end_date = (end_date - timedelta(days=1)).replace(day=1)
                    start_date = end_date
                    end_date = (end_date.replace(day=28) + timedelta(days=4)).replace(day=1) - timedelta(days=1)

                count = CustomUser.objects.filter(
                    date_joined__gte=start_date,
                    date_joined__lte=end_date
                ).count()

                monthly_stats.append({
                    'month': start_date.strftime('%Y-%m'),
                    'count': count,
                    'month_name': start_date.strftime('%b %Y')
                })

            monthly_stats.reverse()

            return Response({
                'success': True,
                'data': {
                    'total_users': total_users,
                    'active_users': active_users,
                    'inactive_users': inactive_users,
                    'new_users_this_month': new_users_this_month,
                    'monthly_registration': monthly_stats
                }
            }, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({
                'success': False,
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class DashboardAnalysisStatsView(APIView):
    """
    Dashboard Analysis Statistics View
    """
    permission_classes = [IsAuthenticated]

    def get(self, request):
        """Get analysis statistics for dashboard"""
        try:
            # Check if user has admin permissions
            if not (request.user.is_staff or request.user.groups.filter(name='Data Analysts').exists()):
                return Response({
                    'success': False,
                    'message': 'Access denied. Admin permissions required.'
                }, status=status.HTTP_403_FORBIDDEN)

            total_analyses = AudioAnalysis.objects.count()
            completed_analyses = AudioAnalysis.objects.filter(status='completed').count()
            processing_analyses = AudioAnalysis.objects.filter(status='processing').count()
            failed_analyses = AudioAnalysis.objects.filter(status='failed').count()

            # Status distribution
            status_distribution = [
                {'status': 'Completed', 'count': completed_analyses},
                {'status': 'Processing', 'count': processing_analyses},
                {'status': 'Failed', 'count': failed_analyses}
            ]

            # Daily analysis trend (past 30 days)
            daily_stats = []
            for i in range(30):
                date = timezone.now().date() - timedelta(days=i)
                count = AudioAnalysis.objects.filter(
                    upload_time__date=date
                ).count()
                daily_stats.append({
                    'date': date.strftime('%Y-%m-%d'),
                    'count': count
                })

            daily_stats.reverse()

            return Response({
                'success': True,
                'data': {
                    'total_analyses': total_analyses,
                    'completed_analyses': completed_analyses,
                    'processing_analyses': processing_analyses,
                    'failed_analyses': failed_analyses,
                    'status_distribution': status_distribution,
                    'daily_analyses': daily_stats
                }
            }, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({
                'success': False,
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class DashboardDonationStatsView(APIView):
    """
    Dashboard Donation Statistics View
    """
    permission_classes = [IsAuthenticated]

    def get(self, request):
        """Get donation statistics for dashboard"""
        try:
            # Check if user has admin permissions
            if not (request.user.is_staff or request.user.groups.filter(name='Data Analysts').exists()):
                return Response({
                    'success': False,
                    'message': 'Access denied. Admin permissions required.'
                }, status=status.HTTP_403_FORBIDDEN)

            total_donations = Donation.objects.filter(status='completed').count()
            total_amount = Donation.objects.filter(
                status='completed'
            ).aggregate(total=Sum('amount'))['total'] or 0

            avg_donation = Donation.objects.filter(
                status='completed'
            ).aggregate(avg=Avg('amount'))['avg'] or 0

            # Monthly donation trend (past 12 months)
            monthly_donations = []
            for i in range(12):
                if i == 0:
                    end_date = timezone.now()
                    start_date = end_date.replace(day=1)
                else:
                    end_date = (timezone.now().replace(day=1) - timedelta(days=1)).replace(day=1)
                    for _ in range(i-1):
                        end_date = (end_date - timedelta(days=1)).replace(day=1)
                    start_date = end_date
                    end_date = (end_date.replace(day=28) + timedelta(days=4)).replace(day=1) - timedelta(days=1)

                month_total = Donation.objects.filter(
                    status='completed',
                    created_at__gte=start_date,
                    created_at__lte=end_date
                ).aggregate(total=Sum('amount'))['total'] or 0

                month_count = Donation.objects.filter(
                    status='completed',
                    created_at__gte=start_date,
                    created_at__lte=end_date
                ).count()

                monthly_donations.append({
                    'month': start_date.strftime('%Y-%m'),
                    'month_name': start_date.strftime('%b %Y'),
                    'amount': float(month_total),
                    'count': month_count
                })

            monthly_donations.reverse()

            return Response({
                'success': True,
                'data': {
                    'total_donations': total_donations,
                    'total_amount': float(total_amount),
                    'average_donation': round(float(avg_donation), 2),
                    'monthly_donations': monthly_donations
                }
            }, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({
                'success': False,
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class DashboardRecentActivitiesView(APIView):
    """
    Dashboard Recent Activities View
    """
    permission_classes = [IsAuthenticated]

    def get(self, request):
        """Get recent activities for dashboard"""
        try:
            # Check if user has admin permissions
            if not (request.user.is_staff or request.user.groups.filter(name='Data Analysts').exists()):
                return Response({
                    'success': False,
                    'message': 'Access denied. Admin permissions required.'
                }, status=status.HTTP_403_FORBIDDEN)

            # Recent registered users
            recent_users = CustomUser.objects.order_by('-date_joined')[:10].values(
                'email', 'first_name', 'last_name', 'date_joined', 'is_active'
            )

            # Recent audio analyses
            recent_analyses = AudioAnalysis.objects.select_related('user').order_by('-upload_time')[:10]
            recent_analyses_data = [{
                'user': analysis.user.email,
                'filename': analysis.filename,
                'status': analysis.status,
                'date': analysis.upload_time.isoformat()
            } for analysis in recent_analyses]

            # Recent donations
            recent_donations = Donation.objects.select_related('user').order_by('-created_at')[:10]
            recent_donations_data = [{
                'user': donation.user.email,
                'amount': float(donation.amount),
                'status': donation.status,
                'date': donation.created_at.isoformat()
            } for donation in recent_donations]

            return Response({
                'success': True,
                'data': {
                    'recent_users': list(recent_users),
                    'recent_analyses': recent_analyses_data,
                    'recent_donations': recent_donations_data
                }
            }, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({
                'success': False,
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class DashboardUserListView(APIView):
    """
    Dashboard User List View - Detailed user information with analysis and donation data
    """
    permission_classes = [IsAuthenticated]

    def get(self, request):
        """Get detailed user list with analysis and donation data"""
        try:
            # Check if user has admin permissions
            if not (request.user.is_staff or request.user.groups.filter(name='Data Analysts').exists()):
                return Response({
                    'success': False,
                    'message': 'Access denied. Admin permissions required.'
                }, status=status.HTTP_403_FORBIDDEN)

            # Get pagination parameters
            page = int(request.GET.get('page', 1))
            page_size = int(request.GET.get('page_size', 20))
            offset = (page - 1) * page_size

            # Get users with their related data
            users = CustomUser.objects.all().order_by('-date_joined')[offset:offset + page_size]
            total_users = CustomUser.objects.count()

            user_data = []
            for user in users:
                # Get user's audio analyses
                analyses = AudioAnalysis.objects.filter(user=user).order_by('-upload_time')
                analyses_data = [{
                    'id': str(analysis.id),
                    'filename': analysis.filename,
                    'status': analysis.status,
                    'upload_time': analysis.upload_time.isoformat(),
                    'relationship': analysis.relationship or 'Not specified',
                    'age': analysis.age or 'Not specified'
                } for analysis in analyses[:5]]  # Show latest 5 analyses

                # Get user's donations
                donations = Donation.objects.filter(user=user).order_by('-created_at')
                donations_data = [{
                    'id': str(donation.id),
                    'amount': float(donation.amount),
                    'status': donation.status,
                    'created_at': donation.created_at.isoformat(),
                    'completed_at': donation.completed_at.isoformat() if donation.completed_at else None
                } for donation in donations[:5]]  # Show latest 5 donations

                # Calculate totals
                total_analyses = analyses.count()
                completed_analyses = analyses.filter(status='completed').count()
                total_donations = donations.filter(status='completed').aggregate(
                    total=Sum('amount')
                )['total'] or 0
                donation_count = donations.filter(status='completed').count()

                user_data.append({
                    'id': str(user.id),
                    'email': user.email,
                    'first_name': user.first_name,
                    'last_name': user.last_name,
                    'full_name': f"{user.first_name} {user.last_name}".strip(),
                    'is_active': user.is_active,
                    'date_joined': user.date_joined.isoformat(),
                    'last_login': user.last_login.isoformat() if user.last_login else None,
                    'analyses': {
                        'total': total_analyses,
                        'completed': completed_analyses,
                        'recent': analyses_data
                    },
                    'donations': {
                        'total_amount': float(total_donations),
                        'count': donation_count,
                        'recent': donations_data
                    }
                })

            return Response({
                'success': True,
                'data': {
                    'users': user_data,
                    'pagination': {
                        'page': page,
                        'page_size': page_size,
                        'total': total_users,
                        'total_pages': (total_users + page_size - 1) // page_size
                    }
                }
            }, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({
                'success': False,
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@method_decorator(csrf_exempt, name='dispatch')
class ContactInquiryView(APIView):
    """
    Contact Us Inquiry API View
    """
    permission_classes = [AllowAny]  # Allow anonymous users to submit inquiries

    def post(self, request):
        """Submit a contact inquiry"""
        import logging
        logger = logging.getLogger(__name__)

        try:
            logger.info("ContactInquiryView: Received POST request")

            # Get data from request (support both JSON and form data)
            if request.content_type == 'application/json':
                # JSON data
                name = request.data.get('name', '').strip()
                email = request.data.get('email', '').strip()
                inquiry_type = request.data.get('inquiry_type')
                description = request.data.get('description', '').strip()
                logger.info("ContactInquiryView: Processing JSON data")
            else:
                # Form data from frontend
                name = request.POST.get('name', '').strip()
                email = request.POST.get('email', '').strip()
                inquiry_type = request.POST.get('inquiry_type')
                description = request.POST.get('description', '').strip()
                logger.info("ContactInquiryView: Processing form data")

            logger.info(f"ContactInquiryView: Data received - email={email}, type={inquiry_type}")

            # Validate required fields
            if not email:
                return Response({
                    'success': False,
                    'error': 'Email address is required'
                }, status=status.HTTP_400_BAD_REQUEST)

            if not description:
                return Response({
                    'success': False,
                    'error': 'Message is required'
                }, status=status.HTTP_400_BAD_REQUEST)

            # Validate inquiry type
            try:
                inquiry_type = int(inquiry_type) if inquiry_type is not None else 0
                if inquiry_type not in [choice[0] for choice in Inquiry.INQUIRY_TYPE_CHOICES]:
                    inquiry_type = 0  # Default to General Inquiry
            except (ValueError, TypeError):
                inquiry_type = 0

            # Validate email format
            from django.core.validators import validate_email
            from django.core.exceptions import ValidationError
            try:
                validate_email(email)
            except ValidationError:
                return Response({
                    'success': False,
                    'error': 'Invalid email address format'
                }, status=status.HTTP_400_BAD_REQUEST)

            # Validate message length
            if len(description) > 1500:
                return Response({
                    'success': False,
                    'error': 'Message is too long (maximum 1500 characters)'
                }, status=status.HTTP_400_BAD_REQUEST)

            # Create inquiry with safe field handling
            logger.info("ContactInquiryView: Creating inquiry object")

            try:
                # Try to create with all fields including status
                inquiry = Inquiry.objects.create(
                    name=name if name else None,
                    email=email,
                    inquiry_type=inquiry_type,
                    description=description,
                    status='new'  # Try with status field
                )
                logger.info(f"ContactInquiryView: Inquiry created with status field, ID: {inquiry.id}")
            except Exception as status_error:
                logger.warning(f"ContactInquiryView: Failed to create with status field: {status_error}")
                # Fallback: create without status field
                try:
                    inquiry = Inquiry.objects.create(
                        name=name if name else None,
                        email=email,
                        inquiry_type=inquiry_type,
                        description=description
                    )
                    logger.info(f"ContactInquiryView: Inquiry created without status field, ID: {inquiry.id}")
                except Exception as create_error:
                    logger.error(f"ContactInquiryView: Failed to create inquiry: {create_error}")
                    raise create_error

            logger.info("ContactInquiryView: Inquiry submitted successfully")
            return Response({
                'success': True,
                'message': 'Your inquiry has been submitted successfully. We will get back to you within 48 hours.',
                'inquiry_id': str(inquiry.id)
            }, status=status.HTTP_201_CREATED)

        except Exception as e:
            # Log the actual error for debugging
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"ContactInquiryView error: {str(e)}", exc_info=True)

            return Response({
                'success': False,
                'error': 'Failed to submit inquiry. Please try again.'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class ContactMessageListView(APIView):
    """
    Contact Message List View for Admin
    """
    permission_classes = [IsAuthenticated]

    def get(self, request):
        """Get list of contact messages for admin"""
        import logging
        logger = logging.getLogger(__name__)

        try:
            logger.info(f"ContactMessageListView: Request from user {request.user.email if hasattr(request.user, 'email') else request.user}")

            # Check if user has admin permissions (same logic as other admin views)
            if not (request.user.is_staff or request.user.groups.filter(name='Data Analysts').exists()):
                logger.warning(f"ContactMessageListView: Access denied for user {request.user}")
                return Response({
                    'success': False,
                    'message': 'Access denied. Admin permissions required.'
                }, status=status.HTTP_403_FORBIDDEN)

            logger.info(f"ContactMessageListView: Access granted for user {request.user.email if hasattr(request.user, 'email') else request.user}")

            # Get query parameters
            page = int(request.GET.get('page', 1))
            page_size = int(request.GET.get('page_size', 20))
            search = request.GET.get('search', '').strip()
            status_filter = request.GET.get('status', '').strip()
            type_filter = request.GET.get('type', '').strip()

            # Build query
            queryset = Inquiry.objects.all()
            logger.info(f"ContactMessageListView: Total inquiries in database: {queryset.count()}")

            # Apply search filter
            if search:
                queryset = queryset.filter(
                    Q(name__icontains=search) |
                    Q(email__icontains=search) |
                    Q(description__icontains=search)
                )
                logger.info(f"ContactMessageListView: After search filter: {queryset.count()}")

            # Apply status filter (only if status field exists)
            if status_filter:
                try:
                    # Check if status field exists
                    inquiry_fields = [f.name for f in Inquiry._meta.get_fields()]
                    if 'status' in inquiry_fields:
                        queryset = queryset.filter(status=status_filter)
                        logger.info(f"ContactMessageListView: After status filter: {queryset.count()}")
                    else:
                        logger.warning("ContactMessageListView: Status field not found in Inquiry model")
                except Exception as e:
                    logger.error(f"ContactMessageListView: Error applying status filter: {e}")

            # Apply type filter
            if type_filter:
                try:
                    type_int = int(type_filter)
                    queryset = queryset.filter(inquiry_type=type_int)
                except ValueError:
                    pass

            # Get total count
            total_count = queryset.count()
            logger.info(f"ContactMessageListView: Final query count: {total_count}")

            # Apply pagination
            start = (page - 1) * page_size
            end = start + page_size
            messages = queryset[start:end]
            logger.info(f"ContactMessageListView: Paginated messages: {len(messages)}")

            # Serialize data
            try:
                serializer = InquiryListSerializer(messages, many=True)
                serialized_data = serializer.data
                logger.info(f"ContactMessageListView: Serialized {len(serialized_data)} messages")
            except Exception as e:
                logger.error(f"ContactMessageListView: Serialization error: {e}")
                # Fallback: create simple data structure
                serialized_data = []
                for message in messages:
                    try:
                        serialized_data.append({
                            'id': str(message.id),
                            'name': message.name or 'Anonymous User',
                            'email': message.email,
                            'inquiry_type': message.inquiry_type,
                            'inquiry_type_display': message.get_inquiry_type_display(),
                            'description': message.description,
                            'status': getattr(message, 'status', 'new'),
                            'status_display': getattr(message, 'status', 'New').title(),
                            'created_at': message.created_at.isoformat() if hasattr(message, 'created_at') else None,
                            'reply_count': 0  # Default for now
                        })
                    except Exception as msg_error:
                        logger.error(f"ContactMessageListView: Error serializing message {message.id}: {msg_error}")
                        continue

            response_data = {
                'success': True,
                'data': {
                    'messages': serialized_data,
                    'pagination': {
                        'page': page,
                        'page_size': page_size,
                        'total': total_count,
                        'total_pages': (total_count + page_size - 1) // page_size
                    }
                }
            }

            logger.info(f"ContactMessageListView: Returning {len(serialized_data)} messages")
            return Response(response_data, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error(f"ContactMessageListView: Unexpected error: {e}", exc_info=True)
            return Response({
                'success': False,
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class ContactMessageDetailView(APIView):
    """
    Contact Message Detail View for Admin
    """
    permission_classes = [IsAuthenticated]

    def get(self, request, message_id):
        """Get detailed message information"""
        import logging
        logger = logging.getLogger(__name__)

        try:
            logger.info(f"ContactMessageDetailView: Request from user {request.user.email} for message {message_id}")

            # Check if user has admin permissions
            if not (request.user.is_staff or request.user.groups.filter(name='Data Analysts').exists()):
                return Response({
                    'success': False,
                    'message': 'Access denied. Admin permissions required.'
                }, status=status.HTTP_403_FORBIDDEN)

            # Get message
            try:
                message = Inquiry.objects.get(id=message_id)
            except Inquiry.DoesNotExist:
                return Response({
                    'success': False,
                    'message': 'Message not found'
                }, status=status.HTTP_404_NOT_FOUND)

            # Serialize data
            serializer = InquirySerializer(message)

            return Response({
                'success': True,
                'data': serializer.data
            }, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error(f"ContactMessageDetailView GET: Unexpected error: {e}", exc_info=True)
            return Response({
                'success': False,
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def put(self, request, message_id):
        """Update message status"""
        import logging
        logger = logging.getLogger(__name__)

        try:
            logger.info(f"ContactMessageDetailView PUT: Status update request from user {request.user.email} for message {message_id}")

            # Check if user has admin permissions
            if not (request.user.is_staff or request.user.groups.filter(name='Data Analysts').exists()):
                return Response({
                    'success': False,
                    'message': 'Access denied. Admin permissions required.'
                }, status=status.HTTP_403_FORBIDDEN)

            # Get message
            try:
                message = Inquiry.objects.get(id=message_id)
            except Inquiry.DoesNotExist:
                return Response({
                    'success': False,
                    'message': 'Message not found'
                }, status=status.HTTP_404_NOT_FOUND)

            # Update status
            new_status = request.data.get('status')
            if new_status and new_status in [choice[0] for choice in Inquiry.STATUS_CHOICES]:
                message.status = new_status
                message.save()

                return Response({
                    'success': True,
                    'message': 'Message status updated successfully',
                    'data': InquirySerializer(message).data
                }, status=status.HTTP_200_OK)
            else:
                return Response({
                    'success': False,
                    'message': 'Invalid status value'
                }, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            logger.error(f"ContactMessageDetailView PUT: Unexpected error: {e}", exc_info=True)
            return Response({
                'success': False,
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class ContactMessageReplyView(APIView):
    """
    Contact Message Reply View for Admin
    """
    permission_classes = [IsAuthenticated]

    def post(self, request, message_id):
        """Send reply to contact message"""
        import logging
        logger = logging.getLogger(__name__)

        try:
            logger.info(f"ContactMessageReplyView: Reply request from user {request.user.email} for message {message_id}")

            # Check if user has admin permissions
            if not (request.user.is_staff or request.user.groups.filter(name='Data Analysts').exists()):
                return Response({
                    'success': False,
                    'message': 'Access denied. Admin permissions required.'
                }, status=status.HTTP_403_FORBIDDEN)

            # Get message
            try:
                message = Inquiry.objects.get(id=message_id)
            except Inquiry.DoesNotExist:
                return Response({
                    'success': False,
                    'message': 'Message not found'
                }, status=status.HTTP_404_NOT_FOUND)

            # Get reply content
            reply_message = request.data.get('message', '').strip()
            if not reply_message:
                return Response({
                    'success': False,
                    'message': 'Reply message is required'
                }, status=status.HTTP_400_BAD_REQUEST)

            if len(reply_message) > 2000:
                return Response({
                    'success': False,
                    'message': 'Reply message is too long (maximum 2000 characters)'
                }, status=status.HTTP_400_BAD_REQUEST)

            # Create reply
            reply = InquiryReply.objects.create(
                inquiry=message,
                admin_user=request.user,
                message=reply_message
            )

            # Update inquiry status to replied
            if message.status == 'new':
                message.status = 'replied'
                message.save()

            # Create notification for registered user
            from .models import Notification
            notification = Notification.create_message_reply_notification(message, reply)
            if notification:
                logger.info(f"ContactMessageReplyView: Created notification for user {message.email}")

            # TODO: Send email notification to user
            # This would be implemented with email service

            return Response({
                'success': True,
                'message': 'Reply sent successfully',
                'data': {
                    'reply': InquiryReplySerializer(reply).data,
                    'inquiry': InquirySerializer(message).data
                }
            }, status=status.HTTP_201_CREATED)

        except Exception as e:
            logger.error(f"ContactMessageReplyView: Unexpected error: {e}", exc_info=True)
            return Response({
                'success': False,
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class UserNotificationListView(APIView):
    """
    Get user notifications
    """
    permission_classes = [IsAuthenticated]

    def get(self, request):
        """Get user notifications"""
        import logging
        logger = logging.getLogger(__name__)

        try:
            logger.info(f"UserNotificationListView: Request from user {request.user.email}")

            # Get query parameters
            page = int(request.GET.get('page', 1))
            page_size = min(int(request.GET.get('page_size', 20)), 50)

            # Get user notifications
            from .models import Notification
            notifications = Notification.objects.filter(user=request.user)
            total_count = notifications.count()
            unread_count = notifications.filter(is_read=False).count()

            # Apply pagination
            start = (page - 1) * page_size
            end = start + page_size
            paginated_notifications = notifications[start:end]

            # Serialize data
            from .serializers import NotificationSerializer
            serializer = NotificationSerializer(paginated_notifications, many=True)

            logger.info(f"UserNotificationListView: Returning {len(serializer.data)} notifications")

            return Response({
                'success': True,
                'data': {
                    'notifications': serializer.data,
                    'unread_count': unread_count,
                    'pagination': {
                        'page': page,
                        'page_size': page_size,
                        'total': total_count,
                        'total_pages': (total_count + page_size - 1) // page_size
                    }
                }
            }, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error(f"UserNotificationListView: Unexpected error: {e}", exc_info=True)
            return Response({
                'success': False,
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class NotificationMarkReadView(APIView):
    """
    Mark notification as read
    """
    permission_classes = [IsAuthenticated]

    def post(self, request, notification_id):
        """Mark notification as read"""
        import logging
        logger = logging.getLogger(__name__)

        try:
            logger.info(f"NotificationMarkReadView: Marking notification {notification_id} as read for user {request.user.email}")

            # Get notification
            from .models import Notification
            notification = Notification.objects.get(
                id=notification_id,
                user=request.user
            )

            # Mark as read
            notification.mark_as_read()

            logger.info(f"NotificationMarkReadView: Notification {notification_id} marked as read")

            return Response({
                'success': True,
                'message': 'Notification marked as read'
            }, status=status.HTTP_200_OK)

        except Notification.DoesNotExist:
            logger.warning(f"NotificationMarkReadView: Notification {notification_id} not found for user {request.user.email}")
            return Response({
                'success': False,
                'error': 'Notification not found'
            }, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            logger.error(f"NotificationMarkReadView: Unexpected error: {e}", exc_info=True)
            return Response({
                'success': False,
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class NotificationMarkAllReadView(APIView):
    """
    Mark all notifications as read
    """
    permission_classes = [IsAuthenticated]

    def post(self, request):
        """Mark all notifications as read"""
        import logging
        logger = logging.getLogger(__name__)

        try:
            logger.info(f"NotificationMarkAllReadView: Marking all notifications as read for user {request.user.email}")

            # Mark all unread notifications as read
            from .models import Notification
            unread_notifications = Notification.objects.filter(
                user=request.user,
                is_read=False
            )

            count = unread_notifications.count()

            # Bulk update
            from django.utils import timezone
            unread_notifications.update(
                is_read=True,
                read_at=timezone.now()
            )

            logger.info(f"NotificationMarkAllReadView: Marked {count} notifications as read")

            return Response({
                'success': True,
                'message': f'Marked {count} notifications as read'
            }, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error(f"NotificationMarkAllReadView: Unexpected error: {e}", exc_info=True)
            return Response({
                'success': False,
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class UserMessageDetailView(APIView):
    """Get message details for the current user (only their own messages)"""
    permission_classes = [IsAuthenticated]

    def get(self, request, message_id):
        """Get detailed message information for user's own message"""
        import logging
        logger = logging.getLogger(__name__)

        try:
            logger.info(f"UserMessageDetailView: Request from user {request.user.email} for message {message_id}")

            # First, let's check if the message_id is a valid UUID
            try:
                import uuid
                uuid.UUID(str(message_id))
            except ValueError:
                logger.error(f"UserMessageDetailView: Invalid UUID format: {message_id}")
                return Response({
                    'success': False,
                    'error': 'Invalid message ID format'
                }, status=status.HTTP_400_BAD_REQUEST)

            # Get the message - only allow users to view their own messages
            try:
                message = Inquiry.objects.get(
                    id=message_id,
                    email=request.user.email  # Only allow viewing own messages
                )
                logger.info(f"UserMessageDetailView: Found message {message_id} for user {request.user.email}")
            except Inquiry.DoesNotExist:
                logger.warning(f"UserMessageDetailView: Message {message_id} not found or not owned by user {request.user.email}")
                return Response({
                    'success': False,
                    'error': 'Message not found or access denied'
                }, status=status.HTTP_404_NOT_FOUND)

            # Get all replies for this message
            try:
                replies = InquiryReply.objects.filter(
                    inquiry=message
                ).order_by('created_at')
                logger.info(f"UserMessageDetailView: Found {replies.count()} replies for message {message_id}")
            except Exception as replies_error:
                logger.error(f"UserMessageDetailView: Error fetching replies: {replies_error}")
                replies = []

            # Serialize the message data
            try:
                message_data = {
                    'id': str(message.id),
                    'name': getattr(message, 'name', None) or 'Anonymous User',
                    'email': message.email,
                    'subject': (message.description[:100] + '...' if message.description and len(message.description) > 100 else message.description) if message.description else 'No subject',
                    'message': message.description or 'No message content',
                    'status': getattr(message, 'status', 'pending'),
                    'created_at': message.created_at.isoformat(),
                    'updated_at': message.updated_at.isoformat(),
                }
                logger.info(f"UserMessageDetailView: Serialized message data successfully")
            except Exception as msg_error:
                logger.error(f"UserMessageDetailView: Error serializing message: {msg_error}")
                return Response({
                    'success': False,
                    'error': f'Error processing message data: {str(msg_error)}'
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

            # Serialize the replies data
            replies_data = []
            for reply in replies:
                try:
                    admin_user = getattr(reply, 'admin_user', None)
                    if admin_user:
                        admin_name = f"{admin_user.first_name} {admin_user.last_name}".strip()
                        if not admin_name:
                            admin_name = admin_user.email
                    else:
                        admin_name = 'Admin'

                    replies_data.append({
                        'id': str(reply.id),
                        'message': reply.message,
                        'admin_name': admin_name,
                        'created_at': reply.created_at.isoformat(),
                    })
                except Exception as reply_error:
                    logger.error(f"UserMessageDetailView: Error serializing reply {reply.id}: {reply_error}")
                    # Skip this reply if there's an error
                    continue

            logger.info(f"UserMessageDetailView: Successfully retrieved message {message_id} with {len(replies_data)} replies")

            return Response({
                'success': True,
                'data': {
                    'message': message_data,
                    'replies': replies_data
                }
            })

        except Exception as e:
            logger.error(f"UserMessageDetailView: Unexpected error: {e}", exc_info=True)
            return Response({
                'success': False,
                'error': f'Internal server error: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class TestNotificationView(APIView):
    """Test notification creation for debugging"""
    permission_classes = [IsAuthenticated]

    def post(self, request):
        """Create a test notification"""
        import logging
        logger = logging.getLogger(__name__)

        try:
            logger.info(f"TestNotificationView: Request from user {request.user.email}")

            # Get user's latest audio analysis
            try:
                audio_analysis = AudioAnalysis.objects.filter(user=request.user).latest('upload_time')
                logger.info(f"TestNotificationView: Found audio analysis {audio_analysis.id}")
            except AudioAnalysis.DoesNotExist:
                return Response({
                    'success': False,
                    'error': 'No audio analysis found for user'
                }, status=status.HTTP_404_NOT_FOUND)

            # Test direct notification creation
            try:
                notification = Notification.create_audio_complete_notification(
                    user=request.user,
                    audio_analysis=audio_analysis
                )
                logger.info(f"TestNotificationView: Direct notification created {notification.id}")

                # Also test Celery task
                from api.tasks import test_notification_creation
                task = test_notification_creation.delay(
                    str(request.user.id),
                    str(audio_analysis.id)
                )
                logger.info(f"TestNotificationView: Celery test task started {task.id}")

                return Response({
                    'success': True,
                    'data': {
                        'direct_notification_id': str(notification.id),
                        'celery_task_id': task.id,
                        'audio_analysis_id': str(audio_analysis.id),
                        'message': 'Test notifications created successfully'
                    }
                })

            except Exception as create_error:
                logger.error(f"TestNotificationView: Error creating notification: {create_error}")
                return Response({
                    'success': False,
                    'error': f'Failed to create notification: {str(create_error)}'
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        except Exception as e:
            logger.error(f"TestNotificationView: Unexpected error: {e}", exc_info=True)
            return Response({
                'success': False,
                'error': f'Internal server error: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class CreateAudioNotificationView(APIView):
    """Create audio notification directly for testing"""
    permission_classes = [IsAuthenticated]

    def post(self, request):
        """Create audio notification for testing"""
        import logging
        logger = logging.getLogger(__name__)

        try:
            logger.info(f"CreateAudioNotificationView: Request from user {request.user.email}")

            # Get notification type from request
            notification_type = request.data.get('type', 'audio_complete')

            # Get user's latest audio analysis or create a dummy one
            try:
                audio_analysis = AudioAnalysis.objects.filter(user=request.user).latest('upload_time')
                logger.info(f"CreateAudioNotificationView: Found audio analysis {audio_analysis.id}")
            except AudioAnalysis.DoesNotExist:
                # Create a dummy audio analysis for testing
                audio_analysis = AudioAnalysis.objects.create(
                    user=request.user,
                    filename="test_audio.wav",
                    status="completed"
                )
                logger.info(f"CreateAudioNotificationView: Created dummy audio analysis {audio_analysis.id}")

            # Create notification based on type
            try:
                if notification_type == 'audio_complete':
                    notification = Notification.create_audio_complete_notification(
                        user=request.user,
                        audio_analysis=audio_analysis
                    )
                    logger.info(f"CreateAudioNotificationView: Audio complete notification created {notification.id}")
                elif notification_type == 'audio_failed':
                    notification = Notification.create_audio_failed_notification(
                        user=request.user,
                        audio_analysis=audio_analysis,
                        error_message="Test error message"
                    )
                    logger.info(f"CreateAudioNotificationView: Audio failed notification created {notification.id}")
                else:
                    return Response({
                        'success': False,
                        'error': 'Invalid notification type. Use "audio_complete" or "audio_failed"'
                    }, status=status.HTTP_400_BAD_REQUEST)

                # Serialize the notification
                serializer = NotificationSerializer(notification)

                return Response({
                    'success': True,
                    'data': {
                        'notification': serializer.data,
                        'audio_analysis_id': str(audio_analysis.id),
                        'message': f'{notification_type} notification created successfully'
                    }
                })

            except Exception as create_error:
                logger.error(f"CreateAudioNotificationView: Error creating notification: {create_error}")
                import traceback
                logger.error(f"CreateAudioNotificationView: Error details: {traceback.format_exc()}")
                return Response({
                    'success': False,
                    'error': f'Failed to create notification: {str(create_error)}'
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        except Exception as e:
            logger.error(f"CreateAudioNotificationView: Unexpected error: {e}", exc_info=True)
            return Response({
                'success': False,
                'error': f'Internal server error: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class TimezoneDetectionView(APIView):
    """Detect timezone based on IP address"""
    permission_classes = []  # Allow anonymous access

    def get(self, request):
        """Get timezone information based on IP address"""
        import logging
        import requests
        import json
        from django.utils import timezone

        logger = logging.getLogger(__name__)

        try:
            # Get client IP address
            x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
            if x_forwarded_for:
                ip = x_forwarded_for.split(',')[0].strip()
            else:
                ip = request.META.get('REMOTE_ADDR', '127.0.0.1')

            logger.info(f"TimezoneDetectionView: Detecting timezone for IP {ip}")

            # Default timezone info for US Eastern
            default_timezone_info = {
                'timezone': 'America/New_York',
                'country': 'US',
                'region': 'New York',
                'city': 'New York',
                'offset': '-05:00',
                'dst': True
            }

            # If localhost or private IP, return default US Eastern timezone
            if ip in ['127.0.0.1', 'localhost'] or ip.startswith('192.168.') or ip.startswith('10.') or ip.startswith('172.'):
                logger.info(f"TimezoneDetectionView: Using default timezone for local/private IP")
                return Response({
                    'success': True,
                    'data': {
                        'ip': ip,
                        'timezone_info': default_timezone_info,
                        'current_time': timezone.now().isoformat(),
                        'source': 'default'
                    }
                })

            # Try to get timezone from IP geolocation service
            try:
                # Using ipapi.co service (free tier)
                response = requests.get(f'https://ipapi.co/{ip}/json/', timeout=5)
                if response.status_code == 200:
                    data = response.json()

                    if 'timezone' in data and data['timezone']:
                        timezone_info = {
                            'timezone': data.get('timezone', 'America/New_York'),
                            'country': data.get('country_code', 'US'),
                            'region': data.get('region', 'Unknown'),
                            'city': data.get('city', 'Unknown'),
                            'offset': data.get('utc_offset', '-05:00'),
                            'dst': data.get('dst', False)
                        }

                        logger.info(f"TimezoneDetectionView: Detected timezone {timezone_info['timezone']} for IP {ip}")

                        return Response({
                            'success': True,
                            'data': {
                                'ip': ip,
                                'timezone_info': timezone_info,
                                'current_time': timezone.now().isoformat(),
                                'source': 'geolocation'
                            }
                        })

            except requests.RequestException as e:
                logger.warning(f"TimezoneDetectionView: Geolocation service error: {e}")

            # Fallback to default US Eastern timezone
            logger.info(f"TimezoneDetectionView: Using fallback timezone for IP {ip}")
            return Response({
                'success': True,
                'data': {
                    'ip': ip,
                    'timezone_info': default_timezone_info,
                    'current_time': timezone.now().isoformat(),
                    'source': 'fallback'
                }
            })

        except Exception as e:
            logger.error(f"TimezoneDetectionView: Unexpected error: {e}", exc_info=True)
            return Response({
                'success': False,
                'error': f'Failed to detect timezone: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class MessageBoardListView(APIView):
    """
    List all message board messages (public access)
    """
    permission_classes = []  # Allow anonymous access

    def get(self, request):
        """Get all active message board messages"""
        import logging
        logger = logging.getLogger(__name__)

        try:
            logger.info("MessageBoardListView: Getting message board messages")

            # Get query parameters
            page = int(request.GET.get('page', 1))
            page_size = min(int(request.GET.get('page_size', 10)), 50)
            search = request.GET.get('search', '').strip()

            # Get all active messages
            messages = MessageBoardMessage.objects.filter(is_active=True)

            # Apply search filter if provided
            if search:
                from django.db.models import Q
                messages = messages.filter(
                    Q(title__icontains=search) |
                    Q(content__icontains=search) |
                    Q(anonymous_name__icontains=search)
                )

            total_count = messages.count()

            # Apply pagination
            start = (page - 1) * page_size
            end = start + page_size
            paginated_messages = messages[start:end]

            # Serialize data
            serializer = MessageBoardMessageListSerializer(paginated_messages, many=True, context={'request': request})

            logger.info(f"MessageBoardListView: Returning {len(serializer.data)} messages")

            return Response({
                'success': True,
                'data': {
                    'messages': serializer.data,
                    'pagination': {
                        'page': page,
                        'page_size': page_size,
                        'total': total_count,
                        'total_pages': (total_count + page_size - 1) // page_size
                    }
                }
            }, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error(f"MessageBoardListView: Unexpected error: {e}", exc_info=True)
            return Response({
                'success': False,
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def post(self, request):
        """Create a new message board message"""
        import logging
        logger = logging.getLogger(__name__)

        try:
            logger.info("MessageBoardListView: Creating new message")

            # Validate and create message
            serializer = MessageBoardMessageSerializer(data=request.data, context={'request': request})
            if serializer.is_valid():
                message = serializer.save()
                logger.info(f"MessageBoardListView: Created message {message.id}")

                # Return the created message with full details
                response_serializer = MessageBoardMessageSerializer(message, context={'request': request})
                return Response({
                    'success': True,
                    'message': 'Message posted successfully',
                    'data': response_serializer.data
                }, status=status.HTTP_201_CREATED)
            else:
                logger.warning(f"MessageBoardListView: Validation errors: {serializer.errors}")
                return Response({
                    'success': False,
                    'errors': serializer.errors
                }, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            logger.error(f"MessageBoardListView: Unexpected error: {e}", exc_info=True)
            return Response({
                'success': False,
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class MessageBoardDetailView(APIView):
    """
    Get message board message details with replies (public access)
    """
    permission_classes = []  # Allow anonymous access

    def get(self, request, message_id):
        """Get message details with all replies"""
        import logging
        logger = logging.getLogger(__name__)

        try:
            logger.info(f"MessageBoardDetailView: Getting message {message_id}")

            # Get message
            try:
                message = MessageBoardMessage.objects.get(id=message_id, is_active=True)
            except MessageBoardMessage.DoesNotExist:
                return Response({
                    'success': False,
                    'error': 'Message not found'
                }, status=status.HTTP_404_NOT_FOUND)

            # Serialize message with replies
            serializer = MessageBoardMessageSerializer(message, context={'request': request})

            logger.info(f"MessageBoardDetailView: Returning message {message_id} with {message.get_replies_count()} replies")

            return Response({
                'success': True,
                'data': serializer.data
            }, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error(f"MessageBoardDetailView: Unexpected error: {e}", exc_info=True)
            return Response({
                'success': False,
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class MessageBoardReplyView(APIView):
    """
    Create a reply to a message board message (requires authentication)
    """
    permission_classes = [IsAuthenticated]

    def post(self, request, message_id):
        """Create a reply to a message"""
        import logging
        logger = logging.getLogger(__name__)

        try:
            logger.info(f"MessageBoardReplyView: Creating reply to message {message_id} by user {request.user.email}")

            # Get message
            try:
                message = MessageBoardMessage.objects.get(id=message_id, is_active=True)
            except MessageBoardMessage.DoesNotExist:
                return Response({
                    'success': False,
                    'error': 'Message not found'
                }, status=status.HTTP_404_NOT_FOUND)

            # Create reply
            serializer = MessageBoardReplySerializer(data=request.data, context={'request': request})
            if serializer.is_valid():
                reply = serializer.save(message=message, user=request.user)
                logger.info(f"MessageBoardReplyView: Created reply {reply.id}")

                return Response({
                    'success': True,
                    'message': 'Reply posted successfully',
                    'data': serializer.data
                }, status=status.HTTP_201_CREATED)
            else:
                logger.warning(f"MessageBoardReplyView: Validation errors: {serializer.errors}")
                return Response({
                    'success': False,
                    'errors': serializer.errors
                }, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            logger.error(f"MessageBoardReplyView: Unexpected error: {e}", exc_info=True)
            return Response({
                'success': False,
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
