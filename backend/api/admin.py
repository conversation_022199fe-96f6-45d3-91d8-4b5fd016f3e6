from django.contrib import admin
from django.contrib.auth.admin import UserAdmin
from django.utils.html import format_html
from .models import CustomUser, EmailVerificationToken, PasswordResetToken, AudioAnalysis, Donation, MessageBoardMessage, MessageBoardReply


@admin.register(CustomUser)
class CustomUserAdmin(UserAdmin):
    """
    自定义用户管理界面
    """
    list_display = ['email', 'display_name', 'is_active', 'is_staff', 'date_joined']
    list_filter = ['is_active', 'is_staff', 'is_superuser', 'date_joined']
    search_fields = ['email', 'first_name', 'last_name']
    ordering = ['-date_joined']

    fieldsets = (
        (None, {'fields': ('email', 'password')}),
        ('个人信息', {'fields': ('first_name', 'last_name', 'avatar')}),
        ('权限', {'fields': ('is_active', 'is_staff', 'is_superuser', 'groups', 'user_permissions')}),
        ('重要日期', {'fields': ('last_login', 'date_joined')}),
    )

    add_fieldsets = (
        (None, {
            'classes': ('wide',),
            'fields': ('email', 'password1', 'password2'),
        }),
    )

    readonly_fields = ['date_joined', 'last_login']


@admin.register(EmailVerificationToken)
class EmailVerificationTokenAdmin(admin.ModelAdmin):
    """
    邮箱验证令牌管理界面
    """
    list_display = ['user_email', 'created_at', 'expires_at', 'is_used', 'is_expired_display']
    list_filter = ['is_used', 'created_at', 'expires_at']
    search_fields = ['user__email', 'token']
    readonly_fields = ['token', 'created_at', 'expires_at']

    def user_email(self, obj):
        return obj.user.email
    user_email.short_description = '用户邮箱'

    def is_expired_display(self, obj):
        if obj.is_expired():
            return format_html('<span style="color: red;">已过期</span>')
        return format_html('<span style="color: green;">有效</span>')
    is_expired_display.short_description = '状态'


@admin.register(PasswordResetToken)
class PasswordResetTokenAdmin(admin.ModelAdmin):
    """
    密码重置令牌管理界面
    """
    list_display = ['user_email', 'created_at', 'expires_at', 'is_used', 'is_expired_display']
    list_filter = ['is_used', 'created_at', 'expires_at']
    search_fields = ['user__email', 'token']
    readonly_fields = ['token', 'created_at', 'expires_at']

    def user_email(self, obj):
        return obj.user.email
    user_email.short_description = '用户邮箱'

    def is_expired_display(self, obj):
        if obj.is_expired():
            return format_html('<span style="color: red;">已过期</span>')
        return format_html('<span style="color: green;">有效</span>')
    is_expired_display.short_description = '状态'


@admin.register(AudioAnalysis)
class AudioAnalysisAdmin(admin.ModelAdmin):
    """
    音频分析管理界面
    """
    list_display = ['user_email', 'filename', 'status', 'upload_time', 'relationship', 'age']
    list_filter = ['status', 'upload_time', 'relationship']
    search_fields = ['user__email', 'filename']
    readonly_fields = ['id', 'upload_time']

    fieldsets = (
        ('基本信息', {
            'fields': ('id', 'user', 'filename', 'audio_file', 'upload_time')
        }),
        ('分析结果', {
            'fields': ('status', 'result')
        }),
        ('说话人信息', {
            'fields': ('relationship', 'occupation', 'age', 'date_of_birth')
        }),
    )

    def user_email(self, obj):
        return obj.user.email
    user_email.short_description = '用户邮箱'


@admin.register(Donation)
class DonationAdmin(admin.ModelAdmin):
    """
    捐赠管理界面
    """
    list_display = ['user_email', 'amount', 'status', 'created_at', 'audio_analysis_filename']
    list_filter = ['status', 'amount', 'created_at']
    search_fields = ['user__email', 'audio_analysis__filename']
    readonly_fields = ['id', 'created_at', 'updated_at', 'stripe_payment_intent_id', 'stripe_charge_id']

    fieldsets = (
        ('基本信息', {
            'fields': ('id', 'user', 'audio_analysis', 'amount', 'status')
        }),
        ('支付信息', {
            'fields': ('stripe_payment_intent_id', 'stripe_charge_id')
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at', 'completed_at')
        }),
    )

    def user_email(self, obj):
        return obj.user.email
    user_email.short_description = '用户邮箱'

    def audio_analysis_filename(self, obj):
        return obj.audio_analysis.filename
    audio_analysis_filename.short_description = '音频文件名'


@admin.register(MessageBoardMessage)
class MessageBoardMessageAdmin(admin.ModelAdmin):
    """
    Message Board Message Admin
    """
    list_display = ['title', 'get_author_name', 'is_anonymous', 'created_at', 'is_active', 'get_replies_count']
    list_filter = ['is_anonymous', 'is_active', 'created_at']
    search_fields = ['title', 'content', 'anonymous_name', 'user__email']
    readonly_fields = ['id', 'created_at', 'updated_at']
    list_per_page = 20

    fieldsets = (
        ('Message Information', {
            'fields': ('id', 'title', 'content', 'image')
        }),
        ('Author Information', {
            'fields': ('user', 'is_anonymous', 'anonymous_name', 'anonymous_email')
        }),
        ('Status & Timestamps', {
            'fields': ('is_active', 'created_at', 'updated_at')
        }),
    )

    def get_replies_count(self, obj):
        return obj.get_replies_count()
    get_replies_count.short_description = 'Replies'


@admin.register(MessageBoardReply)
class MessageBoardReplyAdmin(admin.ModelAdmin):
    """
    Message Board Reply Admin
    """
    list_display = ['message', 'get_author_name', 'created_at', 'is_active']
    list_filter = ['is_active', 'created_at']
    search_fields = ['content', 'user__email', 'message__title']
    readonly_fields = ['id', 'created_at', 'updated_at']
    list_per_page = 20

    fieldsets = (
        ('Reply Information', {
            'fields': ('id', 'message', 'content', 'image')
        }),
        ('Author Information', {
            'fields': ('user',)
        }),
        ('Status & Timestamps', {
            'fields': ('is_active', 'created_at', 'updated_at')
        }),
    )

    def get_author_name(self, obj):
        return obj.get_author_name()
    get_author_name.short_description = 'Author'