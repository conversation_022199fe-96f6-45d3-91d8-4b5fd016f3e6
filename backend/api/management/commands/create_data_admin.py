from django.core.management.base import BaseCommand
from django.contrib.auth.models import Group, Permission
from api.models import CustomUser

class Command(BaseCommand):
    help = 'Create a data admin user for dashboard access'

    def add_arguments(self, parser):
        parser.add_argument('--email', type=str, required=True, help='Admin email address')
        parser.add_argument('--password', type=str, required=True, help='Admin password')
        parser.add_argument('--first-name', type=str, default='Data', help='First name')
        parser.add_argument('--last-name', type=str, default='Admin', help='Last name')

    def handle(self, *args, **options):
        email = options['email']
        password = options['password']
        first_name = options['first_name']
        last_name = options['last_name']

        # Check if user already exists
        if CustomUser.objects.filter(email=email).exists():
            self.stdout.write(
                self.style.ERROR(f'User with email {email} already exists')
            )
            return

        try:
            # Create user
            user = CustomUser.objects.create_user(
                email=email,
                password=password,
                first_name=first_name,
                last_name=last_name
            )
            user.is_staff = True
            user.is_active = True
            user.save()

            # Create or get Data Analysts group
            data_group, created = Group.objects.get_or_create(name='Data Analysts')
            if created:
                self.stdout.write(
                    self.style.SUCCESS('Created Data Analysts group')
                )

            # Add user to group
            user.groups.add(data_group)

            self.stdout.write(
                self.style.SUCCESS(f'Successfully created data admin user: {email}')
            )
            self.stdout.write(f'Email: {email}')
            self.stdout.write(f'Password: {password}')
            self.stdout.write(f'Dashboard URL: http://192.168.50.180:8000/dashboard/')
            self.stdout.write(
                self.style.WARNING('Please change the password after first login')
            )

        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Failed to create user: {str(e)}')
            )
