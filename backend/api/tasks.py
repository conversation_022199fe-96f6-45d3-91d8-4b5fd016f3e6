import json
from celery import shared_task
from mmse_prediction.pipeline_functions.pipeline import mmse_predict_pipeline
from mmse_prediction.pipeline_functions.other_functions import NpEncoder
from celery.signals import worker_process_init
pipe = None

@worker_process_init.connect
def init_pipe(**kwargs):
    global pipe
    pipe = mmse_predict_pipeline()

@shared_task
def mmse_predict(audioanalysis_id, file_path):
    # Ensure Django is properly set up
    import os
    import django
    if not django.apps.apps.ready:
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
        django.setup()
    print(f"🚀 开始MMSE预测任务:")
    print(f"   任务ID: {audioanalysis_id} (type: {type(audioanalysis_id)})")
    print(f"   音频路径: {file_path}")
    print(f"   Django apps ready: {django.apps.apps.ready}")

    # 更新状态为处理中
    try:
        from django.apps import apps
        AudioAnalysis = apps.get_model('api', 'AudioAnalysis')

        instance = AudioAnalysis.objects.get(id=audioanalysis_id)
        instance.status = "processing"
        instance.save()
        print(f"✅ 状态已更新为处理中")
    except Exception as e:
        print(f"❌ 更新状态失败: {str(e)}")
        return

    global pipe
    print(f"⏳ 开始MMSE预测分析...")

    dict_ = pipe.predict(file_path)
    print(f"📊 MMSE预测完成")

    # dict_ = {}
    # dict_['Predicted mmse score'] = 27.0
    # dict_['Model performance'] = {'RMSE': 3.79, 'Pearson correlation coefficient': 0.83}
    # dict_['Transcribed'] = 'test Transcribed'
    # dict_['Model'] = 'HiSage-MMScore-en-1.0'

    # Write back to database
    try:
        from django.apps import apps
        AudioAnalysis = apps.get_model('api', 'AudioAnalysis')
        Notification = apps.get_model('api', 'Notification')

        instance = AudioAnalysis.objects.get(id=audioanalysis_id)
        instance.result = json.dumps(dict_, cls=NpEncoder)
        instance.status = "completed"
        instance.save()
        print(f"✅ 音频分析完成: {audioanalysis_id}")
        print(f"📋 分析结果已保存到数据库")

        # Create notification for successful analysis
        try:
            print(f"🔔 开始创建完成通知...")
            notification = Notification.create_audio_complete_notification(
                user=instance.user,
                audio_analysis=instance
            )
            print(f"🔔 通知已创建: 音频分析完成 - ID: {notification.id}")
        except Exception as notification_error:
            print(f"⚠️ 创建通知失败: {str(notification_error)}")
            import traceback
            print(f"⚠️ 通知错误详情: {traceback.format_exc()}")

    except Exception as e:
        print(f"❌ 音频分析失败: {audioanalysis_id}, 错误: {str(e)}")
        try:
            from django.apps import apps
            AudioAnalysis = apps.get_model('api', 'AudioAnalysis')
            Notification = apps.get_model('api', 'Notification')

            instance = AudioAnalysis.objects.get(id=audioanalysis_id)
            instance.status = "failed"
            instance.save()
            print(f"💾 状态已更新为失败")

            # Create notification for failed analysis
            try:
                print(f"🔔 开始创建失败通知...")
                notification = Notification.create_audio_failed_notification(
                    user=instance.user,
                    audio_analysis=instance,
                    error_message=str(e)
                )
                print(f"🔔 通知已创建: 音频分析失败 - ID: {notification.id}")
            except Exception as notification_error:
                print(f"⚠️ 创建失败通知失败: {str(notification_error)}")
                import traceback
                print(f"⚠️ 失败通知错误详情: {traceback.format_exc()}")

        except Exception as save_error:
            print(f"❌ 保存失败状态时出错: {str(save_error)}")


@shared_task
def test_notification_creation(user_id, audio_analysis_id):
    """Test task to verify notification creation works"""
    import os
    import django
    if not django.apps.apps.ready:
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
        django.setup()

    try:
        from django.apps import apps
        CustomUser = apps.get_model('api', 'CustomUser')
        AudioAnalysis = apps.get_model('api', 'AudioAnalysis')
        Notification = apps.get_model('api', 'Notification')

        print(f"🧪 测试通知创建...")
        print(f"   用户ID: {user_id}")
        print(f"   音频分析ID: {audio_analysis_id}")

        user = CustomUser.objects.get(id=user_id)
        audio_analysis = AudioAnalysis.objects.get(id=audio_analysis_id)

        print(f"🧪 找到用户: {user.email}")
        print(f"🧪 找到音频分析: {audio_analysis.filename}")

        # Create test notification
        notification = Notification.create_audio_complete_notification(
            user=user,
            audio_analysis=audio_analysis
        )

        print(f"🧪 ✅ 测试通知创建成功!")
        print(f"🧪    通知ID: {notification.id}")
        print(f"🧪    通知标题: {notification.title}")
        print(f"🧪    通知消息: {notification.message}")

        return {
            'success': True,
            'notification_id': str(notification.id),
            'message': 'Test notification created successfully'
        }

    except Exception as e:
        print(f"🧪 ❌ 测试通知创建失败: {str(e)}")
        import traceback
        print(f"🧪 错误详情: {traceback.format_exc()}")
        return {
            'success': False,
            'error': str(e)
        }