///////////////////////////////////////////////////////////////////////////////////////
///////// > openSMILE configuration file for speech prosody features //////////////////
/////////   pitch (ACF) and intensity                                //////////////////
/////////                                                            //////////////////
///////// (c) 2013-2016 audEERING.                                   //////////////////
/////////     All rights reserved. See file COPYING for details.    //////////////////
///////////////////////////////////////////////////////////////////////////////////////


;;;;;;; component list ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;

[componentInstances:cComponentManager]
instance[dataMemory].type=cDataMemory

\{../shared/standard_wave_input.conf.inc}

[componentInstances:cComponentManager]
instance[frame].type=cFramer
instance[int].type=cIntensity
instance[win].type=cWindower
instance[fft].type=cTransformFFT
instance[fftmp].type=cFFTmagphase
instance[acf].type=cAcf
instance[cep].type=cAcf
instance[pitch].type=cPitchACF
instance[smo].type=cContourSmoother
printLevelStats=0

;;;;;;;;;;;;;;;;;;;;;;;;;;;; main section ;;;;;;;;;;;;;;;;;;;;;;;;;;;

[frame:cFramer]
reader.dmLevel=wave
writer.dmLevel=outp
frameSize = 0.050
frameStep = 0.010
frameCenterSpecial = left

[int:cIntensity]
reader.dmLevel = outp
writer.dmLevel = intens
// nameAppend =
copyInputName = 1
processArrayFields = 1
intensity = 0
loudness = 1

[win:cWindower]
reader.dmLevel=outp
writer.dmLevel=win
winFunc=gauss
gain=1.0
sigma=0.4

[fft:cTransformFFT]
reader.dmLevel=win
writer.dmLevel=fftc
 ; for compatibility with 2.2.0 and older versions
zeroPadSymmetric = 0

[fftmp:cFFTmagphase]
reader.dmLevel=fftc
writer.dmLevel=fftmag

[acf:cAcf]
reader.dmLevel=fftmag
writer.dmLevel=acf

[cep:cAcf]
reader.dmLevel=fftmag
writer.dmLevel=cepstrum
cepstrum=1

[pitch:cPitchACF]
reader.dmLevel = acf;cepstrum
writer.dmLevel = pitch
// nameAppend =
copyInputName = 1
processArrayFields = 0
maxPitch = 500
voiceProb = 1
voiceQual = 0
HNR = 0
F0 = 1
F0raw = 0
F0env = 0
voicingCutoff = 0.550000

[smo:cContourSmoother]
reader.dmLevel = pitch;intens
writer.dmLevel = lld
nameAppend = sma
copyInputName = 1
noPostEOIprocessing = 0
smaWin = 3

\{../shared/standard_data_output_lldonly.conf.inc}

